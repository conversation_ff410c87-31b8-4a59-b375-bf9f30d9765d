# Guia de Customização da Interface de Usuário ASP.NET Core (MVC / Razor Pages)

Este documento explica como sobrescrever a interface de usuário de um [módulo de aplicação](../../../modules) ou [tema](theming.md) dependente para aplicações ASP.NET Core MVC / Razor Page.

## Sobrescrevendo uma Página

Esta seção aborda o desenvolvimento de [Razor Pages](https://docs.microsoft.com/en-us/aspnet/core/razor-pages/), que é a abordagem recomendada para criar uma interface de usuário renderizada no servidor para ASP.NET Core. Módulos pré-construídos normalmente usam a abordagem Razor Pages em vez do padrão MVC clássico (as próximas seções também cobrirão o padrão MVC).

Você normalmente tem três tipos de requisitos de sobrescrita para uma página:

* Sobrescrevendo **apenas o Page Model** (C#) para realizar lógica adicional sem alterar a interface do usuário da página.
* Sobrescrevendo **apenas a Razor Page** (arquivo .cshtml) para alterar a interface do usuário sem alterar o c# por trás da página.
* **Sobrescrevendo completamente** a página.

### Sobrescrevendo um Page Model (C#)

````csharp
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Identity;
using Volo.Abp.Identity.Web.Pages.Identity.Users;

namespace Acme.BookStore.Web.Pages.Identity.Users
{
    [Dependency(ReplaceServices = true)]
    [ExposeServices(typeof(EditModalModel))]
    public class MyEditModalModel : EditModalModel
    {
        public MyEditModalModel(
            IIdentityUserAppService identityUserAppService,
            IIdentityRoleAppService identityRoleAppService
            ) : base(
                identityUserAppService,
                identityRoleAppService)
        {
        }

        public async override Task<IActionResult> OnPostAsync()
        {
            //TODO: Lógica adicional
            await base.OnPostAsync();
            //TODO: Lógica adicional
        }
    }
}
````

* Esta classe herda e substitui o `EditModalModel` para os usuários e sobrescreve o método `OnPostAsync` para realizar lógica adicional antes e depois do código subjacente.
* Ele usa os atributos `ExposeServices` e `Dependency` para substituir a classe.

### Sobrescrevendo uma Razor Page (.CSHTML)

Sobrescrever um arquivo `.cshtml` (razor page, razor view, view component... etc.) é possível criando o mesmo arquivo `.cshtml` no mesmo caminho.

#### Exemplo

Este exemplo sobrescreve a interface do usuário da **página de login** definida pelo [Módulo de Conta](../../../modules/account.md).

O módulo de conta define um arquivo `Login.cshtml` na pasta `Pages/Account`. Portanto, você pode sobrescrevê-lo criando um arquivo no mesmo caminho:

![overriding-login-cshtml](/ABP-Docs/images/overriding-login-cshtml.png)

Você normalmente quer copiar o arquivo `.cshtml` original do módulo e, em seguida, fazer as alterações necessárias. Você pode encontrar o arquivo original [aqui](https://github.com/abpframework/abp/blob/dev/modules/account/src/Volo.Abp.Account.Web/Pages/Account/Login.cshtml). Não copie o arquivo `Login.cshtml.cs`, que é o arquivo de código por trás da razor page, e ainda não queremos sobrescrevê-lo (veja a próxima seção).

> Não se esqueça de adicionar o [_ViewImports.cshtml](https://learn.microsoft.com/en-us/aspnet/core/mvc/views/layout?view=aspnetcore-7.0#importing-shared-directives) se a página que você deseja sobrescrever contiver [ABP Tag Helpers](tag-helpers).

````csharp
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@addTagHelper *, Volo.Abp.AspNetCore.Mvc.UI
@addTagHelper *, Volo.Abp.AspNetCore.Mvc.UI.Bootstrap
@addTagHelper *, Volo.Abp.AspNetCore.Mvc.UI.Bundling
````

É isso, você pode alterar o conteúdo do arquivo como quiser.

### Sobrescrevendo Completamente uma Razor Page

Você pode querer sobrescrever completamente uma página; o razor e o arquivo c# relacionados à página.

Nesse caso;

1. Sobrescreva a classe C# do page model da mesma forma como descrito acima, mas não substitua a classe de page model existente.
2. Sobrescreva a Razor Page como acabou de ser descrito acima, mas também altere a diretiva @model para apontar para o seu novo page model.

#### Exemplo

Este exemplo sobrescreve a **página de login** definida pelo [Módulo de Conta](../../../modules/account.md).

Crie uma classe de page model derivada de `LoginModel` (definida no namespace `Volo.Abp.Account.Web.Pages.Account`):

````csharp
public class MyLoginModel : LoginModel
{
    public MyLoginModel(
        IAuthenticationSchemeProvider schemeProvider,
        IOptions<AbpAccountOptions> accountOptions
        ) : base(
        schemeProvider,
        accountOptions)
    {

    }

    public override Task<IActionResult> OnPostAsync(string action)
    {
        //TODO: Adicionar lógica
        return base.OnPostAsync(action);
    }

    //TODO: Adicionar novos métodos e propriedades...
}
````

Você pode sobrescrever qualquer método ou adicionar novas propriedades/métodos, se necessário.

> Observe que não usamos `[Dependency(ReplaceServices = true)]` ou `[ExposeServices(typeof(LoginModel))]`, pois não queremos substituir a classe existente na injeção de dependência, definimos uma nova.

Copie o arquivo `Login.cshtml` para sua solução como acabou de ser descrito acima. Altere a diretiva **@model** para apontar para o `MyLoginModel`:

````xml
@page
...
@model Acme.BookStore.Web.Pages.Account.MyLoginModel
...
````

É isso! Faça qualquer alteração na view e execute sua aplicação.

#### Substituindo o Page Model Sem Herança

Você não precisa herdar da classe original do page model (como feito no exemplo anterior). Em vez disso, você pode **reimplementar** completamente a página por conta própria. Nesse caso, basta derivar de `PageModel`, `AbpPageModel` ou qualquer classe base adequada que você precise.

## Sobrescrevendo um View Component

O ABP, temas e módulos pré-construídos definem alguns **view components reutilizáveis**. Esses view components podem ser substituídos da mesma forma que uma página descrita acima.

### Exemplo

A captura de tela abaixo foi tirada do [Tema Básico](basic-theme.md) que vem com o template de inicialização da aplicação.

![bookstore-brand-area-highlighted](/ABP-Docs/images/bookstore-brand-area-highlighted.png)

O [Tema Básico](basic-theme.md) define alguns view components para o layout. Por exemplo, a área destacada com o retângulo vermelho acima é chamada de **componente Brand**. Você provavelmente deseja customizar este componente adicionando seu **próprio logo da aplicação**. Vamos ver como fazer isso.

Primeiro, crie seu logo e coloque em uma pasta em sua aplicação web. Usamos o caminho `wwwroot/logos/bookstore-logo.png`. Em seguida, copie a view do componente Brand ([daqui](https://github.com/abpframework/abp/blob/dev/modules/basic-theme/src/Volo.Abp.AspNetCore.Mvc.UI.Theme.Basic/Themes/Basic/Components/Brand/Default.cshtml)) dos arquivos do tema básico na pasta `Themes/Basic/Components/Brand`. O resultado deve ser semelhante à imagem abaixo:

![bookstore-added-brand-files](/ABP-Docs/images/bookstore-added-brand-files.png)

Em seguida, altere o `Default.cshtml` como desejar. Um exemplo de conteúdo pode ser assim:

````xml
<a href="/">
    <img src="~/logos/bookstore-logo.png" width="250" height="60"/>
</a>
````

Agora, você pode executar a aplicação para ver o resultado:

![bookstore-added-logo](/ABP-Docs/images/bookstore-added-logo.png)

Se precisar, você também pode substituir [a classe c# por trás do código](https://github.com/abpframework/abp/blob/dev/modules/basic-theme/src/Volo.Abp.AspNetCore.Mvc.UI.Theme.Basic/Themes/Basic/Components/Brand/MainNavbarBrandViewComponent.cs) do componente usando apenas o sistema de injeção de dependência.

### Sobrescrevendo o Tema

Assim como explicado acima, você pode substituir qualquer componente, layout ou classe c# do tema utilizado. Consulte o [documento de theming](theming.md) para obter mais informações sobre o sistema de theming.

## Sobrescrevendo Recursos Estáticos

Sobrescrever um recurso estático incorporado (como arquivos JavaScript, Css ou imagem) de um módulo é muito fácil. Basta colocar um arquivo no mesmo caminho em sua solução e deixar o [Virtual File System](../../infrastructure/virtual-file-system.md) lidar com isso.

## Manipulando os Bundles

O sistema [Bundling & Minification](bundling-minification.md) fornece um sistema **extensível e dinâmico** para criar bundles de **scripts** e **estilos**. Ele permite que você estenda e manipule os bundles existentes.

### Exemplo: Adicionando um Arquivo CSS Global

Por exemplo, o ABP define um **bundle de estilo global** que é adicionado a todas as páginas (na verdade, adicionado ao layout pelos temas). Vamos adicionar um **arquivo de estilo customizado** ao final dos arquivos do bundle, para que possamos sobrescrever qualquer estilo global.

Primeiro, crie um arquivo CSS e localize-o em uma pasta dentro de `wwwroot`:

![bookstore-global-css-file](/ABP-Docs/images/bookstore-global-css-file.png)

Defina algumas regras CSS customizadas dentro do arquivo. Exemplo:

````css
.card-title {
    color: orange;
    font-size: 2em;
    text-decoration: underline;
}

.btn-primary {
    background-color: red;
}
````

Em seguida, adicione este arquivo ao bundle de estilo global padrão no método `ConfigureServices` do seu [módulo](../../architecture/modularity/basics.md):

````csharp
Configure<AbpBundlingOptions>(options =>
{
    options.StyleBundles.Configure(
        StandardBundles.Styles.Global, //O nome do bundle!
        bundleConfiguration =>
        {
            bundleConfiguration.AddFiles("/styles/my-global-styles.css");
        }
    );
});
````

#### O Bundle de Script Global

Assim como o `StandardBundles.Styles.Global`, existe um `StandardBundles.Scripts.Global` que você pode adicionar arquivos ou manipular os existentes.

### Exemplo: Manipular os Arquivos do Bundle

O exemplo acima adiciona um novo arquivo ao bundle. Você pode fazer mais se criar uma classe de **contribuinte do bundle**. Exemplo:

````csharp
public class MyGlobalStyleBundleContributor : BundleContributor
{
    public override void ConfigureBundle(BundleConfigurationContext context)
    {
        context.Files.Clear();
        context.Files.Add("/styles/my-global-styles.css");
    }
}
````

Em seguida, você pode adicionar o contribuinte a um bundle existente:

````csharp
Configure<AbpBundlingOptions>(options =>
{
    options.StyleBundles.Configure(
        StandardBundles.Styles.Global,
        bundleConfiguration =>
        {
            bundleConfiguration.AddContributors(typeof(MyGlobalStyleBundleContributor));
        }
    );
});
````

Não é uma boa ideia limpar todos os arquivos CSS. Em um cenário real, você pode encontrar e substituir um arquivo específico por seu próprio arquivo.

### Exemplo: Adicionando um Arquivo JavaScript para uma Página Específica

Os exemplos acima funcionam com o bundle global adicionado ao layout. E se você quiser adicionar um arquivo CSS/JavaScript (ou substituir um arquivo) para uma página específica definida dentro de um módulo dependente?

Suponha que você deseja executar um **código JavaScript** quando o usuário entrar na página de **Gerenciamento de Funções** do Módulo de Identidade.

Primeiro, crie um arquivo JavaScript padrão na pasta `wwwroot`, `Pages` ou `Views` (o ABP suporta adicionar recursos estáticos dentro dessas pastas por padrão). Preferimos a pasta `Pages/Identity/Roles` para seguir as convenções:

![bookstore-added-role-js-file](/ABP-Docs/images/bookstore-added-role-js-file.png)

O conteúdo do arquivo é simples:

````js
$(function() {
    abp.log.info('Meu arquivo de script de função customizado foi carregado!');
});
````

Em seguida, adicione este arquivo ao bundle da página de gerenciamento de funções:

````csharp
Configure<AbpBundlingOptions>(options =>
{
    options.ScriptBundles
        .Configure(
            typeof(Volo.Abp.Identity.Web.Pages.Identity.Roles.IndexModel).FullName,
            bundleConfig =>
            {
                bundleConfig.AddFiles("/Pages/Identity/Roles/my-role-script.js");
            });
});
````

`typeof(Volo.Abp.Identity.Web.Pages.Identity.Roles.IndexModel).FullName` é a maneira segura de obter o nome do bundle para a página de gerenciamento de funções.

> Observe que nem todas as páginas definem esses bundles de página. Eles definem apenas se necessário.

Além de adicionar um novo arquivo CSS/JavaScript a uma página, você também pode substituir o existente (definindo um contribuinte de bundle).

## Customização de Layout

Os layouts são definidos pelo tema ([veja o theming](theming.md)) por design. Eles não estão incluídos em uma solução de aplicação baixada. Desta forma, você pode facilmente **atualizar** o tema e obter novos recursos. Você não pode **alterar diretamente** o código do layout em sua aplicação, a menos que o substitua por seu próprio layout (será explicado nas próximas seções).

Existem algumas maneiras comuns de **customizar o layout** descritas nas próximas seções.

### Menu Contributors

Existem dois **menus padrão** definidos pelo ABP:

![bookstore-menus-highlighted](/ABP-Docs/images/bookstore-menus-highlighted.png)

* `StandardMenus.Main`: O menu principal da aplicação.
* `StandardMenus.User`: O menu do usuário (geralmente no canto superior direito da tela).

A renderização dos menus é responsabilidade do tema, mas os **itens de menu** são determinados pelos módulos e pelo código da sua aplicação. Basta implementar a interface `IMenuContributor` e **manipular os itens de menu** no método `ConfigureMenuAsync`.

Os contribuidores de menu são executados sempre que for necessário renderizar o menu. Já existe um contribuidor de menu definido no **template de inicialização da aplicação**, então você pode tomá-lo como exemplo e melhorar, se necessário. Veja o documento [menu de navegação](navigation-menu.md) para mais informações.

### Toolbar Contributors

O sistema de [Toolbar](toolbars.md) é usado para definir **toolbars** na interface do usuário. Módulos (ou sua aplicação) podem adicionar **itens** a uma toolbar, então o tema renderiza a toolbar no **layout**.

Existe apenas uma **toolbar padrão** (chamada "Main" - definida como uma constante: `StandardToolbars.Main`). Para o tema básico, ela é renderizada como mostrado abaixo:
![bookstore-toolbar-highlighted](/ABP-Docs/images/bookstore-toolbar-highlighted.png)

Na captura de tela acima, existem dois itens adicionados à toolbar principal: componente de troca de idioma e menu do usuário. Você pode adicionar seus próprios itens aqui.

#### Exemplo: Adicionando um Ícone de Notificação

Neste exemplo, adicionaremos um **ícone de notificação (sino)** à esquerda do item de troca de idioma. Um item na toolbar deve ser um **view component**. Portanto, primeiro, crie um novo view component em seu projeto:

![bookstore-notification-view-component](/ABP-Docs/images/bookstore-notification-view-component.png)

**NotificationViewComponent.cs**

````csharp
public class NotificationViewComponent : AbpViewComponent
{
    public async Task<IViewComponentResult> InvokeAsync()
    {
        return View("/Pages/Shared/Components/Notification/Default.cshtml");
    }
}
````

**Default.cshtml**

````xml
<div id="MainNotificationIcon" style="color: white; margin: 8px;">
    <i class="far fa-bell"></i>
</div>
````

Agora, podemos criar uma classe que implementa a interface `IToolbarContributor`:

````csharp
public class MyToolbarContributor : IToolbarContributor
{
    public Task ConfigureToolbarAsync(IToolbarConfigurationContext context)
    {
        if (context.Toolbar.Name == StandardToolbars.Main)
        {
            context.Toolbar.Items
                .Insert(0, new ToolbarItem(typeof(NotificationViewComponent)));
        }

        return Task.CompletedTask;
    }
}
````

Esta classe adiciona o `NotificationViewComponent` como o primeiro item na toolbar `Main`.

Finalmente, você precisa adicionar este contribuinte ao `AbpToolbarOptions`, no `ConfigureServices` do seu módulo:

````csharp
Configure<AbpToolbarOptions>(options =>
{
    options.Contributors.Add(new MyToolbarContributor());
});
````

É isso, você verá o ícone de notificação na toolbar quando executar a aplicação:

![bookstore-notification-icon-on-toolbar](/ABP-Docs/images/bookstore-notification-icon-on-toolbar.png)

`NotificationViewComponent` neste exemplo simplesmente retorna uma view sem nenhum dado. Na vida real, você provavelmente deseja **consultar o banco de dados** (ou chamar uma API HTTP) para obter as notificações e passar para a view. Se precisar, você pode adicionar um arquivo `JavaScript` ou `CSS` ao bundle global (como descrito antes) para seu item de toolbar.

Veja o [documento de toolbars](toolbars.md) para mais informações sobre o sistema de toolbar.

### Layout Hooks

O sistema de [Layout Hooks](layout-hooks.md) permite que você **adicione código** em algumas partes específicas do layout. Todos os layouts de todos os temas devem implementar esses hooks. Então você pode adicionar um **view component** em um ponto de hook.

#### Exemplo: Adicionando Script do Google Analytics

Suponha que você precise adicionar o script do Google Analytics ao layout (que estará disponível para todas as páginas). Primeiro, **crie um view component** em seu projeto:

![bookstore-google-analytics-view-component](/ABP-Docs/images/bookstore-google-analytics-view-component.png)

**GoogleAnalyticsViewComponent.cs**

````csharp
public class GoogleAnalyticsViewComponent : AbpViewComponent
{
    public IViewComponentResult Invoke()
    {
        return View("/Pages/Shared/Components/GoogleAnalytics/Default.cshtml");
    }
}
````

**Default.cshtml**

````html
<script>
    (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
            (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
            m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
    })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

    ga('create', 'UA-xxxxxx-1', 'auto');
    ga('send', 'pageview');
</script>
````

Altere `UA-xxxxxx-1` com seu próprio código.

Em seguida, você pode adicionar este componente a qualquer um dos pontos de hook no `ConfigureServices` do seu módulo:

````csharp
Configure<AbpLayoutHookOptions>(options =>
{
    options.Add(
        LayoutHooks.Head.Last, //O nome do hook
        typeof(GoogleAnalyticsViewComponent) //O componente a ser adicionado
    );
});
````

Agora, o código do GA será inserido no `head` da página como o último item. Você (ou os módulos que está usando) pode adicionar vários itens ao mesmo hook. Todos eles serão adicionados ao layout.

A configuração acima adiciona o `GoogleAnalyticsViewComponent` a todos os layouts. Você pode querer adicionar apenas a um layout específico:

````csharp
Configure<AbpLayoutHookOptions>(options =>
{
    options.Add(
        LayoutHooks.Head.Last,
        typeof(GoogleAnalyticsViewComponent),
        layout: StandardLayouts.Application //Define o layout a ser adicionado
    );
});
````

Veja a seção de layouts abaixo para saber mais sobre o sistema de layout.

### Layouts

O sistema de layout permite que os temas definam layouts padrão, nomeados e permite que qualquer página selecione um layout adequado para sua finalidade. Existem três layouts pré-definidos:

* "**Application**": O layout principal (e padrão) para uma aplicação. Ele normalmente contém cabeçalho, menu (barra lateral), rodapé, toolbar... etc.
* "**Account**": Este layout é usado por login, registro e outras páginas semelhantes. Ele é usado para as páginas na pasta `/Pages/Account` por padrão.
* "**Empty**": Layout vazio e mínimo.

Esses nomes são definidos na classe `StandardLayouts` como constantes. Você pode definitivamente criar seus próprios layouts, mas esses são os nomes de layout padrão e implementados por todos os temas prontos para uso.

#### Localização do Layout

Você pode encontrar os arquivos de layout [aqui](https://github.com/abpframework/abp/blob/dev/modules/basic-theme/src/Volo.Abp.AspNetCore.Mvc.UI.Theme.Basic/Themes/Basic/Layouts) para o tema básico. Você pode tomá-los como referências para construir seus próprios layouts ou pode sobrescrevê-los, se necessário.

#### ITheme

O ABP usa o serviço `ITheme` para obter a localização do layout pelo nome do layout. Você pode substituir este serviço para selecionar dinamicamente a localização do layout.

#### IThemeManager

`IThemeManager` é usado para obter o tema atual e obter o caminho do layout. Qualquer página pode determinar o layout por conta própria. Exemplo:

````html
@using Volo.Abp.AspNetCore.Mvc.UI.Theming
@inject IThemeManager ThemeManager
@{
    Layout = ThemeManager.CurrentTheme.GetLayout(StandardLayouts.Empty);
}
````

Esta página usará o layout vazio. Você pode usar o método de extensão `ThemeManager.CurrentTheme.GetEmptyLayout();` como um atalho.

Se você quiser definir o layout para todas as páginas em uma pasta específica, escreva o código acima em um arquivo `_ViewStart.cshtml` nessa pasta.
