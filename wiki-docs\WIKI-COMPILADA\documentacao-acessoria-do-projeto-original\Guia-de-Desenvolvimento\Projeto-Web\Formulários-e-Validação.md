# ASP.NET Core MVC / Razor Pages: Formulários & Validação

O ABP oferece infraestrutura e convenções para facilitar a criação de formulários, localizar nomes de exibição para os elementos do formulário e lidar com a validação do lado do servidor e do cliente;

* A tag helper [abp-dynamic-form](tag-helpers/dynamic-forms.md) automatiza a **criação de um formulário completo** a partir de uma classe de modelo C#: Cria os elementos de entrada, lida com a localização e a validação do lado do cliente.
* As [ABP Form tag helpers](tag-helpers/form-elements.md) (`abp-input`, `abp-select`, `abp-radio`...) renderizam **um único elemento de formulário** lidando com a localização e a validação do lado do cliente.
* O ABP **localiza automaticamente o nome de exibição** de um elemento de formulário sem a necessidade de adicionar um atributo `[DisplayName]`.
* **Erros de validação** são automaticamente localizados com base na cultura do usuário.

> Este documento é para a **validação do lado do cliente** e não cobre a validação do lado do servidor. Consulte o [documento de validação](../../fundamentals/validation.md) para obter informações sobre a infraestrutura de validação do lado do servidor.

## A Maneira Clássica

Em uma interface do usuário típica baseada em Bootstrap ASP.NET Core MVC / Razor Pages, você [precisa escrever](https://docs.microsoft.com/en-us/aspnet/core/mvc/models/validation#client-side-validation) um código boilerplate como este para criar um elemento de formulário simples:

```html
<div class="form-group">
    <label asp-for="Movie.ReleaseDate" class="control-label"></label>
    <input asp-for="Movie.ReleaseDate" class="form-control" />
    <span asp-validation-for="Movie.ReleaseDate" class="text-danger"></span>
</div>
```

Você pode continuar usando esta abordagem se precisar ou preferir. No entanto, as ABP Form tag helpers podem produzir a mesma saída com um código mínimo.

## Formulários Dinâmicos ABP

A tag helper [abp-dynamic-form](tag-helpers/dynamic-forms.md) automatiza completamente a criação do formulário. Considere esta classe de modelo como um exemplo:

```csharp
using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;

namespace MyProject.Web.Pages
{
    public class MovieViewModel
    {
        [Required]
        [StringLength(256)]
        public string Name { get; set; }

        [Required]
        [DataType(DataType.Date)]
        public DateTime ReleaseDate { get; set; }

        [Required]
        [TextArea]
        [StringLength(1000)]
        public string Description { get; set; }

        public Genre Genre { get; set; }

        public float? Price { get; set; }

        public bool PreOrder { get; set; }
    }
}
```

Ele usa os atributos de anotação de dados para definir regras de validação e estilos de IU para as propriedades. `Genre` é um `enum` neste exemplo:

```csharp
namespace MyProject.Web.Pages
{
    public enum Genre
    {
        Classic,
        Action,
        Fiction,
        Fantasy,
        Animation
    }
}
```

Para criar o formulário em uma razor page, crie uma propriedade em sua classe `PageModel`:

```csharp
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace MyProject.Web.Pages
{
    public class CreateMovieModel : PageModel
    {
        [BindProperty]
        public MovieViewModel Movie { get; set; }

        public void OnGet()
        {
            Movie = new MovieViewModel();
        }

        public async Task OnPostAsync()
        {
            if (ModelState.IsValid)
            {
                //TODO: Salvar o Filme
            }
        }
    }
}
```

Em seguida, você pode renderizar o formulário no arquivo `.cshtml`:

```html
@page
@model MyProject.Web.Pages.CreateMovieModel

<h2>Criar um novo Filme</h2>

<abp-dynamic-form abp-model="Movie" submit-button="true" />
```

O resultado é mostrado abaixo:

![abp-dynamic-form-result](/ABP-Docs/images/abp-dynamic-form-result.png)

Consulte a seção *Localização e Validação* abaixo para localizar os nomes de exibição dos campos e ver como a validação funciona.

> Consulte [seu próprio documento](tag-helpers/dynamic-forms.md) para todas as opções da tag helper `abp-dynamic-form`.

## ABP Form Tag Helpers

`abp-dynamic-form` cobre a maioria dos cenários e permite que você controle e personalize o formulário usando os atributos.

No entanto, se você quiser **renderizar o corpo do formulário você mesmo** (por exemplo, você pode querer controlar totalmente o **layout do formulário**), você pode usar diretamente as [ABP Form Tag Helpers](tag-helpers/form-elements.md). O mesmo formulário gerado automaticamente acima pode ser criado usando as ABP Form Tag Helpers, conforme mostrado abaixo:

```html
@page
@model MyProject.Web.Pages.CreateMovieModel

<h2>Criar um novo Filme</h2>

<form method="post">
    <abp-input asp-for="Movie.Name"/>
    <abp-input asp-for="Movie.ReleaseDate"/>
    <abp-input asp-for="Movie.Description"/>
    <abp-select asp-for="Movie.Genre"/>
    <abp-input asp-for="Movie.Price"/>
    <abp-input asp-for="Movie.PreOrder"/>
    <abp-button button-type="Primary" type="submit">Salvar</abp-button>
</form>
```

> Consulte o documento [ABP Form Tag Helpers](tag-helpers/form-elements.md) para obter detalhes sobre essas tag helpers e suas opções.

## Validação e Localização

Tanto o Dynamic Form quanto as Form Tag Helpers **validam automaticamente** a entrada com base nos atributos de anotação de dados e mostram mensagens de erro de validação na interface do usuário. As mensagens de erro são **automaticamente localizadas** com base na cultura atual.

**Exemplo: o usuário deixa em branco uma propriedade de string obrigatória**

![abp-form-input-validation-error](/ABP-Docs/images/abp-form-input-validation-error.png)

A mensagem de erro abaixo é exibida se o idioma for francês:

![abp-form-input-validation-error](/ABP-Docs/images/abp-form-input-validation-error-french.png)

Os erros de validação já foram [traduzidos](https://github.com/abpframework/abp/tree/dev/framework/src/Volo.Abp.Validation/Volo/Abp/Validation/Localization) para muitos idiomas. Você pode [contribuir](../../../contribution) para a tradução para seu próprio idioma ou substituir os textos para seu próprio aplicativo seguindo a documentação de [localização](../../fundamentals/localization.md).

## Localização do Nome de Exibição

O ABP usa o nome da propriedade como o nome do campo na interface do usuário. Normalmente, você deseja [localizar](../../fundamentals/localization.md) este nome com base na cultura atual.

O ABP pode localizar convencionalmente os campos na IU quando você adiciona as chaves de localização aos arquivos JSON de localização.

Exemplo: localização em francês para a propriedade *Name* (adicione em `fr.json` no aplicativo):

```js
"Name": "Nom"
```

Então a IU usará o nome dado para o idioma francês:

![abp-form-input-validation-error](/ABP-Docs/images/abp-form-input-validation-error-french-name.png)

### Usando o Prefixo `DisplayName:`

Usar diretamente o nome da propriedade como a chave de localização pode ser um problema se você precisar usar o nome da propriedade para outro propósito, que possui um valor de tradução diferente. Nesse caso, use o prefixo `DisplayName:` para a chave de localização:

```js
"DisplayName:Name": "Nom"
```

O ABP prefere usar a chave `DisplayName:Name` em vez da chave `Name` se ela existir.

### Usando uma Chave de Localização Personalizada

Se precisar, você pode usar o atributo `[DisplayName]` para especificar a chave de localização para uma propriedade específica:

```csharp
[DisplayName("MyNameKey")]
public string Name { get; set; }
```

Neste caso, você pode adicionar uma entrada ao arquivo de localização usando a chave `MyNameKey`.

> Se você usar `[DisplayName]` mas não adicionar uma entidade correspondente ao arquivo de localização, o ABP mostra a chave fornecida como o nome do campo, `MyNameKey` para este caso. Portanto, ele oferece uma maneira de especificar um nome de exibição codificado, mesmo que você não precise usar o sistema de localização.

### Localização de Enum

Os membros do Enum também são automaticamente localizados sempre que possível. Por exemplo, quando adicionamos `<abp-select asp-for="Movie.Genre"/>` ao formulário (como fizemos na seção *ABP Form Tag Helpers*), o ABP pode preencher automaticamente os nomes localizados dos membros do Enum. Para habilitá-lo, você deve definir os valores localizados em seu arquivo JSON de localização. Exemplo de entradas para o Enum `Genre` definido na seção *ABP Form Tag Helpers*:

```json
"Enum:Genre.0": "Filme clássico",
"Enum:Genre.1": "Filme de ação",
"Enum:Genre.2": "Ficção",
"Enum:Genre.3": "Fantasia",
"Enum:Genre.4": "Animação/Desenho"
```

Você pode usar uma das seguintes sintaxes para as chaves de localização:

* `Enum:<nome-do-tipo-enum>.<valor-enum>`
* `<nome-do-tipo-enum>.<valor-enum>`

> Lembre-se de que, se você não especificar valores para seu Enum, os valores serão ordenados, começando em `0`.

> As tag helpers MVC também oferecem suporte ao uso de nomes de membros Enum em vez de valores (portanto, você pode definir `"Enum:Genre.Action"` em vez de `"Enum:Genre.1"`, por exemplo), mas não é sugerido. Porque, quando você serializa propriedades Enum para JSON e envia para os clientes, o serializador padrão usa valores Enum em vez de nomes Enum. Portanto, o nome Enum não estará disponível para os clientes e será um problema se você quiser usar os mesmos valores de localização no lado do cliente.

## Veja Também

* [Validação do lado do servidor](../../fundamentals/Validation.md)
