# Cards

## Introdução

`abp-card` é um container de conteúdo derivado do elemento card do bootstrap.

Uso básico:

```xml
<abp-card style="width: 18rem;">
  <img abp-card-image="Top" src="~/imgs/demo/300x200.png"/>
  <abp-card-body>
    <abp-card-title>Card Title</abp-card-title>
    <abp-card-text>Some quick example text to build on the card title and make up the bulk of the card's content.</abp-card-text>
    <a abp-button="Primary" href="#"> Go somewhere</a>
  </abp-card-body>
</abp-card>
```

##### Utilizando Títulos, Texto e Links:

As seguintes tags podem ser usadas dentro da tag principal `abp-card`

* `abp-card-title`
* `abp-card-subtitle`
* `a abp-card-link`

Exemplo:

```xml
<abp-card style="width: 18rem;">
    <abp-card-body>
       <abp-card-title>Card title</abp-card-title>
       <abp-card-subtitle class="mb-2 text-muted">Card subtitle</abp-card-subtitle>
       <abp-card-text>Some quick example text to build on the card title and make up the bulk of the card's content.</abp-card-text>
       <a abp-card-link href="#">Card link</a>
       <a abp-card-link href="#">Another link</a>
    </abp-card-body>
</abp-card>
```

##### Utilizando List Groups:

* `abp-list-group flush="true"`: O atributo `flush` renderiza a classe `list-group-flush` do bootstrap, que é usada para remover bordas e cantos arredondados para renderizar itens de list group de ponta a ponta em um container pai.
* `abp-list-group-item`

Exemplo "Completo":

```xml
<abp-card style="width: 18rem;">
    <img abp-card-image="Top" src="~/imgs/demo/300x200.png" />
    <abp-card-body>
       <abp-card-title>Card Title</abp-card-title>
       <abp-card-text>Some quick example text to build on the card title and make up the bulk of the card's content.</abp-card-text>
    </abp-card-body>
    <abp-list-group flush="true">
       <abp-list-group-item>Cras justo odio</abp-list-group-item>
       <abp-list-group-item>Dapibus ac facilisis in</abp-list-group-item>
       <abp-list-group-item>Vestibulum at eros</abp-list-group-item>
    </abp-list-group>
    <abp-card-body>
       <a abp-card-link href="#">Card link</a>
       <a abp-card-link href="#">Another link</a>
    </abp-card-body>
</abp-card>
```

##### Utilizando Header, Footer e Blockquote:

* `abp-card-header`
* `abp-card-footer`
* `abp-blockquote`

Exemplo:

```xml
<abp-card style="width: 18rem;">
    <abp-card-header>Featured</abp-card-header>
    <abp-card-body>
       <abp-card-title> Special title treatment</abp-card-title>
       <abp-card-text>With supporting text below as a natural lead-in to additional content.</abp-card-text>
       <a abp-button="Primary" href="#"> Go somewhere</a>
    </abp-card-body>
</abp-card>
```

Exemplo de Citação:

```xml
<abp-card>
    <abp-card-header>Quote</abp-card-header>
    <abp-card-body>
<abp-blockquote>
    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer posuere erat a ante.</p>
    <footer>Someone famous in Source Title</footer>
</abp-blockquote>
    </abp-card-body>
</abp-card>
```

Exemplo de Footer:

```xml
<abp-card class="text-center">
    <abp-card-header>Featured</abp-card-header>
    <abp-card-body>
        <abp-blockquote>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer posuere erat a ante.</p>
            <footer>Someone famous in Source Title</footer>
        </abp-blockquote>
    </abp-card-body>
    <abp-card-footer class="text-muted"> 2 days ago</abp-card-footer>
</abp-card>
```

## Demonstração

Veja a [página de demonstração de cards](https://bootstrap-taghelpers.abp.io/Components/Cards) para vê-lo em ação.

## Atributos de abp-card

-   **background:** Um valor que indica a cor de fundo do card.
-   **text-color**: Um valor que indica a cor do texto dentro do card.
-   **border:** Um valor que indica a cor da borda dentro do card.

Deve ser um dos seguintes valores:

*   `Default` (valor padrão)
*   `Primary`
*   `Secondary`
*   `Success`
*   `Danger`
*   `Warning`
*   `Info`
*   `Light`
*   `Dark`

Exemplo:

```xml
<abp-card background="Success" text-color="Danger" border="Dark">
```

### sizing

Cards tem 100% de largura por padrão e pode ser alterado com CSS personalizado, classes de grid, mixins Sass de grid ou [utilitários](https://getbootstrap.com/docs/4.0/utilities/sizing/).

```xml
<abp-card style="width: 18rem;">
```

### card-deck e card-columns

`abp-card` pode ser usado dentro de `card-deck` ou `card-columns` também.

```xml
<div class="card-deck">
    <abp-card background="Primary">
        <abp-card-header>First Deck</abp-card-header>
        <abp-card-body>
            <abp-card-title> Ace </abp-card-title>
            <abp-card-text>Here is the content for Ace.</abp-card-text>
        </abp-card-body>
    </abp-card>
    <abp-card background="Info">
        <abp-card-header>Second Deck</abp-card-header>
        <abp-card-body>
            <abp-card-title> Beta </abp-card-title>
            <abp-card-text>Beta content.</abp-card-text>
        </abp-card-body>
    </abp-card>
    <abp-card background="Warning">
        <abp-card-header>Third Deck</abp-card-header>
        <abp-card-body>
            <abp-card-title> Epsilon </abp-card-title>
            <abp-card-text>Content for Epsilon.</abp-card-text>
        </abp-card-body>
    </abp-card>
</div>
```
