# UI para ASP.NET Core MVC / Razor Pages: API JavaScript CurrentUser

`abp.currentUser` é um objeto que contém informações sobre o usuário atual do aplicativo.

> Este documento explica apenas a API JavaScript. Consulte o [documento CurrentUser](../../../CurrentUser.md) para obter informações sobre o usuário atual no lado do servidor.

## Usu<PERSON>rio Autenticado

Se o usuário foi autenticado, este objeto será algo como abaixo:

```js
{
  isAuthenticated: true,
  id: "34f1f4a7-13cc-4b91-84d1-b91c87afa95f",
  tenantId: null,
  userName: "john",
  name: "<PERSON>",
  surName: "<PERSON>",
  email: "<EMAIL>",
  emailVerified: true,
  phoneNumber: null,
  phoneNumberVerified: false,
  roles: ["moderator","supporter"]
}
```

<PERSON><PERSON><PERSON>, `abp.currentUser.userName` retorna `john` neste caso.

## Usu<PERSON>rio Anônimo

Se o usuário não foi autenticado, este objeto será algo como abaixo:

```js
{
  isAuthenticated: false,
  id: null,
  tenantId: null,
  userName: null,
  name: null,
  surName: null,
  email: null,
  emailVerified: false,
  phoneNumber: null,
  phoneNumberVerified: false,
  roles: []
}
```

Você pode verificar `abp.currentUser.isAuthenticated` para entender se o usuário foi autenticado ou não.
