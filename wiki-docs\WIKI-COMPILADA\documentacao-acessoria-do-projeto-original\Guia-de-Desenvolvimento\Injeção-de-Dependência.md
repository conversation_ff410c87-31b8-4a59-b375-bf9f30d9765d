[[_TOC_]]
# Injeção de Dependência

O sistema de Injeção de Dependência do ABP foi desenvolvido com base na biblioteca de extensão de injeção de dependência da Microsoft. Portanto, sua documentação também é válida no ABP.

> O ABP não possui dependência central de nenhum provedor DI de terceiros. No entanto, é necessário usar um provedor que suporte proxy dinâmico e alguns outros recursos avançados para que algumas funcionalidades do ABP funcionem corretamente. Os templates de inicialização vêm com o [Autofac](https://autofac.org/) instalado. Consulte o documento [integração com Autofac](https://abp.io/docs/8.2/framework/fundamentals/autofac-integration) para mais informações.

## Tipos Registrados por Padrão

Alguns tipos específicos são registrados automaticamente na injeção de dependência. Exemplos:

- Classes de módulo são registradas como singleton.
- Controllers do MVC (que herdam de `Controller` ou `AbpController`) são registrados como transient.
- Modelos de páginas do MVC (que herdam de `PageModel` ou `AbpPageModel`) são registrados como transient.
- Componentes de visão do MVC (que herdam de `ViewComponent` ou `AbpViewComponent`) são registrados como transient.
- Serviços de aplicação (que herdam da classe `ApplicationService` ou suas subclasses) são registrados como transient.
- Repositórios (que implementam a classe `BasicRepositoryBase` ou suas subclasses) são registrados como transient.
- Serviços de domínio (que implementam a interface `IDomainService` ou herdam da classe `DomainService`) são registrados como transient.

Exemplo:

```csharp
public class BlogPostAppService : ApplicationService
{
}
```

`BlogPostAppService` é automaticamente registrado com o tempo de vida transient porque herda de uma classe base conhecida.

### Interfaces de Dependência

Se você implementar estas interfaces, sua classe será registrada automaticamente na injeção de dependência:

- `ITransientDependency` para registrar com tempo de vida transient.
- `ISingletonDependency` para registrar com tempo de vida singleton.
- `IScopedDependency` para registrar com tempo de vida scoped.

Exemplo:

```csharp
public class TaxCalculator : ITransientDependency
{
}
```

`TaxCalculator` é automaticamente registrado com tempo de vida transient porque implementa `ITransientDependency`.

### Atributo Dependency

Outra maneira de configurar um serviço para injeção de dependência é usar o atributo `DependencyAttribute`. Ele possui as seguintes propriedades:

- `Lifetime`: Tempo de vida do registro: `Singleton`, `Transient` ou `Scoped`.
- `TryRegister`: Define como `true` para registrar o serviço apenas se ainda não estiver registrado. Utiliza métodos de extensão `TryAdd...` de `IServiceCollection`.
- `ReplaceServices`: Define como `true` para substituir serviços se já estiverem registrados. Utiliza o método de extensão `Replace` de `IServiceCollection`.

Exemplo:

```csharp
[Dependency(ServiceLifetime.Transient, ReplaceServices = true)]
public class TaxCalculator
{
}
```

O atributo `Dependency` tem maior prioridade do que outras interfaces de dependência se definir a propriedade `Lifetime`.

### Atributo ExposeServices

O atributo `ExposeServicesAttribute` é usado para controlar quais serviços são fornecidos pela classe relacionada. Exemplo:

```csharp
[ExposeServices(typeof(ITaxCalculator))]
public class TaxCalculator: ICalculator, ITaxCalculator, ICanCalculate, ITransientDependency
{
}
```

A classe `TaxCalculator` expõe apenas a interface `ITaxCalculator`. Isso significa que você pode injetar apenas `ITaxCalculator`, mas não `TaxCalculator` ou `ICalculator` na sua aplicação.

### Serviços Expostos por Convenção

Se você não especificar quais serviços expor, o ABP os expõe por convenção. Para a classe `TaxCalculator` definida acima:

- A própria classe é exposta por padrão. Isso significa que você pode injetá-la pela classe `TaxCalculator`.
- Interfaces padrão são expostas por padrão. Interfaces padrão são determinadas pela convenção de nomenclatura. Nesse exemplo, `ICalculator` e `ITaxCalculator` são interfaces padrão de `TaxCalculator`, mas `ICanCalculate` não é.

### Combinando Tudo

É possível combinar atributos e interfaces desde que seja significativo.

```csharp
[Dependency(ReplaceServices = true)]
[ExposeServices(typeof(ITaxCalculator))]
public class TaxCalculator : ITaxCalculator, ITransientDependency
{
}
```

### Atributo ExposeKeyedService

O atributo `ExposeKeyedServiceAttribute` é usado para expor serviços chaveados fornecidos pela classe relacionada. Exemplo:

```csharp
[ExposeKeyedService<ITaxCalculator>("taxCalculator")]
[ExposeKeyedService<ICalculator>("calculator")]
public class TaxCalculator: ICalculator, ITaxCalculator, ICanCalculate, ITransientDependency
{
}
```

Nesse exemplo, a classe `TaxCalculator` expõe a interface `ITaxCalculator` com a chave `taxCalculator` e a interface `ICalculator` com a chave `calculator`. Isso significa que você pode obter serviços chaveados de `IServiceProvider`.

## Registro Convencional e Registro Manual

O ABP introduz o registro convencional de serviços. Você não precisa fazer nada para registrar um serviço por convenção; é feito automaticamente. Se quiser desativá-lo, defina `SkipAutoServiceRegistration` como `true` no construtor da sua classe de módulo. Exemplo:

```csharp
public class BlogModule : AbpModule
{
    public BlogModule()
    {
        SkipAutoServiceRegistration = true;
    }
}
```

### Modularidade

Como o ABP é um framework modular, cada módulo define seus próprios serviços e os registra via injeção de dependência em sua própria classe de módulo. Exemplo:

```csharp
public class BlogModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        //registre dependências aqui
    }
}
```

Após desativar o registro automático, você deverá registrar seus serviços manualmente. Nesse caso, o método de extensão `AddAssemblyOf` pode ajudar a registrar todos os seus serviços por convenção. Exemplo:

```csharp
public class BlogModule : AbpModule
{
    public BlogModule()
    {
        SkipAutoServiceRegistration = true;
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.AddAssemblyOf<BlogModule>();
    }
}
```


Em alguns casos, pode ser necessário registrar manualmente um serviço em `IServiceCollection`, especialmente se precisar usar métodos de fábrica personalizados ou instâncias singleton. Exemplo:

```csharp
public class BlogModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        // Registra uma instância como singleton
        context.Services.AddSingleton<TaxCalculator>(new TaxCalculator(taxRatio: 0.18));

        // Registra um método de fábrica
        context.Services.AddScoped<ITaxCalculator>(
            sp => sp.GetRequiredService<TaxCalculator>()
        );
    }
}
```
