---
description: Regras específicas para desenvolvimento na camada Domain do TRF3.SISPREC
globs: src/TRF3.SISPREC.Domain/**/*
alwaysApply: true
---

# Regras para SISPREC Domain Developer

## **Responsabilidades Principais**
- **Entidades de Negócio**: Implementar entidades ricas em comportamento
- **Agregados**: Manter integridade e consistência dos agregados
- **Domain Services**: Implementar lógica de negócio complexa
- **Interfaces de Repositório**: Definir contratos de persistência
- **Regras de Negócio**: Centralizar toda lógica de negócio no domínio

## **Principais Agregados do Sistema**
- **RequisicoesProtocolos**: Agregado principal - Requisições de precatórios
- **Processos**: Agregado - Processos judiciais
- **Beneficiarios**: Agregado - Beneficiários dos precatórios
- **Parcelas**: Entidade - Parcelas de pagamento
- **Partes**: Entidade - Partes processuais
- **Pessoas**: Entidade - Pessoas físicas/jurídicas

## **Padrões DDD Implementados**
- **Aggregate Root**: Entidades principais que controlam acesso ao agregado
- **Entity**: Entidades com identidade única
- **Value Object**: Objetos sem identidade, definidos por seus atributos
- **Domain Service**: Serviços que encapsulam lógica de negócio
- **Repository Pattern**: Interfaces para abstração de persistência
- **Specification Pattern**: Critérios de consulta reutilizáveis

## **Diretrizes de Desenvolvimento**
- **Entidades Ricas**: Implementar comportamento nas entidades, não apenas propriedades
- **Invariantes**: Garantir que regras de negócio sejam sempre respeitadas
- **Encapsulamento**: Proteger estado interno das entidades
- **Consistência**: Manter consistência dentro dos limites do agregado
- **Separação de Responsabilidades**: Cada classe deve ter uma responsabilidade clara

## **Regras de Negócio**
- **Validações de Domínio**: Implementar validações de negócio nas entidades
- **Cálculos**: Centralizar cálculos complexos em domain services
- **Workflows**: Implementar fluxos de negócio no domínio
- **Estados**: Controlar transições de estado das entidades

## **Tecnologias e Frameworks**
- **ABP Framework 8**: Base para entidades e domain services
- **Entity Framework Core**: Mapeamento objeto-relacional
- **C# 12**: Recursos modernos da linguagem
- **.NET 8**: Framework base

## **Boas Práticas**
- Manter o domínio independente de infraestrutura
- Implementar testes unitários para regras de negócio
- Usar eventos de domínio para comunicação entre agregados
- Evitar dependências externas no domínio
- Documentar regras de negócio complexas
