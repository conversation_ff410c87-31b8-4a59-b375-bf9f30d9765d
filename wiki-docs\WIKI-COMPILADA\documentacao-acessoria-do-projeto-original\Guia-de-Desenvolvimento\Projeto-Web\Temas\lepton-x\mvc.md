# LeptonX MVC UI

O tema LeptonX está implementado e pronto para uso com o ABP. Nenhuma implementação personalizada é necessária para Razor Pages.

## Instalação

- Instale o pacote no seu projeto **Web** com a CLI.
```bash
abp add-package Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonX
```
- Remova as referências `Volo.Abp.AspNetCore.Mvc.UI.Theme.Lepton` e `Volo.Abp.LeptonTheme.Management.Web` do projeto, pois não são necessárias após a mudança para o LeptonX.

- Certifique-se de que o tema antigo foi removido e o LeptonX foi adicionado na sua classe de Módulo.

```diff
[DependsOn(
        // ...
        // remove as seguintes linhas
-       typeof(LeptonThemeManagementWebModule),
-       typeof(AbpAspNetCoreMvcUiLeptonThemeModule),

        // adicione a linha abaixo
+       typeof(AbpAspNetCoreMvcUiLeptonXThemeModule),
)]
```

- Substitua `LeptonThemeBundles` por `LeptonXThemeBundles` em AbpBundlingOptions.

```csharp
    options.StyleBundles.Configure(
            LeptonXThemeBundles.Styles.Global, // 👈 Aqui
            bundle =>
            {
                bundle.AddFiles("/global-styles.css");
            }
        );
```

## Código-Fonte
Você pode baixar o código-fonte do tema LeptonX de acordo com sua licença ABP.

Visite a seção [Código-Fonte LeptonX](index#source-code) para baixar o código-fonte.

## Customização

Antes de começar a customizar o tema, você pode considerar baixar o código-fonte do tema. Você pode encontrar os códigos originais dos componentes relacionados abaixo no código-fonte.

---

### Aparência
Você pode definir um tema padrão, adicionar ou remover estilos de aparência usando o **LeptonXThemeOptions**.

- `DefaultStyle`: Define o tema padrão de fallback. O valor padrão é **Dim**
  
```csharp
Configure<LeptonXThemeOptions>(options =>
{
    options.DefaultStyle = LeptonXStyleNames.Dark;
});
```

- `Styles`: Define as aparências selecionáveis ​​da interface do usuário.

![Temas selecionáveis do Lepton-x](images/selectable-themes.png)

```csharp
Configure<LeptonXThemeOptions>(options =>
{
    // Removendo os estilos existentes
    options.Styles.Remove(LeptonXStyleNames.Light);

    // Adicionando um novo estilo
    options.Styles.Add("red", 
        new LeptonXThemeStyle(
        LocalizableString.Create<YourResource>("Theme:Red"),
        "bi bi-circle-fill"));
});

```

> `red.css` e `bootstrap-red.css` devem ser adicionados na pasta **wwwroot/Themes/LeptonX/Global/side-menu/css/** para alternar para o seu tema personalizado corretamente quando selecionado.
>
> Se o seu layout for **TopMenu**, você deverá adicioná-los na pasta **wwwroot/Themes/LeptonX/Global/top-menu/css/**.

--- 

### LeptonXThemeMvcOptions
As opções de layout da interface do usuário do MVC Razor Pages podem ser gerenciadas usando **LeptonXThemeMvcOptions**.

- `ApplicationLayout`: Layout da aplicação principal. O valor padrão é `LeptonXMvcLayouts.SideMenu`

    ```csharp
    Configure<LeptonXThemeMvcOptions>(options =>
    {
        options.ApplicationLayout = LeptonXMvcLayouts.SideMenu;
        // Ou seu layout personalizado implementado:
        options.ApplicationLayout = "~/Shared/_Layout.cshtml";
    });
    ```

- `MobileMenuSelector`: Define os itens a serem exibidos no menu mobile. O valor padrão são os 2 primeiros itens dos itens do menu principal.

    ![Visualização do menu mobile do LeptonX](images/mobile-menu-preview.png)

    ```csharp
    Configure<LeptonXThemeMvcOptions>(options =>
    {
        options.MobileMenuSelector = items => items.Where(x => x.MenuItem.Name == "MyProjectName.Home" || x.MenuItem.Name == "MyProjectName.Dashboard");
    });
    ```

### Layouts

**LeptonX** oferece dois **layouts prontos** para sua aplicação web. Um deles é **posicionado** com os **itens de menu** no **topo** e o outro com os **itens de menu** nas **laterais**.

### Layout do Menu Superior

![Layout do menu superior](images/leptonx-top-menu-layout.png)

### Layout do Menu Lateral

![Layout do menu lateral](images/leptonx-side-menu-layout.png)

> Os layouts são definidos na pasta **Themes/LeptonX/Layouts/Application** e você pode **substituí-los** criando o arquivo (**SideMenuLayout.cshtml** ou **TopMenuLayout.cshtml**) com o **mesmo nome** e na **mesma pasta**. 

> Existem **partial views** que são definidas na pasta **Themes/LeptonX/Layouts/Application**. Você pode **substituí-las** criando o arquivo com o **mesmo nome** e na **mesma pasta**. 

> - **_Footer.cshtml**
> - **_Sidebar.cshtml**
> - **_Toolbar.cshtml**

### Layout da Conta

![Layout da conta](images/leptonx-account-layout-new.png)

> O **layout da conta** é definido na pasta **Themes/LeptonX/Layouts/Account** e você pode **substituí-lo** criando o arquivo (**Default.cshtml**) com o **mesmo nome** e na **mesma pasta**. Existe uma **partial view**, seu nome é **Footer.cshtml**. Você pode **substituí-la** **seguindo** da **mesma forma**.

---

## Componentes

O ABP **ajuda** você a criar uma **UI altamente customizável**. Você pode facilmente **customizar** seus temas para se adequar às suas necessidades. **O Sistema de Arquivos Virtual** torna possível **gerenciar arquivos** que **não existem fisicamente** no **sistema de arquivos** (disco). Ele é usado principalmente para incorporar arquivos **(js, css, image...)** em assemblies e **usá-los como** arquivos físicos em tempo de execução.

Uma aplicação (ou outro módulo) pode **substituir** um **arquivo virtual de um módulo** assim como colocar um arquivo com o **mesmo nome** e **extensão** na **mesma pasta** do **arquivo virtual**.

LeptonX é construído sobre o [ABP](https://abp.io/), então você pode **facilmente** customizar sua interface de usuário Asp.Net Core Mvc seguindo a [Customização da UI Abp Mvc](../../framework/ui/mvc-razor-pages/customization-user-interface.md).

## Componentes Comuns

Componentes comumente usados em todos os layouts.

### Breadcrumb

![Breadcrumb](images/leptonx-breadcrumb.png)

Os breadcrumbs podem ser customizados usando o serviço `IPageLayout`. Veja [PageLayout - Breadcrumb](../../framework/ui/mvc-razor-pages/page-header.md#breadcrumb) para mais informações. 

Se você precisa substituir o componente, você pode seguir os passos abaixo.

- A **página do componente breadcrumb (.cshtml)** é definida no arquivo `Themes/LeptonX/Components/Common/BreadCrumb/Default.cshtml` e você pode **substituí-la** criando um arquivo com o **mesmo nome** e **na mesma pasta**.  

- O **componente breadcrumb (arquivo C#)** é definido no arquivo `Themes/LeptonX/Components/Common/BreadCrumb/ContentBreadCrumbViewComponent.cs` e você pode **substituí-lo** criando um arquivo com o **mesmo nome** e **na mesma pasta**.



### Título do Conteúdo

![Título do conteúdo](images/leptonx-content-title.png)

Os títulos das páginas podem ser customizados usando o serviço `IPageLayout`. Veja [PageLayout - Título da Página](../../framework/ui/mvc-razor-pages/page-header.md#page-title) para mais informações.

Se você precisa substituir o componente, você pode seguir os passos abaixo.

* A **página do componente título do conteúdo (.cshtml)** é definida no arquivo `Themes/LeptonX/Components/Common/ContentTitle/Default.cshtml` e você pode **substituí-la** criando um arquivo com o **mesmo nome** e **na mesma pasta**.

* O **componente título do conteúdo (arquivo C#)** é definido no arquivo `Themes/LeptonX/Components/Common/ContentTitle/ContentTitleViewComponent.cs` e você pode **substituí-lo** criando um arquivo com o **mesmo nome** e **na mesma pasta**.


### Configurações Gerais

![Configurações gerais](images/leptonx-general-settings.png)

As Configurações Gerais podem ser substituídas pelos arquivos seguintes.

* A **página do componente configurações gerais (.cshtml)** é definida no arquivo `Themes/LeptonX/Components/Common/GeneralSettings/Default.cshtml` e você pode **substituí-la** criando um arquivo com o **mesmo nome** e **na mesma pasta**.

* O **componente configurações gerais (arquivo C#)** é definido no arquivo `Themes/LeptonX/Components/Common/GeneralSettings/GeneralSettingsViewComponent.cs` e você pode **substituí-lo** criando um arquivo com o **mesmo nome** e **na mesma pasta**.


### Branding do Header Principal

![Branding do header principal](images/leptonx-main-header-branding.png)

O nome e o logo da aplicação podem ser customizados usando o serviço `IBrandingProvider`. Veja [Razor Pages: Branding](../../framework/ui/mvc-razor-pages/branding.md) para mais informações.

Se você precisa substituir o componente, você pode seguir os passos abaixo.

* A **página do componente branding do header principal (.cshtml)** é definida no arquivo `Themes/LeptonX/Components/Common/MainHeaderBranding/Default.cshtml` e você pode **substituí-la** criando um arquivo com o **mesmo nome** e **na mesma pasta**.

* O **componente branding do header principal (arquivo C#)** é definido no arquivo `Themes/LeptonX/Components/Common/MainHeaderBranding/MainHeaderBrandingViewComponent.cs` e você pode **substituí-lo** criando um arquivo com o **mesmo nome** e **na mesma pasta**.

### Configurações Gerais Mobile

![Configurações gerais mobile](images/leptonx-mobile-general-settings.png)

* A **página do componente configurações gerais mobile (.cshtml)** é definida no arquivo `Themes/LeptonX/Components/Common/MobileGeneralSettings/Default.cshtml` e você pode **substituí-la** criando um arquivo com o **mesmo nome** e **na mesma pasta**.

* O **componente configurações gerais mobile (arquivo C#)** é definido no arquivo `Themes/LeptonX/Components/Common/MobileGeneralSettings/MobileGeneralSettingsViewComponent.cs` e você pode **substituí-lo** criando um arquivo com o **mesmo nome** e **na mesma pasta**.

### Alertas da Página

![Alertas da página](images/leptonx-page-alerts.png)

* A **página do componente alertas da página (.cshtml)** é definida no arquivo `Themes/LeptonX/Components/PageAlerts/Default.cshtml` e você pode **substituí-la** criando um arquivo com o **mesmo nome** e **na mesma pasta**.

* O **componente alertas da página (arquivo C#)** é definido no arquivo `Themes/LeptonX/Components/PageAlerts/PageAlertsViewComponent.cs` e você pode **substituí-lo** criando um arquivo com o **mesmo nome** e **na mesma pasta**.

---

## Componentes do Menu Lateral

Componentes usados no layout do menu lateral.

### Menu Principal

![Menu principal da barra lateral](images/leptonx-sidebar-main-menu.png)

* A **página do componente menu principal (.cshtml)** é definida no arquivo `Themes/LeptonX/Components/SideMenu/MainMenu/Default.cshtml` e você pode **substituí-la** criando um arquivo com o **mesmo nome** e **na mesma pasta**.

* O **componente menu principal (arquivo C#)** é definido no arquivo `Themes/LeptonX/Components/SideMenu/MainMenu/MainMenuViewComponent.cs` e você pode **substituí-lo** criando um arquivo com o **mesmo nome** e **na mesma pasta**.

> O **componente menu principal** usa **partial view** para renderizar os **itens de menu**. A **partial view** é definida no arquivo `Themes/LeptonX/Components/SideMenu/MainMenu/_MenuItem.cshtml` e você pode **substituí-la** criando um arquivo com o **mesmo nome** e **na mesma pasta**.

### Navbar Mobile

![Navbar mobile](images/leptonx-sidemenu-mobile-navbar.png)

* A **página do componente navbar mobile (.cshtml)** é definida no arquivo `Themes/LeptonX/Components/SideMenu/MobileNavbar/Default.cshtml` e você pode **substituí-la** criando um arquivo com o **mesmo nome** e **na mesma pasta**.

* O **componente navbar mobile (arquivo C#)** é definido no arquivo `Themes/LeptonX/Components/SideMenu/MobileNavbar/MobileNavbarViewComponent.cs` e você pode **substituí-lo** criando um arquivo com o **mesmo nome** e **na mesma pasta**.

### Seletor de Idioma

![Seletor de idioma](images/leptonx-sidemenu-language-switch.png)

* A **página do componente seletor de idioma (.cshtml)** é definida no arquivo `Themes/LeptonX/Components/SideMenu/Toolbar/LanguageSwitch/Default.cshtml` e você pode **substituí-la** criando um arquivo com o **mesmo nome** e **na mesma pasta**.

* O **componente seletor de idioma (arquivo C#)** é definido no arquivo `Themes/LeptonX/Components/SideMenu/Toolbar/LanguageSwitch/LanguageSwitchViewComponent.cs` e você pode **substituí-lo** criando um arquivo com o **mesmo nome** e **na mesma pasta**.

### Menu do Usuário

![Menu do usuário](images/leptonx-sidemenu-user-menu.png)

* A **página do componente menu do usuário (.cshtml)** é definida no arquivo `Themes/LeptonX/Components/SideMenu/Toolbar/UserMenu/Default.cshtml` e você pode **substituí-la** criando um arquivo com o **mesmo nome** e **na mesma pasta**.

* O **componente menu do usuário (arquivo C#)** é definido no arquivo `Themes/LeptonX/Components/SideMenu/Toolbar/UserMenu/UserMenuViewComponent.cs` e você pode **substituí-lo** criando um arquivo com o **mesmo nome** e **na mesma pasta**.

> LeptonX implementa o ABP Menu [ABP Navegação/Menus](../../framework/ui/mvc-razor-pages/navigation-menu.md#standard-menus). Então você pode dar uma olhada para aprender como adicionar/remover itens de menu do menu do usuário.

---

## Componentes do Menu Superior

Componentes usados no layout do menu superior.

### Header Principal

![Header principal](images/leptonx-topmenu-main-header.png)

* A **página do componente header principal (.cshtml)** é definida no arquivo `Themes/LeptonX/Components/TopMenu/MainHeader/Default.cshtml` e você pode **substituí-la** criando um arquivo com o **mesmo nome** e **na mesma pasta**.

* O **componente header principal (arquivo C#)** é definido no arquivo `Themes/LeptonX/Components/TopMenu/MainHeader/MainHeaderViewComponent.cs` e você pode **substituí-lo** criando um arquivo com o **mesmo nome** e **na mesma pasta**.

### Barra de Ferramentas do Header Principal

![Barra de ferramentas do header principal](images/leptonx-topmenu-main-header-toolbar.png)

* A **página do componente barra de ferramentas do header principal (.cshtml)** é definida no arquivo `Themes/LeptonX/Components/TopMenu/MainHeaderToolbar/Default.cshtml` e você pode **substituí-la** criando um arquivo com o **mesmo nome** e **na mesma pasta**.

* O **componente barra de ferramentas do header principal (arquivo C#)** é definido no arquivo `Themes/LeptonX/Components/TopMenu/MainHeaderToolbar/MainHeaderToolbarViewComponent.cs` e você pode **substituí-lo** criando um arquivo com o **mesmo nome** e **na mesma pasta**.


> Antes de substituir o componente da barra de ferramentas do header principal, visite a documentação do [ABP Toolbars](../../framework/ui/mvc-razor-pages/toolbars.md) para aprender como adicionar/remover itens de menu da barra de ferramentas do header principal.

### Menu Principal

![Menu principal](images/leptonx-topmenu-main-menu.png)

* A **página do componente menu principal (.cshtml)** é definida no arquivo `Themes/LeptonX/Components/TopMenu/MainMenu/Default.cshtml` e você pode **substituí-la** criando um arquivo com o **mesmo nome** e **na mesma pasta**.

* O **componente menu principal (arquivo C#)** é definido no arquivo `Themes/LeptonX/Components/TopMenu/MainMenu/MainMenuViewComponent.cs` e você pode **substituí-lo** criando um arquivo com o **mesmo nome** e **na mesma pasta**.

### Menu do Usuário

![Menu do usuário](images/leptonx-topmenu-user-menu.png)

* A **página do componente menu do usuário (.cshtml)** é definida no arquivo `Themes/LeptonX/Components/TopMenu/UserMenu/Default.cshtml` e você pode **substituí-la** criando um arquivo com o **mesmo nome** e **na mesma pasta**.

* O **componente menu do usuário (arquivo C#)** é definido no arquivo `Themes/LeptonX/Components/TopMenu/UserMenu/UserMenuViewComponent.cs` e você pode **substituí-lo** criando um arquivo com o **mesmo nome** e **na mesma pasta**.

> Antes de substituir o componente menu do usuário, visite a documentação do [ABP Navegação/Menus](../../framework/ui/mvc-razor-pages/navigation-menu.md#standard-menus) para aprender como adicionar/remover itens de menu do menu do usuário.
