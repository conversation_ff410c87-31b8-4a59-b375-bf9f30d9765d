# ASP.NET Core MVC / Razor Pages: Layout Hooks

O sistema de *theming* do ABP coloca o layout da página dentro dos pacotes NuGet de [tema](theming.md). Isso significa que a aplicação final não inclui um `Layout.cshtml`, então você não pode alterar diretamente o código de layout para customizá-lo.

Você pode copiar o código do tema para dentro da sua solução. Neste caso, você tem total liberdade para customizá-lo. No entanto, você não poderá receber as atualizações automáticas do tema (ao atualizar o pacote NuGet do tema).

O ABP fornece diferentes formas de [customizar a UI](customization-user-interface.md).

O **Sistema de Layout Hook** permite que você **adicione código** em algumas partes específicas do layout. Todos os layouts de todos os temas devem implementar esses *hooks*. Finalmente, você pode adicionar um **view component** em um ponto de *hook*.

## Exemplo: Adicionar o Script do Google Analytics

Assuma que você precisa adicionar o script do Google Analytics ao layout (que estará disponível para todas as páginas). Primeiro, **crie um view component** no seu projeto:

![bookstore-google-analytics-view-component](/ABP-Docs/images/bookstore-google-analytics-view-component.png)

**NotificationViewComponent.cs**

````csharp
public class GoogleAnalyticsViewComponent : AbpViewComponent
{
    public IViewComponentResult Invoke()
    {
        return View("/Pages/Shared/Components/GoogleAnalytics/Default.cshtml");
    }
}
````

**Default.cshtml**

````html
<script>
    (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
            (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
            m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
    })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

    ga('create', 'UA-xxxxxx-1', 'auto');
    ga('send', 'pageview');
</script>
````

Altere `UA-xxxxxx-1` com seu próprio código.

Você pode adicionar este componente a qualquer um dos pontos de *hook* no `ConfigureServices` do seu módulo:

````csharp
Configure<AbpLayoutHookOptions>(options =>
{
    options.Add(
        LayoutHooks.Head.Last, //O nome do hook
        typeof(GoogleAnalyticsViewComponent) //O componente a ser adicionado
    );
});
````

Agora, o código do GA será inserido no `head` da página como o último item.

### Especificando o Layout

A configuração acima adiciona o `GoogleAnalyticsViewComponent` a todos os layouts. Você pode querer adicionar apenas a um layout específico:

````csharp
Configure<AbpLayoutHookOptions>(options =>
{
    options.Add(
        LayoutHooks.Head.Last,
        typeof(GoogleAnalyticsViewComponent),
        layout: StandardLayouts.Application //Define o layout para adicionar
    );
});
````

Veja a seção *Layouts* abaixo para saber mais sobre o sistema de layout.

## Layout Hook Points

Existem alguns pontos de *hook* de layout predefinidos. O `LayoutHooks.Head.Last` usado acima era um deles. Os pontos de *hook* padrão são:

*   `LayoutHooks.Head.First`: Usado para adicionar um componente como o primeiro item na tag HTML head.
*   `LayoutHooks.Head.Last`: Usado para adicionar um componente como o último item na tag HTML head.
*   `LayoutHooks.Body.First`: Usado para adicionar um componente como o primeiro item na tag HTML body.
*   `LayoutHooks.Body.Last`: Usado para adicionar um componente como o último item na tag HTML body.
*   `LayoutHooks.PageContent.First`: Usado para adicionar um componente logo antes do conteúdo da página (o `@RenderBody()` no layout).
*   `LayoutHooks.PageContent.Last`: Usado para adicionar um componente logo após o conteúdo da página (o `@RenderBody()` no layout).

> Você (ou os módulos que você está usando) pode adicionar **múltiplos itens ao mesmo ponto de *hook***. Todos eles serão adicionados ao layout pela ordem em que foram adicionados.

## Layouts

O sistema de layout permite que os temas definam layouts padrão e nomeados e permite que qualquer página selecione um layout apropriado para o seu propósito. Existem três layouts predefinidos:

*   "**Application**": O layout principal (e o padrão) para uma aplicação. Ele normalmente contém cabeçalho, menu (barra lateral), rodapé, *toolbar*, etc.
*   "**Account**": Este layout é usado por páginas de login, registro e outras páginas similares. Ele é usado para as páginas sob a pasta `/Pages/Account` por padrão.
*   "**Empty**": Layout vazio e minimalista.

Esses nomes são definidos na classe `StandardLayouts` como constantes. Você pode definitivamente criar seus próprios layouts, mas esses são os nomes de layout padrão e implementados por todos os temas *out of the box*.

### Layout Location

Você pode encontrar os arquivos de layout [aqui](https://github.com/abpframework/abp/blob/dev/modules/basic-theme/src/Volo.Abp.AspNetCore.Mvc.UI.Theme.Basic/Themes/Basic/Layouts) para o tema básico. Você pode usá-los como referência para construir seus próprios layouts ou pode sobrescrevê-los se necessário.

## Veja Também

*   [Customizando a Interface do Usuário](customization-user-interface.md)
