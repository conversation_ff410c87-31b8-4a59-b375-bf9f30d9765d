[[_TOC_]]

# Projeto EntityFrameworkCore

O projeto EntityFrameworkCore é responsável pela implementação do acesso a dados usando o Entity Framework Core. Ele contém as configurações de mapeamento, implementações de repositório e o contexto do banco de dados.

## Repositórios

Os repositórios implementam o acesso a dados para cada entidade:

```csharp
using TRF3.SISPREC.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace TRF3.SISPREC.Autores;

/// <summary>
/// Implementação concreta do repositório para a entidade Autor.
/// Herda de EfCoreRepository e implementa IAutorRepository,
/// seguindo o padrão de repositório do ABP Framework.
/// </summary>
public class AutorRepository : EfCoreRepository<SISPRECDbContext, Autor>, IAutorRepository
{
    /// <summary>
    /// Inicializa uma nova instância do repositório de Autores.
    /// </summary>
    /// <param name="dbContextProvider">Provedor do contexto de banco de dados</param>
    /// <remarks>
    /// O construtor recebe o DbContextProvider via injeção de dependência,
    /// seguindo as boas práticas do ABP Framework.
    /// </remarks>
    public AutorRepository(IDbContextProvider<SISPRECDbContext> dbContextProvider) : base(dbContextProvider)
    {
    }

    /// <summary>
    /// Carrega a entidade Autor com seus relacionamentos.
    /// </summary>
    /// <returns>IQueryable com os detalhes incluídos</returns>
    /// <remarks>
    /// Este método é utilizado para carregar a entidade Autor
    /// juntamente com suas entidades relacionadas, utilizando
    /// o método IncludeDetails do ABP Framework.
    /// </remarks>
    public override async Task<IQueryable<Autor>> WithDetailsAsync()
    {
        return (await GetQueryableAsync()).IncludeDetails();
    }
}
```

**Extensions para Consultas**

As extensions facilitam a inclusão de entidades relacionadas em consultas:

```csharp
namespace TRF3.SISPREC.Autores;

/// <summary>
/// Classe de extensões para consultas Entity Framework Core relacionadas à entidade Autor.
/// Implementa o padrão Repository do ABP Framework para carregar entidades relacionadas.
/// </summary>
public static class AutorEfCoreQueryableExtensions
{
    /// <summary>
    /// Método de extensão para incluir entidades relacionadas em consultas de Autor.
    /// Utiliza o padrão Include do EF Core para eager loading de dados relacionados.
    /// </summary>
    /// <param name="queryable">IQueryable base para a consulta</param>
    /// <param name="include">Flag que determina se os detalhes devem ser incluídos</param>
    /// <returns>IQueryable com includes configurados quando necessário</returns>
    public static IQueryable<Autor> IncludeDetails(this IQueryable<Autor> queryable, bool include = true)
    {
        // Se include for false, retorna o queryable original sem modificações
        if (!include)
        {
            return queryable;
        }

        // Retorna o queryable com os includes necessários
        // Aqui serão adicionados os includes para entidades relacionadas
        return queryable
            .Include(x => x.Municipio)
            .Include(x => x.Livros);
    }
}
```

```csharp
using TRF3.SISPREC.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace TRF3.SISPREC.Livros;

/// <summary>
/// Implementação do repositório de Livros usando Entity Framework Core.
/// Esta classe herda de EfCoreRepository e implementa ILivroRepository para fornecer
/// operações de persistência específicas para a entidade Livro.
/// Namespace: TRF3.SISPREC.Livros
/// Diretório: .\src\TRF3.SISPREC.EntityFrameworkCore\Livros
/// </summary>
public class LivroRepository : EfCoreRepository<SISPRECDbContext, Livro>, ILivroRepository
{
    /// <summary>
    /// Construtor do repositório que recebe o provedor de contexto do banco de dados.
    /// </summary>
    /// <param name="dbContextProvider">Provedor do contexto do banco de dados injetado pelo ABP Framework</param>
    public LivroRepository(IDbContextProvider<SISPRECDbContext> dbContextProvider) : base(dbContextProvider)
    {
    }

    /// <summary>
    /// Sobrescreve o método WithDetailsAsync para incluir entidades relacionadas nas consultas.
    /// Este método é chamado automaticamente pelo ABP Framework quando necessário carregar
    /// dados relacionados junto com a entidade Livro.
    /// </summary>
    /// <returns>IQueryable com os includes necessários aplicados através da extensão IncludeDetails</returns>
    public override async Task<IQueryable<Livro>> WithDetailsAsync()
    {
        return (await GetQueryableAsync()).IncludeDetails();
    }
}
```

```csharp
namespace TRF3.SISPREC.Livros;

/// <summary>
/// Classe de extensões para consultas Entity Framework Core relacionadas à entidade Livro.
/// Fornece métodos de extensão para customizar consultas IQueryable para a entidade Livro.
/// Namespace: TRF3.SISPREC.Livros
/// Diretório: .\src\TRF3.SISPREC.EntityFrameworkCore\Livros
/// </summary>
public static class LivroEfCoreQueryableExtensions
{
    /// <summary>
    /// Inclui detalhes relacionados à entidade Livro na consulta.
    /// Este método é usado pelo ABP Framework para carregar automaticamente relacionamentos
    /// quando necessário, seguindo o padrão de Repository do framework.
    /// </summary>
    /// <param name="queryable">IQueryable base para a consulta</param>
    /// <param name="include">Flag que indica se os detalhes devem ser incluídos</param>
    /// <returns>IQueryable com os includes necessários aplicados</returns>
    public static IQueryable<Livro> IncludeDetails(this IQueryable<Livro> queryable, bool include = true)
    {
        if (!include)
        {
            return queryable;
        }

        return queryable
            // .Include(x => x.xxx) // TODO: AbpHelper generated
            ;
    }
}

```

## DbContext

O DbContext é a classe principal que coordena a funcionalidade do Entity Framework para um modelo de dados:

```csharp
public class SISPRECDbContext : AbpDbContext<SISPRECDbContext>, ITenantManagementDbContext
{
    public DbSet<Autor> Autors { get; set; }
    public DbSet<Livro> Livros { get; set; }

    public SISPRECDbContext(DbContextOptions<SISPRECDbContext> options)
        : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configura auditoria, background jobs, etc.
        modelBuilder.ConfigureAuditLogging();
        modelBuilder.ConfigureBackgroundJobs();
        modelBuilder.ConfigureTenantManagement();
        modelBuilder.ConfigureSettingManagement();

        // Aplica convenções de nomenclatura
        foreach (var entity in modelBuilder.Model.GetEntityTypes())
        {
            // Tabelas em maiúsculo
            entity.SetTableName(entity.GetTableName().ToUpper());

            // Colunas em maiúsculo
            foreach (var property in entity.GetProperties())
            {
                property.SetColumnName(property.Name.ToUpper());
            }
        }

        // Aplica configurações de entidades
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
    }
}
```

## Configurações de Entidades

As configurações de entidades são implementadas usando o padrão `IEntityTypeConfiguration<T>` do Entity Framework Core. Cada entidade deve ter sua própria classe de configuração.

### Convenções de Nomenclatura

#### Tabelas
- Nomes em MAIÚSCULO
- Sem abreviações ou prefixos
- Exemplos: `AUTOR`, `LIVRO`, `LIVRO_AUTOR`

#### Colunas
Prefixos de 3 caracteres indicando o tipo:
- `SEQ_`: Sequência (chave primária)
- `NOM_`: Nome
- `TXT_`: Texto
- `DAT_`: Data
- `NUM_`: Número
- `VAL_`: Valor monetário
- `SIN_`: Sinalizador (booleano)
- `TIP_`: Tipo (enums)

Nome com 6 caracteres após o prefixo.

Exemplos:
- `SEQ_AUTOR`: ID do autor
- `NOM_AUTOR`: Nome do autor
- `TXT_BIOGRA`: Biografia
- `DAT_PUBLI`: Data de publicação
- `VAL_PRECO`: Preço
- `SIN_DISPON`: Flag de disponibilidade

#### Chaves e Índices
- Chaves Primárias: `NOME_TABELA_P01`
- Chaves Estrangeiras: `NOME_TABELA_R{número}`
- Índices: `NOME_TABELA_i{número}`

### Implementação das Configurações

```csharp
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TRF3.SISPREC.Autores;
using TRF3.SISPREC.Enums;

namespace TRF3.SISPREC.Configuration
{
    /// <summary>
    /// Configuração da entidade Autor no banco de dados.
    /// Define o mapeamento da classe Autor para a tabela e colunas correspondentes,
    /// seguindo as convenções de nomenclatura estabelecidas no guia de desenvolvimento.
    /// </summary>
    public class AutorConfiguration : IEntityTypeConfiguration<Autor>
    {
        public void Configure(EntityTypeBuilder<Autor> builder)
        {
            // Define o nome da tabela no banco de dados.
            // As tabelas devem ser nomeadas em letras maiúsculas, com palavras separadas por "_" e sem abreviações ou prefixos.
            builder.ToTable("AUTOR");

            // Define a chave primária da tabela.
            // A chave primária deve ser nomeada como NOME_TABELA_P01.
            builder.HasKey(a => a.AutorId)
                .HasName("AUTOR_P01");

            // Define o nome da coluna que representa a chave primária.
            // As colunas devem seguir o padrão Prefixo(3 caracteres)_Nome(6 caracteres).
            // SEQ indica que é uma coluna de sequência (identificador).
            builder.Property(a => a.AutorId)
                .HasColumnName("SEQ_AUTOR");

            // Define o nome da coluna para o nome do autor.
            // NOM indica que é uma coluna de nome.
            builder.Property(a => a.Nome)
                .IsRequired() // Define que a coluna não pode ser nula.
                .HasMaxLength(AutorConsts.NOME_TAMANHO_MAX) // Define o tamanho máximo da string.
                .HasColumnName("NOM_AUTOR")
                .HasColumnType("varchar"); // Define o tipo da coluna como varchar.

            // Define o nome da coluna para o sobrenome do autor.
            // NOM indica que é uma coluna de nome.
            builder.Property(a => a.Sobrenome)
                .IsRequired() // Define que a coluna não pode ser nula.
                .HasMaxLength(AutorConsts.SOBRENOME_TAMANHO_MAX) // Define o tamanho máximo da string.
                .HasColumnName("NOM_SOBREN")
                .HasColumnType("varchar"); // Define o tipo da coluna como varchar.

            // Define o nome da coluna para o gênero biológico do autor.
            // TIP indica que é uma coluna de tipo.
            builder.Property(l => l.GeneroBiologico)
                .IsRequired() // Define que a coluna não pode ser nula.
                .HasMaxLength(AutorConsts.GENERO_TAMANHO_MAX) // Define o tamanho máximo da string.
                .HasConversion<string>() // Converte o enum para string.
                .HasColumnName("TIP_GENERE")
                .HasColumnType("varchar"); // Define o tipo da coluna como varchar.

            // Define o nome da coluna para a biografia do autor.
            // TXT indica que é uma coluna de texto.
            builder.Property(a => a.Biografia)
                .HasMaxLength(AutorConsts.BIOGRAFIA_TAMANHO_MAX) // Define o tamanho máximo da string.
                .HasColumnName("TXT_BIOGRA")
                .HasColumnType("varchar"); // Define o tipo da coluna como varchar.

            // Define o nome da coluna para o email do autor.
            // TXT indica que é uma coluna de texto.
            builder.Property(a => a.Email)
                .HasMaxLength(AutorConsts.EMAIL_TAMANHO_MAX) // Define o tamanho máximo da string.
                .HasColumnName("TXT_EMAIL")
                .HasColumnType("varchar"); // Define o tipo da coluna como varchar.

            // Define o nome da coluna para o telefone do autor.
            // NUM indica que é uma coluna de número.
            builder.Property(a => a.Telefone)
                .HasMaxLength(AutorConsts.TELEFONE_TAMANHO_MAX) // Define o tamanho máximo da string.
                .HasColumnName("NUM_TELEFO")
                .HasColumnType("varchar"); // Define o tipo da coluna como varchar.

            // Define o nome da coluna para o CPF do autor.
            // NUM indica que é uma coluna de número.
            builder.Property(a => a.Cpf)
                .HasMaxLength(AutorConsts.CPF_TAMANHO_MAX) // Define o tamanho máximo da string.
                .HasColumnName("NUM_CPF")
                .HasColumnType("varchar"); // Define o tipo da coluna como varchar.

            // Define o nome da coluna para o website do autor.
            // TXT indica que é uma coluna de texto.
            builder.Property(a => a.Website)
                .HasMaxLength(AutorConsts.WEBSITE_TAMANHO_MAX) // Define o tamanho máximo da string.
                .HasColumnName("TXT_SITE")
                .HasColumnType("varchar"); // Define o tipo da coluna como varchar.

            // Define o nome da coluna para o ID do município do autor.
            // SEQ indica que é uma coluna de sequência (identificador).
            builder.Property(a => a.MunicipioId)
                .HasColumnName("SEQ_MUNICI")
                .IsRequired(); // Define que a coluna não pode ser nula.

            // Define a relação de chave estrangeira com a tabela de municípios.
            // A chave estrangeira deve ser nomeada como NOME_TABELA_R{Número da FK com dois dígitos}.
            builder.HasOne(a => a.Municipio)
                .WithMany()
                .HasForeignKey(a => a.MunicipioId)
                .OnDelete(DeleteBehavior.NoAction) // Define que a exclusão em cascata não é permitida.
                .HasConstraintName("AUTOR_R01");

            // Define um índice para a coluna de CPF.
            // Os índices devem ser nomeados como NOME_TABELA_i{Número do índice com dois dígitos}.
            builder.HasIndex(a => a.Cpf)
                .HasDatabaseName("AUTOR_i01");

            // Define um índice para a coluna de email.
            // Os índices devem ser nomeados como NOME_TABELA_i{Número do índice com dois dígitos}.
            builder.HasIndex(a => a.Email)
                .HasDatabaseName("AUTOR_i02");
        }
    }
}

```

```csharp
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TRF3.SISPREC.Autores;
using TRF3.SISPREC.Livros;
using TRF3.SISPREC.Enums;

namespace TRF3.SISPREC.Configuration
{
    /// <summary>
    /// Configuração da entidade Livro no banco de dados.
    /// Define o mapeamento da tabela, colunas, chaves e relacionamentos.
    /// </summary>
    public class LivroConfiguration : IEntityTypeConfiguration<Livro>
    {
        public void Configure(EntityTypeBuilder<Livro> builder)
        {
            // Define o nome da tabela no banco de dados.
            // As tabelas devem ter nomes em maiúsculo, sem abreviações ou prefixos.
            builder.ToTable("LIVRO");

            // Define a chave primária da tabela Livro.
            // A chave primária é nomeada seguindo o padrão: NOME_TABELA_P01.
            builder.HasKey(l => l.LivroId)
                .HasName("LIVRO_P01");

            // Define o nome da coluna que mapeia a propriedade LivroId.
            // As colunas devem seguir o padrão: Prefixo(3 caracteres)_Nome(6 caracteres).
            // SEQ indica que é uma sequência (chave primária).
            builder.Property(l => l.LivroId)
                .HasColumnName("SEQ_LIVRO");

            // Define o nome da coluna que mapeia a propriedade Titulo.
            // TXT indica que é um texto.
            builder.Property(l => l.Titulo)
                .HasColumnName("TXT_TITULO")
                .IsRequired() // Define que a coluna não pode ser nula.
                .HasMaxLength(LivroConsts.TITULO_TAMANHO_MAX) // Define o tamanho máximo do texto.
                .HasColumnType("varchar"); // Define o tipo da coluna como varchar.

            // Define o nome da coluna que mapeia a propriedade Categoria.
            // TIP indica que é um tipo.
            builder.Property(l => l.Categoria)
                .HasColumnName("TIP_CATEG")
                .IsRequired() // Define que a coluna não pode ser nula.
                .HasConversion<string>() // Converte o tipo enum para string.
                .HasMaxLength(LivroConsts.CATEGORIA_TAMANHO_MAX) // Define o tamanho máximo do texto.
                .HasColumnType("varchar"); // Define o tipo da coluna como varchar.

            // Define o nome da coluna que mapeia a propriedade DataPublicacao.
            // DAT indica que é uma data.
            builder.Property(l => l.DataPublicacao)
                .HasColumnName("DAT_PUBLI")
                .IsRequired() // Define que a coluna não pode ser nula.
                .HasColumnType("date"); // Define o tipo da coluna como date.

            // Define o nome da coluna que mapeia a propriedade Preco.
            // VAL indica que é um valor.
            builder.Property(l => l.Preco)
                .HasColumnName("VAL_PRECO")
                .HasColumnType("decimal(10, 2)") // Define o tipo da coluna como decimal com 10 dígitos e 2 casas decimais.
                .IsRequired(); // Define que a coluna não pode ser nula.

            // Define o nome da coluna que mapeia a propriedade Descricao.
            // TXT indica que é um texto.
            builder.Property(l => l.Descricao)
                .HasColumnName("TXT_DESC")
                .HasMaxLength(LivroConsts.DESCRICAO_TAMANHO_MAX) // Define o tamanho máximo do texto.
                .HasColumnType("varchar"); // Define o tipo da coluna como varchar.

            // Define o nome da coluna que mapeia a propriedade Quantidade.
            // NUM indica que é um número.
            builder.Property(l => l.Quantidade)
                .HasColumnName("NUM_QUANTI")
                .IsRequired() // Define que a coluna não pode ser nula.
                .HasColumnType("int"); // Define o tipo da coluna como int.

            // Define o nome da coluna que mapeia a propriedade Disponivel.
            // SIN indica que é um sinalizador (booleano).
            builder.Property(l => l.Disponivel)
                .HasColumnName("SIN_DISPON")
                .IsRequired() // Define que a coluna não pode ser nula.
                .HasColumnType("bit"); // Define o tipo da coluna como bit.

            // Configuração do relacionamento muitos-para-muitos entre Livro e Autor.
            builder.HasMany(l => l.Autores)
                   .WithMany(a => a.Livros)
                   .UsingEntity<Dictionary<string, object>>(
                       "LIVRO_AUTOR", // Nome da tabela de junção.
                       j => j.HasOne<Autor>()
                             .WithMany()
                             .HasForeignKey("SEQ_AUTOR") // Define a FK para a tabela Autor.
                             .HasConstraintName("LIVRO_AUTOR_R01") // Nome da FK seguindo o padrão: NOME_TABELA_R{Número da FK com dois dígitos}.
                             .OnDelete(DeleteBehavior.Cascade), // Define o comportamento em caso de deleção.
                       j => j.HasOne<Livro>()
                             .WithMany()
                             .HasForeignKey("SEQ_LIVRO") // Define a FK para a tabela Livro.
                             .HasConstraintName("LIVRO_AUTOR_R02") // Nome da FK seguindo o padrão: NOME_TABELA_R{Número da FK com dois dígitos}.
                             .OnDelete(DeleteBehavior.Cascade), // Define o comportamento em caso de deleção.
                       j =>
                       {
                           // Define a chave primária da tabela de junção.
                           // A chave primária é nomeada seguindo o padrão: NOME_TABELA_P01.
                           j.HasKey("SEQ_LIVRO", "SEQ_AUTOR").HasName("LIVRO_AUTOR_P01");
                           j.ToTable("LIVRO_AUTOR"); // Define o nome da tabela de junção.
                       });
        }
    }
}

```

## Migrations

As migrations são geradas automaticamente pelo EF Core. Para mais informações, acesse a página da wiki [Migrations](/Guia-de-Desenvolvimento/Configurations-e-Migrations.md).

## Convenções de Nomenclatura

### Tabelas
- Nomes em MAIÚSCULO
- Sem abreviações ou prefixos
- Exemplos: `AUTOR`, `LIVRO`, `LIVRO_AUTOR`

### Colunas
- Prefixo de 3 caracteres indicando o tipo:
  - SEQ: Sequência (chave primária)
  - NOM: Nome
  - TXT: Texto
  - DAT: Data
  - NUM: Número
  - VAL: Valor
  - SIN: Sinalizador (booleano)
  - TIP: Tipo (enums)
- Nome com 6 caracteres
- Exemplos: `SEQ_AUTOR`, `NOM_AUTOR`, `TXT_DESC`

### Chaves e Índices
- Chaves Primárias: `NOME_TABELA_P01`
- Chaves Estrangeiras: `NOME_TABELA_R{número}`
- Índices: `NOME_TABELA_i{número}`
- Exemplos: `AUTOR_P01`, `LIVRO_AUTOR_R01`, `AUTOR_i01`




## Boas Práticas

Sempre que for implementar uma nova configuration e gerar migrations, consulte:
    - [Configurations e Migrations](/Guia-de-Desenvolvimento/Configurations-e-Migrations.md)
    - [Nomes de Tabelas e Colunas](/Guia-de-Desenvolvimento/Banco-de-Dados/Nomes-de-tabelas-e-colunas)

  

**[Próximo: Application.Contracts](/Tutorial-de-Início/Projeto-Application.Contracts)**