# Setup SISPREC-BMAD Custom Modes no RooCode

## Visão Geral

Este guia explica como configurar os **Custom Modes SISPREC-BMAD** no RooCode. O sistema utiliza custom modes específicos do projeto em formato JSON, proporcionando agentes especializados para desenvolvimento eficiente do sistema TRF3.SISPREC com ABP Framework 8, Clean Architecture e DDD.

## Conceito de Custom Modes Específicos do Projeto

O SISPREC-BMAD implementa custom modes específicos do projeto que:

1. **São específicos do workspace** - Não interferem com configurações globais
2. **Usam formato JSON** - Ao invés do YAML padrão do RooCode
3. **Têm contexto especializado** - Conhecimento profundo do domínio SISPREC
4. **Incluem regras específicas** - Validações e padrões do projeto
5. **Coordenam entre si** - Fluxos de trabalho integrados

### Vantagens do Sistema

-   **Especialização**: Cada modo otimizado para tarefas específicas do SISPREC
-   **Isolamento**: Configurações específicas do projeto, sem impacto global
-   **Eficiência**: Comandos e fluxos específicos para o domínio judiciário
-   **Qualidade**: Validações automáticas de padrões arquiteturais

## Custom Modes Implementados

### 1. 🎯 SISPREC Orchestrator (`sisprec-orchestrator`)

-   **Função**: Coordenação de equipe especializada para desenvolvimento SISPREC
-   **Acesso**: Todas as ferramentas (read, edit, command, browser)
-   **Uso**: Coordenar desenvolvimento de funcionalidades complexas

### 2. 🏗️ Arquiteto SISPREC (`sisprec-arquiteto`)

-   **Função**: Especialista em arquitetura ABP Framework 8, Clean Architecture, DDD
-   **Acesso**: Arquivos C#, JSON e Markdown + command + browser
-   **Uso**: Análise arquitetural, design de soluções, definição de padrões

### 3. ⚙️ Dev Backend SISPREC (`sisprec-backend`)

-   **Função**: Desenvolvimento backend (Domain, Application, Infrastructure, API)
-   **Acesso**: Arquivos C# e JSON + command
-   **Uso**: Implementação de camadas backend do sistema SISPREC

### 4. 🎨 Dev Frontend SISPREC (`sisprec-frontend`)

-   **Função**: Desenvolvimento frontend (Web, Razor Pages, Bootstrap)
-   **Acesso**: Arquivos web (Razor, C#, JS, CSS, JSON) + browser
-   **Uso**: Criação de interfaces de usuário, formulários, listagens

### 5. 🧪 Tester SISPREC (`sisprec-tester`)

-   **Função**: Testes automatizados em todas as camadas
-   **Acesso**: Arquivos C# e JSON de teste + command
-   **Uso**: Implementação de testes, validação de qualidade

## Configuração no RooCode

### Passo 1: Verificar Arquivos de Configuração

Os custom modes já estão configurados nos seguintes arquivos:

#### 1.1 Arquivo Principal de Configuração

```
Arquivo: .roomodes (formato JSON)
Localização: Raiz do projeto TRF3.SISPREC
Conteúdo: Definições dos 5 custom modes específicos do projeto
```

#### 1.2 Diretórios de Regras Específicas

```
.roo/rules-sisprec-orchestrator/01-sisprec-orchestrator-rules.md
.roo/rules-sisprec-arquiteto/01-sisprec-arquiteto-rules.md
.roo/rules-sisprec-backend/01-sisprec-backend-rules.md
.roo/rules-sisprec-frontend/01-sisprec-frontend-rules.md
.roo/rules-sisprec-tester/01-sisprec-tester-rules.md
```

### Passo 2: Verificar Recursos de Apoio

Os custom modes utilizam automaticamente os seguintes recursos:

#### 2.1 Base de Conhecimento

-   **Arquivo**: `sisprec-bmad-agent/data/sisprec-kb.md`
-   **Uso**: Conhecimento base integrado nos custom modes

#### 2.2 Templates

-   **Pasta**: `sisprec-bmad-agent/templates/`
-   **Arquivos**:
    -   `entity-template.md` - Template para criação de entidades
    -   `razor-page-template.md` - Template para páginas Razor
    -   `appservice-template.md` - Template para application services
    -   `test-template.md` - Template para testes automatizados
-   **Uso**: Referência automática para padronização

#### 2.3 Checklists

-   **Arquivo**: `sisprec-bmad-agent/checklists/sisprec-quality-checklist.md`
-   **Uso**: Validação automática de qualidade

#### 2.4 Tarefas

-   **Arquivo**: `sisprec-bmad-agent/tasks/create-crud-complete.md`
-   **Uso**: Guias integrados para tarefas complexas

### Passo 3: Ativação dos Custom Modes

#### 3.1 No RooCode

1. Abra o projeto TRF3.SISPREC no RooCode
2. Os custom modes aparecerão automaticamente na interface
3. Selecione o modo apropriado para sua tarefa

#### 3.2 Modos Disponíveis

-   **🎯 BMAD Orchestrator** - Para coordenação geral
-   **🏗️ Arquiteto SISPREC** - Para análise arquitetural
-   **⚙️ Dev Backend SISPREC** - Para desenvolvimento backend
-   **🎨 Dev Frontend SISPREC** - Para desenvolvimento frontend
-   **🧪 Tester SISPREC** - Para testes e qualidade

## Fluxos de Trabalho com Custom Modes

### Fluxo 1: Nova Entidade Completa (Coordenado)

1. **Iniciar**: Modo BMAD Orchestrator
2. **Comando**: `/create-crud {entidade}` ou "Criar CRUD completo para entidade {Nome}"
3. **Sequência Coordenada**:
    - **Orchestrator** analisa requisitos e cria plano
    - **Ativa Arquiteto** → Design da arquitetura e padrões
    - **Ativa Dev-Backend** → Implementação Domain/Application/Infrastructure
    - **Ativa Dev-Frontend** → Criação de interface Razor Pages
    - **Ativa Tester** → Implementação de testes automatizados
    - **Retorna Orchestrator** → Validação final e entrega

### Fluxo 2: Desenvolvimento Especializado (Direto)

1. **Seleção Direta**: Escolher modo específico no RooCode
2. **Contexto Automático**: Carregamento de regras e conhecimento específicos
3. **Trabalho Especializado**: Foco na área de expertise
4. **Coordenação**: Trocar para Orchestrator quando necessário

### Fluxo 3: Validação de Qualidade

1. **Modo**: Tester SISPREC
2. **Comando**: `/run-quality-check` ou `/validate-patterns`
3. **Execução**: Validação abrangente seguindo checklists
4. **Relatório**: Análise detalhada para revisão
5. **Refinamento**: Correções baseadas no relatório

### Fluxo 4: Análise Arquitetural

1. **Modo**: Arquiteto SISPREC
2. **Comando**: `/analyze-entity {nome}` ou `/review-architecture`
3. **Análise**: Verificação de padrões ABP/DDD
4. **Recomendações**: Sugestões de melhorias
5. **Documentação**: ADRs e especificações técnicas

## Sistema de Comandos dos Custom Modes

### Comandos Gerais (Disponíveis em Todos os Modos)

-   `/help` - Lista todos os comandos disponíveis
-   `/analyze-entity {nome}` - Análise completa de entidade
-   `/create-crud {entidade}` - CRUD completo seguindo padrões SISPREC
-   `/check-patterns` - Verificar aderência aos padrões ABP/DDD
-   `/validate-business-rules` - Verificar implementação de regras de negócio

### Comandos do BMAD Orchestrator

-   `/agents` - Mostra agentes especializados SISPREC disponíveis
-   `/review-integration {serviço}` - Revisar integrações com serviços externos
-   `/run-quality-check` - Executar checklist de qualidade abrangente
-   `/optimize-performance {área}` - Análise e otimização de performance

### Comandos do Arquiteto SISPREC

-   `/review-architecture` - Revisão da arquitetura do sistema
-   `/validate-patterns` - Validação de aderência a padrões
-   `/design-module {nome}` - Design de novo módulo ABP
-   `/design-integration {service}` - Design de arquitetura de integração

### Comandos do Dev-Backend SISPREC

-   `/implement-entity {nome}` - Implementar entidade de domínio
-   `/create-appservice {nome}` - Criar application service
-   `/implement-repository {nome}` - Implementar repositório
-   `/implement-integration {service}` - Implementar integração
-   **Nota**: Não há comando para controllers (HttpApi não é usado)

### Comandos do Dev-Frontend SISPREC

-   `/create-page {nome}` - Criar nova Razor Page
-   `/implement-form {entidade}` - Implementar formulário
-   `/create-listing {entidade}` - Criar listagem com filtros
-   `/create-modal {purpose}` - Criar modal reutilizável
-   `/implement-validation {form}` - Implementar validações

### Comandos do Tester SISPREC

-   `/create-unit-tests {classe}` - Criar testes unitários
-   `/create-integration-tests {component}` - Criar testes de integração
-   `/create-e2e-tests {workflow}` - Criar testes end-to-end
-   `/generate-coverage-report` - Gerar relatório de cobertura
-   `/run-performance-tests {scenario}` - Executar testes de performance
-   **Nota**: Não há comando para testes de API (HttpApi não é usado)

## Contexto Específico do Projeto

### Informações do Projeto

Todos os custom modes têm acesso automático ao contexto:

```
Projeto: TRF3.SISPREC
Framework: ABP Framework 8 + .NET 8
Arquitetura: Clean Architecture + DDD
Domínio: Sistema de precatórios e RPVs do TRF3
Entidades: RequisicaoProtocolo, Processo, Beneficiario, Proposta, Parcela
Integrações: CJF, SEI, MinIO, sistemas legados UFEP
```

### Regras de Negócio Críticas

### Padrões de Interface SISPREC

-   **Formulários**: Preferir normais ao invés de abp-dynamic-form, adequando o tamanho dos inputs de acordo com o Bootstrap 5 e com o tamanho do atributo.
-   **Filtros**: Devem caber em uma linha sem quebra
-   **Estilização**: Bootstrap 5 + ABP Tag Helpers (evitar CSS customizado)
-   **Permissões**: Criar permissões de visualização para controle de menu
-   **Referência**: https://abp.io/docs/8.3/framework/ui/mvc-razor-pages/tag-helpers

## Como Usar os Custom Modes

### 1. Seleção do Modo Apropriado

-   **🎯 BMAD Orchestrator**: Para coordenação geral e tarefas complexas
-   **🏗️ Arquiteto SISPREC**: Para análise arquitetural e design de soluções
-   **⚙️ Dev Backend SISPREC**: Para implementação de camadas backend
-   **🎨 Dev Frontend SISPREC**: Para desenvolvimento de interfaces
-   **🧪 Tester SISPREC**: Para testes e validação de qualidade

### 2. Fluxos Recomendados

#### Para Nova Funcionalidade Completa

1. Inicie com **BMAD Orchestrator**
2. Use comando `/create-crud {entidade}`
3. O orchestrator coordenará automaticamente os outros modos

#### Para Desenvolvimento Especializado

1. Selecione o modo específico diretamente
2. Use os comandos especializados do modo
3. Troque para Orchestrator quando precisar de coordenação

### 3. Validação de Qualidade

1. Use **Tester SISPREC** para validação
2. Execute `/run-quality-check` ou `/validate-patterns`
3. Corrija problemas identificados
4. Re-valide até aprovação completa

## Vantagens dos Custom Modes

### Especialização

-   Cada modo otimizado para tarefas específicas do SISPREC
-   Conhecimento profundo do domínio judiciário brasileiro
-   Padrões técnicos específicos do ABP Framework 8

### Eficiência

-   Comandos específicos para tarefas comuns
-   Fluxos de trabalho otimizados
-   Coordenação automática entre modos

### Qualidade

-   Validações automáticas de padrões
-   Verificações de regras de negócio específicas
-   Testes abrangentes em todas as camadas

### Isolamento

-   Configurações específicas do projeto
-   Não interferem com configurações globais do RooCode
-   Mantêm contexto do projeto SISPREC

## Troubleshooting

### Problemas Comuns

1. **Modo não aparece**: Verificar se arquivo `.roomodes` está na raiz do projeto
2. **Contexto perdido**: Verificar se diretórios `.roo/rules-{slug}/` existem
3. **Comandos não funcionam**: Verificar se está no modo correto
4. **Padrões inconsistentes**: Usar comandos de validação

### Soluções

1. **Verificar configuração**: Confirmar que arquivos estão no local correto
2. **Recarregar projeto**: Fechar e reabrir projeto no RooCode
3. **Consultar documentação**: Usar `/help` para listar comandos disponíveis
4. **Validar qualidade**: Usar Tester SISPREC para verificação

## Próximos Passos

1. **Verificar configuração** dos custom modes no RooCode
2. **Testar fluxos básicos** com tarefas simples
3. **Expandir templates** conforme necessidades do projeto
4. **Treinar equipe** nos novos fluxos de trabalho
5. **Coletar feedback** e refinar configurações

---

**Nota**: Esta configuração utiliza custom modes específicos do projeto TRF3.SISPREC em formato JSON, proporcionando agentes especializados sem interferir com configurações globais do RooCode.
