# ASP.NET Core MVC / Razor Pages: Cabeçal<PERSON> da Página

O serviço `IPageLayout` pode ser usado para definir o título da página, o item de menu selecionado e os itens de breadcrumb para uma página. É responsabilidade do [tema](Theming.md) renderizá-los na página.

## IPageLayout

`IPageLayout` pode ser injetado em qualquer página/view para definir as propriedades do cabeçalho da página.

### Título da Página

O Título da Página pode ser definido como mostrado no exemplo abaixo:

```csharp
@inject IPageLayout PageLayout
@{
    PageLayout.Content.Title = "Lista de Livros";
}
```

* O Título da Página é definido para a tag HTML `title` (além do [nome da marca/aplicativo](Branding.md)).
* O tema pode renderizar o Título da Página antes do Conteúdo da Página (não implementado pelo Tema Básico).

### Breadcrumb

> **O [Tema Básico](Basic-Theme.md) atualmente não implementa os breadcrumbs.**
>
> O [Tema LeptonX Lite](../../Themes/LeptonXLite/AspNetCore.md) suporta breadcrumbs.

Os itens de breadcrumb podem ser adicionados ao `PageLayout.Content.BreadCrumb`.

**Exemplo: Adicionar Gerenciamento de Idiomas aos itens de breadcrumb.**

```
PageLayout.Content.BreadCrumb.Add("Gerenciamento de Idiomas");
```

O tema então renderiza o breadcrumb. Um exemplo de resultado de renderização pode ser:

![breadcrumbs-example](/ABP-Docs/images/breadcrumbs-example.png)

* O ícone Home é renderizado por padrão. Defina `PageLayout.Content.BreadCrumb.ShowHome` como `false` para ocultá-lo.
* O nome da Página Atual (obtido de `PageLayout.Content.Title`) é adicionado como o último por padrão. Defina `PageLayout.Content.BreadCrumb.ShowCurrent` como `false` para ocultá-lo.

Qualquer item que você adicionar é inserido entre os itens Home e Página Atual. Você pode adicionar quantos itens precisar. O método `BreadCrumb.Add(...)` obtém três parâmetros:

* `text`: O texto a ser exibido para o item de breadcrumb.
* `url` (opcional): Um URL para navegar, se o usuário clicar no item de breadcrumb.
* `icon` (opcional): Uma classe de ícone (como `fas fa-user-tie` para Font-Awesome) para mostrar com o `text`.

### O Item de Menu Selecionado

> **O [Tema Básico](Basic-Theme.md) atualmente não implementa o item de menu selecionado, pois não é aplicável ao menu superior, que é a única opção para o Tema Básico por enquanto.**
>
> O [Tema LeptonX Lite](../../Themes/LeptonXLite/AspNetCore.md) suporta o item de menu selecionado.

Você pode definir o nome do Item de Menu relacionado a esta página:

```csharp
PageLayout.Content.MenuItemName = "BookStore.Books";
```

O nome do item de menu deve corresponder a um nome de item de menu exclusivo definido usando o sistema [Navegação / Menu](Navigation-Menu.md). Nesse caso, espera-se que o tema torne o item de menu "ativo" no menu principal.
