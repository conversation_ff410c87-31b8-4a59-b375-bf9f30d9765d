[[_TOC_]]
# Autorizações em Código Javascript

A Auth API permite verificar permissões (policies) para o usuário atual no lado do cliente. Dessa forma, você pode mostrar/ocultar partes da UI ou executar lógicas de cliente de acordo com as permissões atuais.

> Este documento explica apenas a JavaScript API. Consulte o [authorization document](https://abp.io/docs/latest/framework/fundamentals/authorization) para entender o sistema de autorização e permissão ABP.

## Uso Básico

**Observação**: os nomes dos parâmetros devem ser idênticos aos valores das permissões definidas no backend.

A função `abp.auth.isGranted(...)` é usada para checar se uma permission/policy foi concedida ou não:

````js
if (abp.auth.isGranted('DeleteUsers')) {
  //TODO: Delete the user
} else {
  alert("You don't have permission to delete a user!");
}
````

## Demais <PERSON>cionali<PERSON>

* `abp.auth.isAnyGranted(...)`: Recebe um ou mais nomes de permission/policy e retorna `true` se pelo menos uma delas foi concedida.
* `abp.auth.areAllGranted(...)`: Recebe um ou mais nomes de permission/policy e retorna `true` se todas foram concedidas.
* `abp.auth.grantedPolicies`: É um objeto cujas chaves são os nomes das permission/policy. Você pode encontrar aqui as permission/policy concedidas.


