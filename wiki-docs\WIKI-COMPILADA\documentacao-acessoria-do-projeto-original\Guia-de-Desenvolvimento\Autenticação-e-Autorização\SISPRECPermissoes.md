[[_TOC_]]
# Classe SISPRECPermissoes

## O que é e para que serve
A classe SISPRECPermissoes centraliza todas as permissões necessárias no SISPREC. Ela agrupa constantes que representam cada permissão, definindo padrões como “Visualizar” e “Gravar” para diferentes recursos.

## Como está estruturada
- Cada recurso possui uma classe estática com duas constantes: Visualizar e Gravar.
- Cada constante é registrada por meio do método privado RegistrarPermissao(...) dentro do método público RegistrarPermissoes(...).

## Quando e como usar
- Use estas permissões para aplicar políticas de autorização em Controllers e ApplicationServices.
- No CAU, crie recursos com o mesmo nome que as constantes definidas em SISPRECPermissoes. Assim, perfis podem ser atribuídos às permissões correspondentes.
- Sempre que um novo recurso for criado, basta adicionar uma propriedade na forma de uma classe estática e registrá-la no método RegistrarPermissoes(...).

## Vantagens
- Facilita a manutenção das autorizações.
- Auxilia na documentação e controle centralizado dos recursos de segurança.

## Exemplos de Código

### Exemplo de classe de permissão
```csharp
public static class ExemploRecurso
{
    public const string Visualizar = nameof(ExemploRecurso) + ".Visualizar";
    public const string Gravar = nameof(ExemploRecurso) + ".Gravar";
}
```

### Exemplo de registro de permissão
```csharp
public static void RegistrarPermissoes(AuthorizationOptions options)
{
    RegistrarPermissao(options, ExemploRecurso.Visualizar);
    RegistrarPermissao(options, ExemploRecurso.Gravar);
}
```