[[_TOC_]]
O AbpHelper é uma ferramenta opensource de linha de comando que funciona como gerador de código, que facilita a criação de arquivos no padrão e convenções do Abp Framework.

[Link para baixar AbpTools.AbpHelper.exe.7z](https://trf3jusbr.sharepoint.com/:u:/s/TRF3.SISPREC/Ea9IiHIaW2ROpKp1_Q4njUABOLNv6XqYghbqwZj6G-4MTw?e=GFVpmH).

##Pré-requisitos para geração
- É necessário que já estejam criadas as classes da entidade e sua classe de mapeamento (configuration), ambas em seus respectivos diretórios padrão do Abp Framework.
- DisplayNameAttribute na entidade
  - Colocando a anotação `[DisplayName("título da propriedade")]` nas propriedades da entidade, fará o gerador atribuir os títulos automaticamente em toda aplicação.
  - Especialmente útil para as telas, que terão as labels preenchidas automaticamente.
- Configuração das chaves primárias
  - As PKs deverão ser configuradas pelo método GetKeys, exatamente conforme exemplo abaixo (matendo a mesma sintaxe, apenas alterando ou adicionando o nome das propriedades)

``` 
       public override object[] GetKeys()
        {
            return new object[] { Seq_Dom_Unidad };
        }
```

##Crud completo
- É possível gerar um CRUD completo, incluíndo repositórios, managers, AppServices, itens do menu e as páginas.
- Exemplo de comando:
  - `.\AbpTools.AbpHelper.exe generate crud Livro -d C:\\Users\\<USER>\\wk\\TRF3.SISPREC`
  - Este comando irá gerar um CRUD completo para a entidade Livro, em um projeto Abp, cuja solution esteja no diretório `C:\Users\<USER>\wk\TRF3.SISPREC` .
  - Os parâmetros serão explicados logo a seguir.

##Serviços só de leitura
- É possível também gerar serviços e páginas só de leitura. São gerados repositórios, porém os AppServices contém somente métodos de busca/leitura, bem como as páginas geradas (somente grid e modal de detalhe).
- Exemplo de comando:
  - `.\AbpTools.AbpHelper.exe generate crud Livro -d C:\\Users\\<USER>\\wk\\TRF3.SISPREC --read-only-app-services`
  - Este comando irá gerar o repositório completo, mas o AppService só com métodos GET para a entidade Livro, em um projeto Abp, cuja solution esteja no diretório `C:\Users\<USER>\wk\TRF3.SISPREC` .

##Parâmetros da Linha de Comando
Parâmetros mais usados
- `--read-only-app-services` Serviços de aplicativo somente leitura
- `--skip-localization` Pular a geração de localização. SEMPRE usar no projeto SISPREC, pois não usamos strings parametrizadas.
- `--skip-permissions` Pular a geração de permissões. Se necessário usar, serão criadas permissões básicas de visualização, inclusão, alteração e exclusão. Ver exemplo no Crud de `MotivoAcao`
- `--skip-db-migrations` Pular a realização de migrações e atualizações no banco de dados. Usar somente em casos necessários. Como não usamos migrations no SISPREC, geralmente não será necessário.
- `--skip-ui` Pular a geração das páginas no projeto Web.
- `--no-overwrite` Especificar para não sobrescrever arquivos ou conteúdo existentes
- `--skip-domain-manager` Pular a criação e uso do DomainManager da Entidade

Demais parâmetros
- `--skip-get-list-input-dto` Pular a geração de arquivos DTO GetListInput
- `--separate-dto` Gerar arquivos DTO separados para Create e Update
- `--entity-prefix-dto` Iniciar o nome do DTO com o nome da entidade. Se esta opção for especificada, o nome do DTO seria, por exemplo, 'TodoCreateDto',caso contrário, seria -`'CreateTodoDto'
- `--dto-suffix <dto-suffix>` Usar um sufixo personalizado para o nome do DTO em vez de 'Dto'
- `--skip-custom-repository` Pular a geração da interface e classe de repositório personalizado para a entidade
- `--skip-view-model` Pular a geração de 'CreateUpdateViewModel', usar 'CreateUpdateDto' diretamente
- `--skip-test` Pular a geração de testes
- `--skip-entity-constructors` Pular a geração de construtores para a entidade

##O que é necessário ajustar
O gerador ainda está sendo aprimorado, então, após a geração, ainda é necessário fazer alguns ajustes:
- SISPRECMenuContributor:
  - O item de menu é gerado fora da hierarquia. Basta mover o código gerado para o local correto na hierarquia do menu.
- DTOs
  - Alguns DTOs talvez necessitem realizar imports. Erros no build indicarão onde.
- Testes
  - Os arquivos de testes são gerados para facilitar, porém não estão completos.
  - Erros no build indicarão onde serão necessários ajustes para deixá-los funcionais.

##Fontes
O código-fonte do gerador está disponível no repositório `https://azure-devops.trf3.jus.br/Projetos/TRF3.SISPREC/_git/AbpHelper`.

Caso alguma alteração seja necessária, pull requests podem ser feitos.



