[[_TOC_]]
# Melhores Práticas e Convenções para Domain Services

### Domains Services

- Defina os domain services na camada de domínio.  
- Crie interfaces para os domain services para facilitar criação de mocks nos testes das classes que o consomem.  
- Nem todos domain services herdam da classe BaseDomainManager, nem precisam necessáriamente herdar da classe do Abp DomainService.
- Use classe base BaseDomainManager para entidades
- Use a classe base DomainService, se precisar de atributos que essa classe fornece.
  - Use somente se necessário, pois as propriedades herdadas podem dificultar a criação de testes.
- Quando não usar o DomainService, lembre-se de declarar a herança da interface que define o ciclo de vida 
   - ITransientDependency
   - ISingletonDependency
   - IScopedDependency

Classe base DomainService:
```cs
public abstract class DomainService : IDomainService
{
    public IAbpLazyServiceProvider LazyServiceProvider { get; set; } = default!;

    [Obsolete("Use LazyServiceProvider instead.")]
    public IServiceProvider ServiceProvider { get; set; } = default!;

    protected IClock Clock => LazyServiceProvider.LazyGetRequiredService<IClock>();

    protected IGuidGenerator GuidGenerator => LazyServiceProvider.LazyGetService<IGuidGenerator>(SimpleGuidGenerator.Instance);

    protected ILoggerFactory LoggerFactory => LazyServiceProvider.LazyGetRequiredService<ILoggerFactory>();

    protected ICurrentTenant CurrentTenant => LazyServiceProvider.LazyGetRequiredService<ICurrentTenant>();

    protected IAsyncQueryableExecuter AsyncExecuter => LazyServiceProvider.LazyGetRequiredService<IAsyncQueryableExecuter>();

    protected ILogger Logger => LazyServiceProvider.LazyGetService<ILogger>(provider => LoggerFactory?.CreateLogger(GetType().FullName!) ?? NullLogger.Instance);
}

```

### Métodos de Domain Service

- Não defina métodos públicos `GET`. Métodos `GET` não alteram o estado de uma entidade. Portanto, utilize o repositório diretamente no Application Service ao invés de um método no Domain Service.

- Não retornar `DTO`. Retorne apenas objetos de domínio quando necessário.  

- Definir métodos que apenas modifiquem dados; alterem o estado de uma entidade.  

- Definir métodos com nomes autoexplicativos (como `AssignToAsync`) que implementam a lógica específica do domínio.  

- Definir apenas objetos de domínio válidos como parâmetros.  

- Não envolva lógica de usuário autenticado. Em vez disso, defina parâmetros adicionais e envie os dados relacionados do `CurrentUser` a partir da camada de Application Service.  Ex.: Dinifir parâmetro que é o nome ou id do `CurrentUser`.

# Serviços de Domínio

## Introdução

Os **Serviços de Domínio** são utilizados para implementar a lógica de negócios que não pertence a nenhuma entidade ou agregado específico. Eles fornecem uma abstração para operações que envolvem múltiplos agregados ou entidades.

## ABP Domain Service Infrastructure

Serviços de Domínio são classes simples e sem estado. Embora você não precise derivar de qualquer serviço ou interface, o ABP Framework fornece algumas classes base e convenções úteis.

### DomainService & IDomainService

Derive um Serviço de Domínio da classe base `DomainService` ou implemente diretamente a interface `IDomainService`.

**Exemplo: Criar um Serviço de Domínio derivando da classe base `DomainService`.**

````csharp
using Volo.Abp.Domain.Services;

namespace MyProject.Issues
{
    public class IssueManager : DomainService
    {
        
    }
}
````

Quando você faz isso;

* O ABP Framework registra automaticamente a classe no sistema de Injeção de Dependência com um ciclo de vida Transitório.
* Você pode usar diretamente alguns serviços comuns como propriedades base, sem precisar injetar manualmente (por exemplo, [ILogger](Logging.md) e [IGuidGenerator](Guid-Generation.md)).

> É sugerido nomear um Serviço de Domínio com o sufixo `Manager` ou `Service`. Normalmente usamos o sufixo `Manager`, como usado no exemplo acima.

**Exemplo: Implementar a lógica de domínio de atribuição de uma Issue a um Usuário**

````csharp
public class IssueManager : DomainService
{
    private readonly IRepository<Issue, Guid> _issueRepository;

    public IssueManager(IRepository<Issue, Guid> issueRepository)
    {
        _issueRepository = issueRepository;
    }
    
    public async Task AssignAsync(Issue issue, AppUser user)
    {
        var currentIssueCount = await _issueRepository
            .CountAsync(i => i.AssignedUserId == user.Id);
        
        //Implementing a core business validation
        if (currentIssueCount >= 3)
        {
            throw new IssueAssignmentException(user.UserName);
        }

        issue.AssignedUserId = user.Id;
    }    
}
````

Issue é uma [entidade](Entities.md) definida conforme mostrado abaixo:

````csharp
public class Issue : Entity<Guid>
{
    public Guid? AssignedUserId { get; internal set; }
    
    //...
}
````

* Tornar o setter `internal` garante que ele não possa ser definido diretamente nas camadas superiores e força o uso sempre do `IssueManager` para atribuir uma `Issue` a um `User`.

### Usando um Serviço de Domínio

Um Serviço de Domínio é tipicamente usado em um [serviço de aplicação](Application-Services.md).

**Exemplo: Usar o `IssueManager` para atribuir uma Issue a um Usuário**

````csharp
using System;
using System.Threading.Tasks;
using MyProject.Users;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace MyProject.Issues
{
    public class IssueAppService : ApplicationService, IIssueAppService
    {
        private readonly IssueManager _issueManager;
        private readonly IRepository<AppUser, Guid> _userRepository;
        private readonly IRepository<Issue, Guid> _issueRepository;

        public IssueAppService(
            IssueManager issueManager,
            IRepository<AppUser, Guid> userRepository,
            IRepository<Issue, Guid> issueRepository)
        {
            _issueManager = issueManager;
            _userRepository = userRepository;
            _issueRepository = issueRepository;
        }

        public async Task AssignAsync(Guid id, Guid userId)
        {
            var issue = await _issueRepository.GetAsync(id);
            var user = await _userRepository.GetAsync(userId);

            await _issueManager.AssignAsync(issue, user);
            await _issueRepository.UpdateAsync(issue);
        }
    }
}
````

Como o `IssueAppService` está na Camada de Aplicação, ele não pode atribuir diretamente uma issue a um usuário. Portanto, ele utiliza o `IssueManager`.

## Serviços de Aplicação vs Serviços de Domínio

Embora ambos, [Serviços de Aplicação](Application-Services.md) e Serviços de Domínio, implementem as regras de negócios, existem diferenças lógicas e formais fundamentais;

* Serviços de Aplicação implementam os **casos de uso** da aplicação (interações do usuário em uma aplicação web típica), enquanto Serviços de Domínio implementam a **lógica de domínio central, independente de casos de uso**.
* Serviços de Aplicação obtêm/devolvem [Objetos de Transferência de Dados](Data-Transfer-Objects.md), métodos de Serviços de Domínio tipicamente obtêm e devolvem os **objetos de domínio** ([entidades](Entities.md), [objetos de valor](Value-Objects.md)).
* Serviços de Domínio são tipicamente usados pelos Serviços de Aplicação ou outros Serviços de Domínio, enquanto Serviços de Aplicação são usados pela Camada de Apresentação ou Aplicações Clientes.

## Ciclo de Vida

O ciclo de vida dos Serviços de Domínio é [Transient](https://docs.abp.io/en/abp/latest/Dependency-Injection) e eles são registrados automaticamente no sistema de injeção de dependência, seguirem as convenções de nomenclatura e herança. Veja mais detalhes em [Injeção de Dependência](/Guia-de-Desenvolvimento/Injeção-de-Dependência.md).

## Veja Também

* [Tutorial em vídeo](https://abp.io/video-courses/essentials/domain-services)