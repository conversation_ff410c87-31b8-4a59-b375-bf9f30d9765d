[[_TOC_]]
O ISoftDelete no ABP Framework 8 é uma interface que, quando implementada por uma entidade, permite que ela seja marcada como "deletada", sem que os dados sejam removidos fisicamente do banco de dados. 

Para implementar a interface ISoftDelete, a entidade deve possuir uma propriedade IsDeleted que é definida como true quando a entidade é deletada. 

As consultas padrão do ABP Framework automaticamente excluem as entidades marcadas como deletadas, o que significa que as consultas não retornam, por padrão, os registros marcados com IsDeleted = true.

Você pode controlar a aplicação do filtro de exclusão lógica em consultas de dados utilizando o `IDataFilter<ISoftDelete>`, que permite habilitar ou desabilitar temporariamente o filtro de exclusão lógica ao realizar operações de leitura. 

Por exemplo, em um serviço, você pode injetar o repositório da entidade e o serviço `IDataFilter`, como mostrado no código abaixo:

```csharp
public class OrderService : ITransientDependency
{
    private readonly IRepository<Order, Guid> _orderRepository;
    private readonly IDataFilter _dataFilter;

    public OrderService(IRepository<Order, Guid> orderRepository, IDataFilter dataFilter)
    {
        _orderRepository = orderRepository;
        _dataFilter = dataFilter;
    }

    public async Task<List<Order>> GetAllOrders()
    {
        using (_dataFilter.Disable<ISoftDelete>())
        {
            return await _orderRepository.GetListAsync();
        }
    }
}
```

Neste exemplo, o filtro de exclusão lógica é desativado dentro do bloco `using`, permitindo que a consulta retorne tanto as entidades excluídas quanto as não excluídas. Após o término do bloco `using`, o filtro é automaticamente reativado, garantindo que futuras consultas voltem a excluir entidades marcadas como deletadas.

**Documentação Recomendada**
- https://abp.io/docs/latest/framework/infrastructure/data-filtering
- https://abp.io/docs/latest/framework/architecture/domain-driven-design/entities?_redirected=B8ABF606AA1BDF5C629883DF1061649A
- https://abp.io/docs/8.2/framework/architecture/domain-driven-design/repositories?_redirected=B8ABF606AA1BDF5C629883DF1061649A

