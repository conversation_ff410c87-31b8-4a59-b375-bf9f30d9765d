[[_TOC_]]

## Classes FilterInput

### Contexto
As classes FilterInput são utilizadas para gerenciar filtros em páginas web, podendo apresentar similaridades estruturais com outras classes do mesmo tipo em páginas de outras entidades, o que o SonarQube identifica como duplicidade. 

Caso a análise do SonarQube indique duplicidade de código em classe desse tipo, siga as instruções abaixo para refatorar e passar na análise.

### Diretrizes de Implementação

- **Localização dos Arquivos**
   - Classes FilterInput devem ser criadas em arquivos separados
   - Manter no mesmo diretório da página que as utiliza
   - Nomenclatura: `NomeDaPaginaFilterInput.cs` (mesmo nome em que já é criado no `Index.cshtml.cs`)
   - Evitar usar herança, para minimizar as chances de alterações na classe base quebrar o comportamento das classes filhas. O próprio ABP indica não reutilizar classe de formulário, tal como ocorre para ViewModels.

- **Exclusão da Análise de Duplicidade**
   - As classes FilterInput são excluídas da análise de duplicidade do SonarQube através da configuração:
     
     sonar.cpd.exclusions="**/src/TRF3.SISPREC.Web/Pages/**/*FilterInput.cs"
     
   - Esta exclusão é necessária devido à natureza similar destas classes

- **Boas Práticas**
   - Manter a classe focada apenas em propriedades de filtro
   - Evitar lógica de negócio dentro da classe FilterInput
   - Evitar usar herança
