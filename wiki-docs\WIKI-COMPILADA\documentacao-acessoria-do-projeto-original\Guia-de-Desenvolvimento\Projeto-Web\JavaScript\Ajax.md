# API JavaScript AJAX da UI para ASP.NET Core MVC / Razor Pages

A API `abp.ajax` oferece uma maneira conveniente de realizar chamadas AJAX para o servidor. Ela usa internamente o `$.ajax` do JQuery, mas automatiza algumas tarefas comuns para você:

*   **Trata e localiza automaticamente os erros** e informa o usuário (usando o [abp.message](Dialogs-Alerts.md)). Portanto, você normalmente não precisa se preocupar com erros.
*   Adiciona automaticamente o token **anti-falsificação** ao cabeçalho HTTP para satisfazer a validação de proteção CSRF no lado do servidor.
*   Define automaticamente as **opções padrão** e permite configurar os padrões em um único lugar.
*   Pode **bloquear** uma parte da UI (ou a página inteira) durante a operação AJAX.
*   Permite personalizar totalmente qualquer chamada AJAX, usando as **opções** padrão `$.ajax`.

> Embora `abp.ajax` torne a chamada AJAX bem mais fácil, você normalmente usará o sistema [Dynamic JavaScript Client Proxy](../Dynamic-JavaScript-Proxies.md) para realizar chamadas para suas APIs HTTP do lado do servidor. `abp.ajax` pode ser usado quando você precisa realizar operações AJAX de baixo nível.

## Uso Básico

`abp.ajax` aceita um objeto de opções que é aceito pelo [$.ajax](https://api.jquery.com/jquery.ajax/#jQuery-ajax-settings) padrão. Todas as opções padrão são válidas. Ele retorna uma [promise](https://api.jquery.com/category/deferred-object/) como valor de retorno.

**Exemplo: Obter a lista de usuários**

```js
abp.ajax({
  type: 'GET',
  url: '/api/identity/users'
}).then(function(result){
  console.log(result);
});
```

Este comando registra a lista de usuários no console, se você tiver **feito login** no aplicativo e tiver [permissão](../../../Authorization.md) para a página de gerenciamento de usuários do [Módulo de Identidade](../../../Modules/Identity.md).

## Tratamento de Erros

O exemplo de chamada AJAX acima mostra uma **mensagem de erro** se você não tiver feito login no aplicativo ou não tiver as permissões necessárias para realizar esta solicitação:

![ajax-error](/ABP-Docs/images/ajax-error.png)

Todos os tipos de erros são tratados automaticamente por `abp.ajax`, a menos que você queira desativá-lo.

### Resposta de Erro Padrão

`abp.ajax` é compatível com o [sistema de tratamento de exceções](../../../Exception-Handling.md) do ABP Framework e trata corretamente o formato de erro padrão retornado do servidor. Uma mensagem de erro típica é um JSON como o abaixo:

```json
{
  "error": {
    "code": "App:010042",
    "message": "Este tópico está bloqueado e não pode adicionar uma nova mensagem",
    "details": "Uma informação mais detalhada sobre o erro..."
  }
}
```

A mensagem de erro é mostrada diretamente ao usuário, usando as propriedades `message` e `details`.

### Resposta de Erro Não Padrão e Códigos de Status HTTP

Ele também trata erros mesmo que o formato de erro padrão não tenha sido enviado pelo servidor. Este pode ser o caso se você ignorar o sistema de tratamento de exceções do ABP e construir manualmente a resposta HTTP no servidor. Nesse caso, os **códigos de status HTTP** são considerados.

Os seguintes Códigos de Status HTTP são predefinidos:

*   **401**: Mostra uma mensagem de erro como "*Você deve estar autenticado (logado) para realizar esta operação*". Quando os usuários clicam no botão OK, eles são redirecionados para a página inicial do aplicativo para que façam login novamente.
*   **403**: Mostra uma mensagem de erro como "*Você não tem permissão para realizar esta operação*".
*   **404**: Mostra uma mensagem de erro como "*O recurso solicitado não pôde ser encontrado no servidor*".
*   **Outros**: Mostra uma mensagem de erro genérica como "*Ocorreu um erro. Detalhe do erro não enviado pelo servidor*".

Todas essas mensagens são localizadas com base no idioma do usuário atual.

### Tratamento Manual dos Erros

Como `abp.ajax` retorna uma promise, você sempre pode encadear uma chamada `.catch(...)` para registrar um callback que é executado se a solicitação AJAX falhar.

**Exemplo: Mostrar um alerta se a solicitação AJAX falhar**

```js
abp.ajax({
  type: 'GET',
  url: '/api/identity/users'
}).then(function(result){
  console.log(result);
}).catch(function(){
  alert("request failed :(");
});
```

Enquanto seu callback é disparado, o ABP ainda trata o erro em si. Se você quiser desativar o tratamento automático de erros, passe `abpHandleError: false` para as opções de `abp.ajax`.

**Exemplo: Desativar o tratamento automático de erros**

```js
abp.ajax({
  type: 'GET',
  url: '/api/identity/users',
  abpHandleError: false //DESATIVAR O TRATAMENTO AUTOMÁTICO DE ERROS
}).then(function(result){
  console.log(result);
}).catch(function(){
  alert("request failed :(");
});
```

Se você definir `abpHandleError: false` e não capturar o erro você mesmo, então o erro será ocultado e a solicitação falhará silenciosamente. `abp.ajax` ainda registra o erro no console do navegador (veja a seção *Configuração* para sobrescrevê-lo).

## Configuração

`abp.ajax` tem uma **configuração global** que você pode personalizar com base em seus requisitos.

### Opções AJAX Padrão

O objeto `abp.ajax.defaultOpts` é usado para configurar as opções padrão usadas ao realizar uma chamada AJAX, a menos que você as sobrescreva. O valor padrão deste objeto é mostrado abaixo:

```js
{
    dataType: 'json',
    type: 'POST',
    contentType: 'application/json',
    headers: {
        'X-Requested-With': 'XMLHttpRequest'
    }
}
```

Portanto, se você quiser alterar o tipo de solicitação padrão, você pode fazê-lo como mostrado abaixo:

```js
abp.ajax.defaultOpts.type = 'GET';
```

Escreva este código antes de todo o seu código JavaScript. Normalmente, você deseja colocar essa configuração em um arquivo JavaScript separado e adicioná-lo ao layout usando o [bundle](../Bundling-Minification.md) global.

### Registrar/Mostrar Erros

As seguintes funções podem ser sobrescritas para personalizar o registro e a exibição das mensagens de erro:

*   A função `abp.ajax.logError` registra os erros usando o [abp.log.error(...)](Logging.md) por padrão.
*   A função `abp.ajax.showError` mostra a mensagem de erro usando o [abp.message.error(...)](Dialogs-Alerts.md) por padrão.
*   `abp.ajax.handleErrorStatusCode` trata diferentes códigos de status HTTP e mostra mensagens diferentes com base no código.
*   `abp.ajax.handleAbpErrorResponse` trata os erros enviados com o formato de erro padrão do ABP.
*   `abp.ajax.handleNonAbpErrorResponse` trata as respostas de erro não padrão.
*   `abp.ajax.handleUnAuthorizedRequest` trata as respostas com código de status `401` e redireciona os usuários para a página inicial do aplicativo.

**Exemplo: Sobrescrever a função `logError`**

```js
abp.ajax.logError = function(error) {
    //...
}
```

### Outras Opções

*   A função `abp.ajax.ajaxSendHandler` é usada para interceptar as solicitações AJAX e adicionar o token antifalsificação ao cabeçalho HTTP. Observe que isso funciona para todas as solicitações AJAX, mesmo se você não usar o `abp.ajax`.
