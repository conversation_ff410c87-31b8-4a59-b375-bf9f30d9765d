[[_TOC_]]
Fonte: https://learn.microsoft.com/pt-br/dotnet/csharp/fundamentals/coding-style/identifier-names

#Pascal case
Use pascal casing ("PascalCasing") ao nomear um tipo class, interface ou delegate.
```csharp
public class DataService
{
}
```

```csharp
public delegate void DelegateType(string message);
```

```csharp
public interface IWorkerQueue
{
}
```

Ao nomear membros public de tipos, como campos, propriedades, eventos, use pascal casing. Além disso, use pascal casing para todos os métodos e funções locais.
```csharp
public class ExampleEvents
{
    // A public field, these should be used sparingly
    public bool IsValid;

    // An init-only property
    public IWorkerQueue WorkerQueue { get; init; }

    // An event
    public event Action EventProcessing;

    // Method
    public void StartEventProcessing()
    {
        // Local function
        static int CountQueueItems() => WorkerQueue.Count;
        // ...
    }
}
```
#Camel Case
Use camel casing ("camelCasing") ao nomear campos private ou internal e dê a eles o prefixo _. Use camel casing ao nomear variáveis locais, incluindo instâncias de um tipo delegado.

```csharp
public class DataService
{
    private IWorkerQueue _workerQueue;
}
```
Ao gravar os parâmetros de método, use Camel Case.


```csharp
public T SomeMethod<T>(int someNumber, bool isValid)
{
}
```

# Nomes Completos e Descritivos
Prefira sempre usar nomes completos e descritivos para variáveis, métodos e classes. Isso facilita a leitura e manutenção do código.

```csharp
// Exemplo ruim
int calc;

// Exemplo bom
int calculoTotal;
```