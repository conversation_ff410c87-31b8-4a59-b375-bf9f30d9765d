[[_TOC_]]
# **Objetivo:**
Através de arquivos HTML que são templates/layouts, permitir a geração de um arquivo PDF:


# **_Bibliotecas utilizadas:_**
- NuGet\Install-Package Scryber.Core -Version 5.0.7
- NuGet\Install-Package HtmlAgilityPack -Version 1.11.67

Exemplo:
![image.png](/.attachments/image-53f86ddd-ad4f-4cb8-be34-4a90e73fa177.png)

ao passar o mouse no botão:

![image.png](/.attachments/image-6a0285d3-f731-4469-a2a2-0daaf704a417.png)

ao clicar no botão, o PDF é baixado automaticamente:

![image.png](/.attachments/image-5ad7b19a-54cf-45f1-9534-92b872355489.png)

ao clicar para abrir o PDF, o mesmo é exibido em nova aba com as informações exportadas, referente às informações da tabela da página

![image.png](/.attachments/image-5406d993-d8be-4d7e-bbbc-48619dfa3487.png)