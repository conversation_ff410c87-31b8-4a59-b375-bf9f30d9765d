[[_TOC_]]
# Background Jobs
## Introdução

Os background jobs são essenciais para lidar com tarefas que precisam ser executadas de forma assíncrona e em segundo plano. 

Vamos abordar dois tipos de jobs:

- **Jobs Periódicos**: Esses são executados em intervalos regulares.
- **Jobs Enfileirados**: Esses são executados uma vez, geralmente acionados por eventos ou condições específicas.

## Criando Background Jobs

O framework ABP fornece uma infraestrutura robusta para gerenciar background jobs. Vamos nos aprofundar em como criar tanto jobs periódicos quanto enfileirados usando classes específicas do nosso projeto como exemplos e contexto.

### Jobs Periódicos

Os jobs periódicos são projetados para executar repetidamente em intervalos definidos. Eles são adequados para tarefas como enfileirar outros jobs, sincronização de dados, geração de relatórios ou operações de limpeza.

#### Exemplo: EnfileiraImportacaoControleRequisicaoPeriodicoJob

A classe `EnfileiraImportacaoControleRequisicaoPeriodicoJob` demonstra como configurar um job periódico. Este job enfileira periodicamente solicitações de controle de importação para processamento.

```csharp
public class EnfileiraImportacaoControleRequisicaoPeriodicoJob : SchedulingBackroundJob<EnfileiraImportacaoControleRequisicaoPeriodicoJob>, IEnfileiraImportacaoControleRequisicaoPeriodicoJob
{
    // ...
}
```

**Componentes Chave:**

- **classe base SchedulingBackroundJob**: Esta classe base fornece capacidades de agendamento usando Quartz.NET. Ela permite definir nomes de jobs, grupos e parâmetros de agendamento como atraso e intervalo.
- **Interfaces de Job**: Implementar interfaces como `IEnfileiraImportacaoControleRequisicaoPeriodicoJob` garante que possa obter o jobs em outras partes da aplicação para por exemplo, realizar a chamada do método `agendar`.

**Usando SchedulingBackroundJob:**

A classe `SchedulingBackroundJob` encapsula a lógica de agendamento. Você pode sobrescrever propriedades como `JobName`, `JobGroupName`, `DelayInSeconds` e `IntervalInSeconds` para personalizar o comportamento do job. O método `Agendar()` foi alterado para poder ser sobrescrito também, porém, sobrescreva-o somente quando for estritamente necessário alterar o comportamento, como configurações de [`MisfireInstructions`](https://www.quartz-scheduler.net/documentation/quartz-2.x/tutorial/simpletriggers.html#simpletrigger-misfire-instructions)

- **Nome e Grupo do Job**: Identificadores únicos para organizar e gerenciar jobs. Devem ser definidos em constantes.
- **Atraso e Intervalo**: Controle do intervalo de execução dos jobs e do tempo de espera para iniciar a primeira execução após agendá-lo.

```csharp
    public abstract class SchedulingBackroundJob<T> : BaseQuartzBackgroundJob where T : IJob
    {
        private readonly IScheduler _scheduler;

        public abstract string JobName { get; }
        public abstract string JobGroupName { get; }
        protected virtual int DelayInSeconds => 30;
        protected virtual int IntervalInSeconds => 40;

        public SchedulingBackroundJob(IGetLoggerService getLoggerService, IScheduler scheduler) : base(getLoggerService)
        {
            _scheduler = scheduler;
        }

        public virtual void Agendar()
        {
            var jobDetail = JobBuilder
                .Create<T>()
                .WithIdentity(JobName, JobGroupName)
                .Build();

            var trigger = TriggerBuilder.Create()
                .WithIdentity(JobName, JobGroupName)
                .StartAt(DateTimeOffset.UtcNow.AddSeconds(DelayInSeconds))
                .WithSimpleSchedule(x => x
                    .WithIntervalInSeconds(IntervalInSeconds)
                    .RepeatForever()
                    //.WithMisfireHandlingInstructionIgnoreMisfires())
                    .WithMisfireHandlingInstructionNextWithExistingCount())
                .Build();

            _scheduler.ScheduleJob(jobDetail, new List<ITrigger>() { trigger }, true).Wait();
        }
    }
```

### Jobs Enfileirados

Os jobs enfileirados são acionados para executar uma vez, tipicamente em resposta a eventos ou condições específicas. Eles são ideais para tarefas como envio de e-mails, execução assíncrona de serviços ou qualquer execução de ações específicas que não precisam ser executadas durante uma requisição.

#### Exemplo: ImportarControleRequisicoesJob

A classe `ImportarControleRequisicoesJob` exemplifica um job enfileirado que processa dados de controle de importação.

```csharp
public class ImportarControleRequisicoesJob : AsyncBackgroundJob<ImportacaoControleArgs>, IImportarControleRequisicoesJob
{
    // ...
}
```

**Componentes Chave:**

- **AsyncBackgroundJob**: Esta classe base suporta a execução assíncrona de jobs, permitindo operações não bloqueantes.
- **Argumentos do Job**: Classes de argumentos personalizadas como `ImportacaoControleArgs` permitem passar dados específicos necessários para a execução do job.

**Definindo Argumentos do Job:**

A classe `ImportacaoControleArgs` encapsula os argumentos necessários para o `ImportarControleRequisicoesJob`.

```csharp
public class ImportacaoControleArgs : BaseBackgroundJobArgs
{
    public long PropostaId { get; set; }

    public override string JobGroupName => ImportacaoRequisicoesSettings.ImportarControleRequisicoesJobGroupName;

    public ImportacaoControleArgs(long propostaId)
    {
        PropostaId = propostaId;
    }
}
```

**Tratando a Execução do Job:**

No método `ExecuteAsync`, o job processa os dados de controle de importação. 

**UnitOfWorkManager**: É necessário usar `IUnitOfWorkManager` tanto para garantir uma instância disponível do DbContext, quanto para gerenciar transações.

**CancellationToken**: Garante que operações de longa duração possam ser canceladas usando o `CancellationToken` 
- O serviço que é chamado dentro do Job deve receber o CancellationToken, exemplo: `ImportarPrecatorioAutomatizado.Executar()`
- Se o serviço a ser executado não precisa ser finalizado caso a interrupção do Job seja acionada, não é necessário usar o CancellationToken. 
- No exemplo abaixo, o método `ImportarParaControle` apenas insere um registro no banco. É uma operação rápida. Não há necessidade de cancelamento. Ao pausar o grupo de jobs de de Importação de controle, o que estiver em execução apenas finaliza, e os que estavam esperando para serem executados, serão pausados.

```csharp
public override async Task ExecuteAsync(ImportacaoControleArgs args)
{
    try
    {
        long propostaId = args.PropostaId;
        if (propostaId <= 0 || _cancelationToken.Token.IsCancellationRequested)
            return;

        using (var uow = _unitOfWorkManager.Begin(new AbpUnitOfWorkOptions(isTransactional: true, timeout: 2 * 60 * 1000), requiresNew: true))
        {
            await _controleService.ImportarParaControle(propostaId);
            await uow.CompleteAsync();
        }
    }
    catch (OperationCanceledException)
    {
        Logger.LogWarning("Job ImportarControleRequisicoesJob foi interrompido.");
    }
    catch (Exception ex)
    {
        Logger.LogError(ex, "Erro ao inserir registro de controle de importação requisição: PropostaId: {PropostaId}. Exceção: {Excecao} - Message: {Message}", args.PropostaId, nameof(ex), ex.Message);
    }
}
```

## Conceitos Fundamentais

### Usando Classes de Argumentos

Classes de argumentos como `ImportacaoControleArgs` desempenham um papel crucial na passagem de dados para os jobs. Elas fornecem uma maneira estruturada de fornecer as informações necessárias para a execução do job, permitindo melhor manutenção e testabilidade.



### Interfaces de Job

Implementar interfaces para seus jobs, como `IImportarControleRequisicoesJob`, promove um acoplamento solto e melhora a capacidade de simular ou substituir implementações de jobs durante os testes.

```csharp
public interface IImportarControleRequisicoesJob
{
    // Métodos específicos do job...
}
```

### Nomes e Grupos de Job

Definir constantes para nomes e grupos de job garante consistência em sua lógica de agendamento e facilita o gerenciamento e a referência de jobs dentro de sua aplicação.

```csharp
public override string JobName => ImportacaoRequisicoesSettings.ImportarImportacaoControleRequisicaoJobName;
public override string JobGroupName => ImportacaoRequisicoesSettings.ImportarImportacaoControleRequisicaoJobGroupName;
```

### Usando IUnitOfWorkManager

O `IUnitOfWorkManager` é essencial para gerenciar transações dentro de seus jobs. Ele garante que suas operações de job sejam executadas dentro de um contexto transacional, proporcionando consistência e confiabilidade dos dados.

```csharp
using (var uow = _unitOfWorkManager.Begin(new AbpUnitOfWorkOptions(isTransactional: true, timeout: 2 * 60 * 1000), requiresNew: true))
{
    // Lógica do job...
    await uow.CompleteAsync();
}
```

### Tokens de Cancelamento

Em jobs de longa duração como `SchedulingBackroundJob`, utilizar um `CancellationToken` permite que seus jobs tratem solicitações de cancelamento de forma elegante, evitando vazamentos de recursos e garantindo que sua aplicação permaneça responsiva.

```csharp
if (context.CancellationToken.IsCancellationRequested)
{
    Logger.LogWarning($"Job {JobName} foi interrompido.");
    await uow.CompleteAsync();
    return;
}
```

### Sobrescrevendo Parâmetros de Agendamento

Você pode sobrescrever propriedades como `DelayInSeconds` e `IntervalInSeconds` em seu `SchedulingBackroundJob` para personalizar o comportamento de agendamento de seus jobs, permitindo um agendamento de tarefas flexível e dinâmico com base nas necessidades de sua aplicação.

```csharp
protected virtual int DelayInSeconds => 30;
protected virtual int IntervalInSeconds => 40;
```

## Melhores Práticas

- **Nomenclatura Consistente**: Use nomes claros e consistentes para seus jobs e grupos para simplificar o gerenciamento e a depuração.
- **Operações Transacionais**: Certifique-se de que operações críticas dentro de seus jobs estejam envolvidas em transações para manter a integridade dos dados.
- **Cancelamento Elegante**: Implemente tokens de cancelamento para permitir que os jobs terminem de forma elegante quando necessário.
- **Implementação de Interfaces**: Sempre defina e implemente interfaces para seus jobs para facilitar testes e escalabilidade futura.
- **Validação de Argumentos**: Valide os argumentos do job para evitar erros inesperados durante a execução.

## Referências

- [Documentação do ABP sobre Background Jobs](https://abp.io/docs/8.2/framework/infrastructure/background-jobs)
- [Documentação do Quartz Scheduler](https://www.quartz-scheduler.net/features.html)
