[[_TOC_]]

# Projeto Application.Contracts

O projeto Application.Contracts contém as interfaces dos serviços de aplicação e os DTOs (Data Transfer Objects).

## DTOs (Data Transfer Objects)

Os DTOs são objetos simples usados para transferir dados entre camadas. Estes objetos devem ser serializáveis. No SISPREC, seguimos algumas práticas importantes:

1. **Validações com Data Annotations**
   - As validações devem ser implementadas quando necessário.
     - Isso garante que os dados recebidos de qualquer fonte (usuário, API, etc.) sejam validados antes de serem processados.
   - Veja exemplos de validação via Data Annotations nos DTOs de Autor e Livro.

2. **Validações Customizadas**
   - Algumas validações podem ser complexas e não podem ser feitas com Data Annotations. Para isso, implementamos a interface `IValidatableObject`.
   - Deve-se retornar mensagens de erro claras e contextualizadas
   - Veja exemplos de validação via `IValidatableObject` nos DTOs de Autor e Livro.

### DTOs de Autor

```csharp
using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Enderecos.Dtos;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.Autores.Dtos;

/// <summary>
/// DTO para transferência de dados de Autores.
/// Herda de EntityDto do ABP Framework, fornecendo uma estrutura padrão
/// para transferência de dados entre camadas da aplicação.
/// </summary>
[Serializable]
public class AutorDto : EntityDto
{
    /// <summary>
    /// Identificador único do Autor.
    /// </summary>
    [Display(Name = "AutorId")]
    public int AutorId { get; set; }

    /// <summary>
    /// Primeiro nome do Autor.
    /// </summary>
    [Display(Name = "Nome")]
    public string Nome { get; set; }

    /// <summary>
    /// Sobrenome do Autor.
    /// </summary>
    [Display(Name = "Sobrenome")]
    public string Sobrenome { get; set; }

    /// <summary>
    /// Nome completo do Autor, calculado a partir da concatenação
    /// do Nome e Sobrenome.
    /// </summary>
    [Display(Name = "Nome Completo")]
    public string NomeCompleto => $"{Nome} {Sobrenome}";

    /// <summary>
    /// Gênero biológico do Autor.
    /// </summary>
    [Display(Name = "Gênero Biológico")]
    public string GeneroBiologico { get; set; }

    /// <summary>
    /// Biografia do Autor (opcional).
    /// </summary>
    [Display(Name = "Biografia")]
    public string? Biografia { get; set; }

    /// <summary>
    /// Email de contato do Autor (opcional).
    /// </summary>
    [Display(Name = "Email")]
    public string? Email { get; set; }

    /// <summary>
    /// Telefone de contato do Autor (opcional).
    /// </summary>
    [Display(Name = "Telefone")]
    public string? Telefone { get; set; }

    /// <summary>
    /// CPF do Autor (opcional).
    /// </summary>
    [Display(Name = "CPF")]
    public string? Cpf { get; set; }

    /// <summary>
    /// Website pessoal do Autor (opcional).
    /// </summary>
    [Display(Name = "Website")]
    public string? Website { get; set; }

    /// <summary>
    /// Identificador do município de naturalidade do Autor.
    /// </summary>
    [Display(Name = "Naturalidade")]
    public int MunicipioId { get; set; }

    /// <summary>
    /// DTO do município de naturalidade do Autor.
    /// </summary>
    [Display(Name = "Naturalidade")]
    public MunicipioDto Municipio { get; set; }
}
```

```csharp
using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Apoio;
using TRF3.SISPREC.Enums;

namespace TRF3.SISPREC.Autores.Dtos;

/// <summary>
/// DTO para criação e atualização de autores.
/// Namespace: TRF3.SISPREC.Autores.Dtos
/// Diretório: .\src\TRF3.SISPREC.Application.Contracts\Autores\Dtos
/// 
/// Implementa IValidatableObject para validações customizadas de CPF e telefone.
/// As validações (Data Annotations e IValidatableObject) são executadas automaticamente 
/// pelo framework antes de chamar os métodos do Application Service,
/// garantindo que os dados já estejam validados quando chegarem na camada de aplicação.
/// 
/// Utiliza Data Annotations para validações básicas como:
/// - Required: campos obrigatórios
/// - StringLength: tamanho máximo de strings
/// - Phone: formato de telefone
/// - Url: formato de website
/// - Display: nome de exibição amigável
/// </summary>
[Serializable]
public class CreateUpdateAutorDto : IValidatableObject
{
    [Required(ErrorMessage = "O nome é obrigatório")]
    [StringLength(AutorConsts.NOME_TAMANHO_MAX, ErrorMessage = "O nome deve ter no máximo {1} caracteres")]
    [Display(Name = "Nome")]
    public string Nome { get; set; }

    [Required(ErrorMessage = "O sobrenome é obrigatório")]
    [StringLength(AutorConsts.SOBRENOME_TAMANHO_MAX, ErrorMessage = "O sobrenome deve ter no máximo {1} caracteres")]
    [Display(Name = "Sobrenome")]
    public string Sobrenome { get; set; }

    [Required(ErrorMessage = "O gênero biológico é obrigatório")]
    [Display(Name = "Gênero Biológico")]
    public EGeneroBiologico GeneroBiologico { get; set; }

    [StringLength(AutorConsts.BIOGRAFIA_TAMANHO_MAX, ErrorMessage = "A biografia deve ter no máximo {1} caracteres")]
    [Display(Name = "Biografia")]
    public string? Biografia { get; set; }

    [StringLength(AutorConsts.EMAIL_TAMANHO_MAX, ErrorMessage = "O email deve ter no máximo {1} caracteres")]
    [Display(Name = "Email")]
    public string? Email { get; set; }

    [Phone(ErrorMessage = "Telefone inválido")]
    [StringLength(AutorConsts.TELEFONE_TAMANHO_MAX, ErrorMessage = "O telefone deve ter no máximo {1} caracteres")]
    [Display(Name = "Telefone")]
    public string? Telefone { get; set; }

    [StringLength(14, ErrorMessage = "O CPF deve ter no máximo {1} caracteres")]
    [Display(Name = "CPF")]
    public string? Cpf { get; set; }

    [Url(ErrorMessage = "Website inválido")]
    [StringLength(AutorConsts.WEBSITE_TAMANHO_MAX, ErrorMessage = "O website deve ter no máximo {1} caracteres")]
    [Display(Name = "Website")]
    public string? Website { get; set; }

    [Required(ErrorMessage = "A naturalidade é obrigatória")]
    [Display(Name = "Naturalidade")]
    public int? MunicipioId { get; set; }

    /// <summary>
    /// Realiza validações customizadas no DTO de autor.
    /// Este método é chamado automaticamente pelo framework antes da execução 
    /// dos métodos do Application Service, em conjunto com as validações 
    /// definidas pelos Data Annotations.
    /// </summary>
    /// <param name="validationContext">Contexto de validação fornecido pelo framework</param>
    /// <returns>
    /// Lista de ValidationResult contendo os erros encontrados.
    /// Atual implementação valida:
    /// - CPF: verifica se é um CPF válido usando ValidadorHelper
    /// - Telefone: verifica se é um número de celular válido usando ValidadorHelper
    /// </returns>
    /// <remarks>
    /// Se qualquer validação falhar (seja dos Data Annotations ou deste método),
    /// uma exceção será lançada antes mesmo de executar o código do Application Service.
    /// As validações são realizadas apenas quando os campos opcionais são preenchidos.
    /// </remarks>
    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if (!string.IsNullOrWhiteSpace(Cpf) && !ValidadorHelper.IsCpfValido(Cpf))
        {
            yield return new ValidationResult(
                "CPF inválido!",
                new[] { nameof(Cpf) }
            );
        }

        if (!string.IsNullOrWhiteSpace(Telefone) && !ValidadorHelper.IsCelularValido(Telefone))
        {
            yield return new ValidationResult(
                "Telefone inválido!",
                new[] { nameof(Telefone) }
            );
        }
    }
}

```

```csharp
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;
using TRF3.SISPREC.Enums;

namespace TRF3.SISPREC.Autores.Dtos;

/// <summary>
/// Classe de entrada para listagem de Autores.
/// Herda de PagedAndSortedResultRequestDto do ABP Framework, fornecendo
/// suporte para paginação, ordenação e filtragem de resultados.
/// </summary>
[Serializable]
public class AutorGetListInput : PagedAndSortedResultRequestDto
{
    /// <summary>
    /// Filtro por identificador único do Autor.
    /// </summary>
    [Display(Name = "AutorId")]
    public int? AutorId { get; set; }

    /// <summary>
    /// Filtro por nome do Autor.
    /// </summary>
    [Display(Name = "Nome")]
    public string? Nome { get; set; }

    /// <summary>
    /// Filtro por sobrenome do Autor.
    /// </summary>
    [Display(Name = "Sobrenome")]
    public string? Sobrenome { get; set; }

    /// <summary>
    /// Filtro por gênero biológico do Autor.
    /// </summary>
    [Display(Name = "Gênero Biológico")]
    public EGeneroBiologico? GeneroBiologico { get; set; }

    /// <summary>
    /// Filtro por conteúdo da biografia do Autor.
    /// </summary>
    [Display(Name = "Biografia")]
    public string? Biografia { get; set; }

    /// <summary>
    /// Filtro por email de contato do Autor.
    /// </summary>
    [Display(Name = "Email")]
    public string? Email { get; set; }

    /// <summary>
    /// Filtro por telefone de contato do Autor.
    /// </summary>
    [Display(Name = "Telefone")]
    public string? Telefone { get; set; }

    /// <summary>
    /// Filtro por CPF do Autor.
    /// </summary>
    [Display(Name = "CPF")]
    public string? Cpf { get; set; }

    /// <summary>
    /// Filtro por website pessoal do Autor.
    /// </summary>
    [Display(Name = "Website")]
    public string? Website { get; set; }

    /// <summary>
    /// Filtro por identificador da naturalidade do Autor.
    /// </summary>
    [Display(Name = "Naturalidade")]
    public int? MunicipioId { get; set; }
}

```

### DTOs de Livro

```csharp
using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Autores.Dtos;
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.Livros.Dtos;

/// <summary>
/// DTO (Data Transfer Object) para leitura de dados de Livros.
/// Herda de EntityDto do ABP Framework e é usado para transferir dados 
/// da camada de aplicação para a camada de apresentação.
/// Namespace: TRF3.SISPREC.Livros.Dtos
/// Diretório: .\src\TRF3.SISPREC.Application.Contracts\Livros\Dtos
/// </summary>
/// <remarks>
/// Este DTO contém todas as propriedades necessárias para exibição de um livro:
/// - Dados básicos como título, categoria, preço
/// - Informações de estoque como quantidade e disponibilidade
/// - Lista de autores relacionados através de AutorDto
/// 
/// As validações via Data Annotations são mantidas para garantir a integridade
/// dos dados mesmo quando o DTO é usado em operações de entrada (ex: binding de formulários)
/// </remarks>
[Serializable]
public class LivroDto : EntityDto
{
    [Display(Name = "LivroId")]
    public int LivroId { get; set; }

    [Display(Name = "Título")]
    [Required(ErrorMessage = "O título é obrigatório")]
    [StringLength(LivroConsts.TITULO_TAMANHO_MAX, ErrorMessage = "O título deve ter no máximo {1} caracteres")]
    public string Titulo { get; set; } = string.Empty;

    [Display(Name = "Categoria")]
    [Required(ErrorMessage = "A categoria é obrigatória")]
    [StringLength(LivroConsts.CATEGORIA_TAMANHO_MAX, ErrorMessage = "A categoria deve ter no máximo {1} caracteres")]
    public string Categoria { get; set; } = string.Empty;

    [Display(Name = "Data de Publicação")]
    [Required(ErrorMessage = "A data de publicação é obrigatória")]
    public DateTime DataPublicacao { get; set; }

    [Display(Name = "Preço")]
    [Required(ErrorMessage = "O preço é obrigatório")]
    [Range(0.01, 99999999.99, ErrorMessage = "O preço deve estar entre {1} e {2}")]
    public decimal Preco { get; set; }

    [Display(Name = "Descrição")]
    [StringLength(LivroConsts.DESCRICAO_TAMANHO_MAX, ErrorMessage = "A descrição deve ter no máximo {1} caracteres")]
    public string? Descricao { get; set; }

    [Display(Name = "Quantidade")]
    [Required(ErrorMessage = "A quantidade é obrigatória")]
    [Range(0, int.MaxValue, ErrorMessage = "A quantidade deve ser maior ou igual a {1}")]
    public int Quantidade { get; set; }

    [Display(Name = "Disponível")]
    [Required(ErrorMessage = "A disponibilidade é obrigatória")]
    public bool Disponivel { get; set; }

    [Display(Name = "Autores")]
    public virtual List<AutorDto> Autores { get; set; } = new();
}

```


### Validações Customizadas

Validações mais complexas, que não podem ser feitas com Data Annotations, podem ser validadas implementando a interface IValidatableObject (vide método `Validate` do `CreateUpdateLivroDto`)

```csharp
using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Enums;

namespace TRF3.SISPREC.Livros.Dtos;

/// <summary>
/// DTO para criação e atualização de livros.
/// Namespace: TRF3.SISPREC.Livros.Dtos
/// Diretório: .\src\TRF3.SISPREC.Application.Contracts\Livros\Dtos
/// 
/// Implementa IValidatableObject para validação customizada da data de publicação.
/// As validações (Data Annotations e IValidatableObject) são executadas automaticamente 
/// pelo framework antes de chamar os métodos do Application Service,
/// garantindo que os dados já estejam validados quando chegarem na camada de aplicação.
/// 
/// Utiliza Data Annotations para validações básicas como:
/// - Required: campos obrigatórios
/// - StringLength: tamanho máximo de strings
/// - Range: intervalo de valores numéricos
/// - DataType: tipo específico de dados (Date, Currency)
/// - Display: nome de exibição amigável
/// </summary>
[Serializable]
public class CreateUpdateLivroDto : IValidatableObject
{
    [Required(ErrorMessage = "O título do livro é obrigatório")]
    [StringLength(LivroConsts.TITULO_TAMANHO_MAX, ErrorMessage = "O título não pode exceder {1} caracteres")]
    [Display(Name = "Titulo")]
    public string Titulo { get; set; }

    [Required(ErrorMessage = "A categoria do livro é obrigatória")]
    [Display(Name = "Categoria")]
    public ECategoriaLivro Categoria { get; set; }

    [Required(ErrorMessage = "A data de publicação é obrigatória")]
    [DataType(DataType.Date)]
    [Display(Name = "Data de Publicação")]
    public DateTime DataPublicacao { get; set; }

    [Required(ErrorMessage = "O preço é obrigatório")]
    [Range(0, 9999999.99, ErrorMessage = "O preço deve estar entre {1} e {2}")]
    [DataType(DataType.Currency)]
    [Display(Name = "Preço")]
    public decimal Preco { get; set; }

    [StringLength(LivroConsts.DESCRICAO_TAMANHO_MAX, ErrorMessage = "A descrição não pode exceder {1} caracteres")]
    [Display(Name = "Descrição")]
    public string? Descricao { get; set; }

    [Required(ErrorMessage = "A quantidade é obrigatória")]
    [Range(0, int.MaxValue, ErrorMessage = "A quantidade deve ser maior ou igual a zero")]
    [Display(Name = "Quantidade")]
    public int Quantidade { get; set; }

    [Required(ErrorMessage = "O status de disponibilidade é obrigatório")]
    [Display(Name = "Disponível")]
    public bool Disponivel { get; set; }

    [Display(Name = "Autores")]
    public List<int> AutoresIds { get; set; } = new();

    /// <summary>
    /// Realiza validações customizadas no DTO de livro.
    /// Este método é chamado automaticamente pelo framework antes da execução 
    /// dos métodos do Application Service, em conjunto com as validações 
    /// definidas pelos Data Annotations.
    /// </summary>
    /// <param name="validationContext">Contexto de validação fornecido pelo framework</param>
    /// <returns>
    /// Lista de ValidationResult contendo os erros encontrados.
    /// Atual implementação valida:
    /// - Data de Publicação: não pode ser maior que a data atual
    /// </returns>
    /// <remarks>
    /// A comparação de datas é feita usando apenas a parte da data (Date),
    /// ignorando a informação de hora para evitar falsos positivos.
    /// Se qualquer validação falhar (seja dos Data Annotations ou deste método),
    /// uma exceção será lançada antes mesmo de executar o código do Application Service.
    /// </remarks>
    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        // Comparação apenas da data, ignorando o horário
        if (DataPublicacao.Date > DateTime.Today)
        {
            yield return new ValidationResult(
                "Data de Publicação não pode ser maior que a data atual!",
                new[] { "DataPublicacao" }
            );
        }
    }
}
```

```csharp
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;
using TRF3.SISPREC.Enums;

namespace TRF3.SISPREC.Livros.Dtos;

/// <summary>
/// DTO para filtrar e paginar a listagem de livros.
/// Herda de PagedAndSortedResultRequestDto do ABP Framework para suportar
/// paginação e ordenação automáticas.
/// Namespace: TRF3.SISPREC.Livros.Dtos
/// Diretório: .\src\TRF3.SISPREC.Application.Contracts\Livros\Dtos
/// </summary>
/// <remarks>
/// Propriedades herdadas de PagedAndSortedResultRequestDto:
/// - MaxResultCount: Quantidade máxima de registros por página
/// - SkipCount: Quantidade de registros a pular (para paginação)
/// - Sorting: String de ordenação (ex: "titulo DESC, preco ASC")
/// 
/// Todas as propriedades são opcionais (nullable) para permitir
/// filtros flexíveis. Quando uma propriedade não é informada,
/// seu filtro não é aplicado na consulta.
/// </remarks>
[Serializable]
public class LivroGetListInput : PagedAndSortedResultRequestDto
{
    /// <summary>
    /// Filtro por ID do livro
    /// </summary>
    [Display(Name = "LivroId")]
    public int? LivroId { get; set; }

    /// <summary>
    /// Filtro por título do livro (busca parcial case-insensitive)
    /// </summary>
    [Display(Name = "Título")]
    public string? Titulo { get; set; }

    /// <summary>
    /// Filtro por categoria do livro
    /// </summary>
    [Display(Name = "Categoria")]
    public ECategoriaLivro? Categoria { get; set; }

    /// <summary>
    /// Filtro por data inicial de publicação (inclusive)
    /// </summary>
    [Display(Name = "Dt. Publicação Início")]
    public DateTime? DataPublicacaoInicio { get; set; }

    /// <summary>
    /// Filtro por data final de publicação (inclusive)
    /// </summary>
    [Display(Name = "Dt. Publicação Final")]
    public DateTime? DataPublicacaoFinal { get; set; }

    /// <summary>
    /// Filtro por preço mínimo (inclusive)
    /// </summary>
    [Display(Name = "Preço Mín.")]
    public decimal PrecoMin { get; set; }

    /// <summary>
    /// Filtro por preço máximo (inclusive)
    /// </summary>
    [Display(Name = "Preço Máx.")]
    public decimal PrecoMax { get; set; }

    /// <summary>
    /// Filtro por descrição do livro (busca parcial case-insensitive)
    /// </summary>
    [Display(Name = "Descrição")]
    public string? Descricao { get; set; }

    /// <summary>
    /// Filtro por quantidade em estoque
    /// </summary>
    [Display(Name = "Quantidade")]
    public int? Quantidade { get; set; }

    /// <summary>
    /// Filtro por status de disponibilidade
    /// </summary>
    [Display(Name = "Disponível")]
    public bool? Disponivel { get; set; }
}
```

## Interfaces de Serviços de Aplicação

As interfaces definem os contratos dos serviços de aplicação, especificando quais operações estão disponíveis:

```csharp
using TRF3.SISPREC.Autores.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.Autores;

/// <summary>
/// Interface que define o contrato para operações CRUD de Autores.
/// Herda de ICrudAppService do ABP Framework, fornecendo operações básicas
/// de criação, leitura, atualização e exclusão de entidades Autor.
/// </summary>
/// <typeparam name="AutorDto">DTO principal para leitura de Autores</typeparam>
/// <typeparam name="int">Tipo da chave primária da entidade Autor</typeparam>
/// <typeparam name="AutorGetListInput">Parâmetros para listagem de Autores</typeparam>
/// <typeparam name="CreateUpdateAutorDto">DTO para criação e atualização de Autores</typeparam>
public interface IAutorAppService :
    ICrudAppService<
        AutorDto,
        int,
        AutorGetListInput,
        CreateUpdateAutorDto,
        CreateUpdateAutorDto>
{
    Task<IEnumerable<AutorDto>> GetAutorPorNome(string nomeAutor);
}
```

```csharp
using TRF3.SISPREC.Livros.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.Livros;

/// <summary>
/// Interface do serviço de aplicação para gerenciamento de Livros.
/// Implementa ICrudAppService do ABP Framework para fornecer operações CRUD padrão.
/// Namespace: TRF3.SISPREC.Livros
/// Diretório: .\src\TRF3.SISPREC.Application.Contracts\Livros
/// </summary>
/// <remarks>
/// Os parâmetros genéricos do ICrudAppService são:
/// - LivroDto: DTO para leitura dos dados do livro
/// - int: Tipo do ID do livro
/// - LivroGetListInput: DTO para filtrar a listagem de livros
/// - CreateUpdateLivroDto: DTO para criar um novo livro
/// - CreateUpdateLivroDto: DTO para atualizar um livro existente (mesmo DTO usado para criar)
/// </remarks>
public interface ILivroAppService :
    ICrudAppService<
        LivroDto,
        int,
        LivroGetListInput,
        CreateUpdateLivroDto,
        CreateUpdateLivroDto>
{

}
```

**[Próximo: Application](/Tutorial-de-Início/Projeto-Application)**