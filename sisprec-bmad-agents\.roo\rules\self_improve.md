---
description: Diretrizes para melhorar continuamente as regras do Roo Code com base em padrões de código emergentes e melhores práticas.
globs: **/*
alwaysApply: true
---

- **Gatilhos para Melhoria de Regras:**
  - Novos padrões de código não cobertos pelas regras existentes
  - Implementações semelhantes repetidas em arquivos
  - Padrões de erro comuns que poderiam ser evitados
  - Novas bibliotecas ou ferramentas sendo usadas consistentemente
  - Melhores práticas emergentes na base de código

- **Processo de Análise:**
  - Comparar novo código com as regras existentes
  - Identificar padrões que devem ser padronizados
  - Procurar referências à documentação externa
  - Verificar padrões consistentes de tratamento de erros
  - Monitorar padrões de teste e cobertura

- **Atualizações de Regras:**
  - **Adicionar Novas Regras Quando:**
    - Uma nova tecnologia/padrão é usado em 3+ arquivos
    - Bugs comuns poderiam ser evitados por uma regra
    - Revisões de código (code reviews) mencionam repetidamente o mesmo feedback
    - Novos padrões de segurança ou desempenho emergem
  
  - **Modificar Regras Existentes Quando:**
    - Existem exemplos melhores na base de código
    - Casos de borda (`edge cases`) adicionais são descobertos
    - Regras relacionadas foram atualizadas
    - Detalhes de implementação mudaram

- **Exemplo de Reconhecimento de Padrão:**
  ```typescript
  // Se você vir padrões repetidos como:
  const data = await prisma.user.findMany({
    select: { id: true, email: true },
    where: { status: 'ACTIVE' }
  });
  
  // Considere adicionar a [prisma.md](mdc:.roo/rules/prisma.md):
  // - Campos de `select` padrão
  // - Condições `where` comuns
  // - Padrões de otimização de desempenho
  ```

- **Verificações de Qualidade da Regra:**
  - As regras devem ser acionáveis e específicas
  - Os exemplos devem vir do código real
  - As referências devem estar atualizadas
  - Os padrões devem ser aplicados consistentemente

- **Melhoria Contínua:**
  - Monitorar comentários de `code review`
  - Acompanhar perguntas comuns de desenvolvimento
  - Atualizar regras após grandes refatorações (`refactors`)
  - Adicionar links para documentação relevante
  - Fazer referência cruzada com regras relacionadas

- **Depreciação de Regra:**
  - Marcar padrões desatualizados como depreciados (`deprecated`)
  - Remover regras que não se aplicam mais
  - Atualizar referências a regras depreciadas
  - Documentar caminhos de migração para padrões antigos

- **Atualizações da Documentação:**
  - Manter exemplos sincronizados com o código
  - Atualizar referências a documentos externos
  - Manter links entre regras relacionadas
  - Documentar alterações que quebram a compatibilidade (`breaking changes`)
Siga [cursor_rules.md](mdc:.roo/rules/cursor_rules.md) para formatação e estrutura de regras adequadas.
