# Security Headers

O ABP permite que você adicione headers de segurança frequentemente utilizados em sua aplicação. Os seguintes headers de segurança serão adicionados como response headers à sua aplicação se você utilizar o middleware `UseAbpSecurityHeaders`:

*   `X-Content-Type-Options`: Diz ao navegador para não tentar adivinhar qual poderia ser o mime-type de um recurso e para apenas aceitar o mime-type que o servidor retornou.
*   `X-XSS-Protection`: Este é um recurso do Internet Explorer, Chrome e Safari que impede que páginas sejam carregadas quando detectam ataques de cross-site scripting (XSS) refletidos.
*   `X-Frame-Options`: Este header pode ser usado para indicar se um navegador deve ou não ter permissão para renderizar uma página em uma tag `<iframe>`. Ao especificar o valor deste header como *SAMEORIGIN*, você pode fazer com que ele seja exibido em um frame na mesma origem da própria página.
*   `Content-Security-Policy`: Este response header permite que você restrinja quais recursos (como JavaScript, CSS, imagens, manifests, etc.) podem ser carregados e os URLs de onde eles podem ser carregados. Este header de segurança só será adicionado se você configurar a classe `AbpSecurityHeadersOptions` e habilitá-lo.

## Configuração

### AbpSecurityHeadersOptions

`AbpSecurityHeadersOptions` é a classe principal para habilitar o header `Content-Security-Policy`, definir seu valor e definir outros headers de segurança que você deseja adicionar à sua aplicação.

**Exemplo:**

```csharp
Configure<AbpSecurityHeadersOptions>(options => 
{
    options.UseContentSecurityPolicyHeader = true; //false por padrão
    options.ContentSecurityPolicyValue = "object-src 'none'; form-action 'self'; frame-ancestors 'none'"; //valor padrão

    //adicionando headers de segurança adicionais
    options.Headers["Referrer-Policy"] = "no-referrer";
});
```

> Se o header for o mesmo, os headers de segurança adicionais que você definiu têm precedência sobre os headers de segurança padrão. Em outras palavras, ele sobrescreve os valores dos headers de segurança padrão.

## Security Headers Middleware

O middleware Security Headers é um middleware de pipeline de requisição do ASP.NET Core que adiciona headers de segurança predefinidos à sua aplicação, incluindo `X-Content-Type-Options`, `X-XSS-Protection` e `X-Frame-Options`. Além disso, este middleware também inclui esses headers de segurança únicos em sua aplicação se você configurar o `AbpSecurityHeadersOptions` conforme mencionado acima.

**Exemplo:**

```csharp
app.UseAbpSecurityHeaders();
```

> Você pode adicionar este middleware após `app.UseRouting()` no método `OnApplicationInitialization` da sua classe de módulo para registrá-lo no pipeline de requisição. Este middleware já está configurado nos [Templates de Inicialização do ABP Commercial](../../../solution-templates/index.md), portanto, você não precisa adicioná-lo manualmente se estiver usando um desses templates de inicialização.

Depois disso, você registrou o middleware `UseAbpSecurityHeaders` no pipeline de requisição, os headers de segurança definidos serão exibidos nos response headers como na figura abaixo:

![](/ABP-Docs/images/security-response-headers.png)

## Content Security Policy Script Nonce

O ABP fornece uma propriedade para adicionar um valor nonce dinâmico de script-src ao header Content-Security-Policy. Com este recurso, ele adiciona automaticamente um valor nonce dinâmico ao lado do header. E com a ajuda do tag helper de script, ele adiciona este valor [`script nonce`](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/nonce) às tags de script em suas páginas (O `ScriptNonceTagHelper` no namespace `Volo.Abp.AspNetCore.Mvc.UI.Bundling` deve ser anexado como um taghelper.).
> Se você precisar adicionar o script nonce manualmente, pode usar 'Html.GetScriptNonce()' para adicionar o valor nonce ou 'Html.GetScriptNonceAttribute()' para adicionar o valor do atributo nonce.

Este recurso está desabilitado por padrão. Você pode habilitá-lo definindo a propriedade `UseContentSecurityPolicyScriptNonce` da classe `AbpSecurityHeadersOptions` como `true`.

### Ignorar Script Nonce

Você pode ignorar o script nonce para algumas páginas ou alguns seletores. Você pode usar as propriedades `IgnoredScriptNoncePaths` e `IgnoredScriptNonceSelectors` da classe `AbpSecurityHeadersOptions`.

**Exemplo:**

```csharp
Configure<AbpSecurityHeadersOptions>(options => 
{
    //adicionando script-src nonce
    options.UseContentSecurityPolicyScriptNonce = true; //false por padrão

    //ignorar a fonte nonce de script para esses paths
    options.IgnoredScriptNoncePaths.Add("/my-page");

    //ignorar script nonce por Elsa Workflows e outros seletores
    options.IgnoredScriptNonceSelectors.Add(context =>
    {
        var endpoint = context.GetEndpoint();
        return Task.FromResult(endpoint?.Metadata.GetMetadata<PageRouteMetadata>()?.RouteTemplate == "/{YOURHOSTPAGE}");
    });
});
```

### Ignorar Abp Security Headers

Você pode ignorar os Abp Security Headers para algumas actions ou páginas. Você pode usar o atributo `IgnoreAbpSecurityHeaderAttribute` para isso.

**Exemplo:**

```csharp
@using Volo.Abp.AspNetCore.Security
@attribute [IgnoreAbpSecurityHeaderAttribute]
```

**Exemplo:**

```csharp
[IgnoreAbpSecurityHeaderAttribute]
public class IndexModel : AbpPageModel
{
    public void OnGet()
    {
    }
}
```
