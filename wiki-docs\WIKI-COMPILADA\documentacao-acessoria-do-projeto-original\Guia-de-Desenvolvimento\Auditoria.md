[[_TOC_]]

# Auditoria
Por padrão todas entidades devem ser auditadas, isto é, salvar o histórico de alterações. Exceto entidades que não são de negócio, por exemplo, entidade de controle de processamento, controle de importação, etc.

## Habilitando Auditoria na Entidade
Para habilitar a auditoria em uma entidade, é necessário adicionar a anotação `[Audited]` na classe:
````csharp
    [Audited]
    public class Proposta : Entity, ISoftDelete
````

## Visualizando Registros de Auditoria
Para visualizar os registros de auditoria de uma entidade, basta acessar o modal de detalhe do registro da entidade e acionar a aba "Histórico".

Para implementar a visualização dos registros de auditoria em seu modal de detalhe:

1. No arquivo .cshtml do modal, utilize o componente `abp-tabs` para criar as abas:
```csharp
<abp-modal>
    <abp-modal-header title="Título do Modal"></abp-modal-header>
    <abp-modal-body>
        <abp-tabs>
            <!-- Aba de detalhes da entidade -->
            <abp-tab title="Detalhe">
                <!-- Coloque aqui os campos do seu formulário -->
                <abp-input asp-for="ViewModel.Campo1" readonly="true" />
                <abp-input asp-for="ViewModel.Campo2" readonly="true" />
            </abp-tab>

            <!-- Aba do histórico de auditoria -->
            <abp-tab title="Histórico">
                @await Component.InvokeAsync("Historico", new { 
                    nomeEntidade = typeof(SuaEntidade).FullName!, 
                    idEntidade = Model.ViewModel.IdEntidade.ToString() 
                })
            </abp-tab>
        </abp-tabs>
    </abp-modal-body>
    <abp-modal-footer buttons="@(AbpModalButtons.Close)"></abp-modal-footer>
</abp-modal>
```

Onde:
- `SuaEntidade` é o nome da sua classe de domínio
- `IdEntidade` é a propriedade do seu ViewModel que contém o Id do registro

O componente `Historico` irá automaticamente buscar e exibir todas as alterações registradas para a entidade especificada.


## AuditedEfCoreRepository
A classe `AuditedEfCoreRepository` é uma classe base especial para repositórios que precisam garantir que todas as operações realizadas através dele sejam auditadas, independente do contexto de execução.

### Quando Usar
Use o `AuditedEfCoreRepository` quando:
- Precisar garantir que TODAS as alterações na entidade sejam auditadas, mesmo fora do escopo de Application Services
  - Por padrão, a auditoria por meio da anotação `[Audited]` é feita apenas em Application Services
- Estiver trabalhando com Background Jobs que alteram entidades auditáveis
- Tiver operações diretas no repositório que precisam ser auditadas
- Precisar garantir auditoria em qualquer parte do código que altere a entidade

### Escopo de Auditoria
Por padrão, o ABP Framework cria automaticamente o escopo de auditoria apenas dentro de Application Services. Em outros contextos (como Background Jobs, Domain Services, etc), você precisa criar o escopo manualmente ou usar o `AuditedEfCoreRepository`, que já faz isso pra você.

### Observações Importantes

#### Cuidado com Logs Excessivos
⚠️ **Atenção**: Em operações que manipulam grande volume de dados (como importações de registros de histórico), o uso do `AuditedEfCoreRepository` pode gerar uma quantidade excessiva de logs de auditoria. Nesses casos, considere:
- Desabilitar temporariamente a auditoria para entidades específicas
- Criar um mecanismo de limpeza periódica dos logs

#### Propriedades Virtuais e [DisableAuditing]
⚠️ **Importante**: Em entidades com a anotação `[Audited]`, é necessário adicionar a anotação `[DisableAuditing]` em todas as propriedades virtuais de navegação. Isso evita:
- Logs desnecessários de entidades relacionadas
- Problemas de performance ao carregar propriedades de navegação
- Possíveis loops infinitos na auditoria

Exemplo:
```csharp
[Audited]
public class Requisicao : Entity
{
    public string Numero { get; set; }
    
    [DisableAuditing]
    public virtual UnidadeJudicial UnidadeJudicial { get; set; }
}
