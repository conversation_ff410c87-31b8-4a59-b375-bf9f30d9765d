# Template: Testes Automatizados SISPREC

## Informações dos Testes

- **Componente**: {NomeComponente}
- **Tipo de Teste**: {TipoTeste} (Unit | Integration | EndToEnd)
- **Camada**: {Camada} (Domain | Application | Infrastructure | Web)
- **Cobertura Esperada**: {CoberturaEsperada}%

## 1. Testes de Unidade - Domain Layer

### Arquivo: `test/TRF3.SISPREC.Domain.Tests/{Modulo}/{NomeEntidade}Tests.cs`

```csharp
using System;
using Shouldly;
using TRF3.SISPREC.{Modulo};
using Volo.Abp;
using Xunit;
using NSubstitute;
using Bogus;

namespace TRF3.SISPREC.{Modulo}
{
    public class {NomeEntidade}Tests : SISPRECDomainTestBase<SISPRECTestBaseModule>
    {
        [Fact]
        public void Deve_Criar_{NomeEntidade}_Com_Dados_Validos()
        {
            // Arrange
            var nome = new Faker().Random.Hash(); // Usar Hash() ao invés de String()
            var tipo = {NomeEntidade}Tipo.Padrao;
            var descricao = new Faker().Random.Hash();

            // Act
            var entity = new {NomeEntidade}(nome, tipo, descricao);

            // Assert
            entity.Nome.ShouldBe(nome);
            entity.Tipo.ShouldBe(tipo);
            entity.Descricao.ShouldBe(descricao);
            entity.Status.ShouldBe({NomeEntidade}Status.Ativo);
            entity.Id.ShouldNotBe(Guid.Empty);
        }

        [Fact]
        public void Deve_Lancar_Excecao_Com_Nome_Vazio()
        {
            // Arrange & Act & Assert
            Should.Throw<ArgumentException>(() =>
                new {NomeEntidade}("", {NomeEntidade}Tipo.Padrao));
        }

        [Fact]
        public void Deve_Ativar_{NomeEntidade}_Corretamente()
        {
            // Arrange
            var entity = Criar{NomeEntidade}Valida();
            entity.Desativar();

            // Act
            entity.Ativar();

            // Assert
            entity.Status.ShouldBe({NomeEntidade}Status.Ativo);
        }

        [Fact]
        public void Deve_Validar_Regras_Negocio_Especificas_SISPREC()
        {
            // Arrange
            var entity = Criar{NomeEntidade}Valida();

            // Act & Assert - Regra específica do SISPREC
            Should.Throw<UserFriendlyException>(() =>
                entity.ProcessarSemValidacao())
                .Message.ShouldContain("deve estar finalizada");
        }

        private {NomeEntidade} Criar{NomeEntidade}Valida()
        {
            return new {NomeEntidade}(
                new Faker().Random.Hash(),
                {NomeEntidade}Tipo.Padrao,
                new Faker().Random.Hash()
            );
        }
    }
}
```

## 2. Testes de Application Services

### Arquivo: `test/TRF3.SISPREC.EntityFrameworkCore.Tests/EntityFrameworkCore/Applications/{Modulo}/{NomeEntidade}AppServiceTests.cs`

```csharp
using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using TRF3.SISPREC.{Modulo};
using TRF3.SISPREC.{Modulo}.Dtos;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Xunit;
using Bogus;

namespace TRF3.SISPREC.{Modulo}
{
    public class {NomeEntidade}AppServiceTests : BaseAppServiceTests<SISPRECEntityFrameworkCoreTestModule>
    {
        private readonly I{NomeEntidade}AppService _appService;
        private readonly I{NomeEntidade}Repository _repository;

        public {NomeEntidade}AppServiceTests()
        {
            _appService = GetRequiredService<I{NomeEntidade}AppService>();
            _repository = GetRequiredService<I{NomeEntidade}Repository>();

            // IMPORTANTE: Sempre usar .Wait() após WithUnitOfWorkAsync no construtor
            WithUnitOfWorkAsync(async () =>
            {
                // Inserir dados de teste se necessário
                // var entity = new {NomeEntidade}(new Faker().Random.Hash(), {NomeEntidade}Tipo.Padrao);
                // await _repository.InsertAsync(entity, autoSave: true);
            })
            .Wait(); // Garante que erros na inserção sejam lançados imediatamente
        }

        [Fact]
        public async Task Deve_Criar_{NomeEntidade}_Com_Sucesso()
        {
            // Arrange
            var input = new Create{NomeEntidade}Dto
            {
                Nome = new Faker().Random.Hash(), // Usar Hash() para strings
                Tipo = {NomeEntidade}Tipo.Padrao,
                Descricao = new Faker().Random.Hash()
            };

            // Act
            var result = await _appService.CreateAsync(input);

            // Assert
            result.ShouldNotBeNull();
            result.Nome.ShouldBe(input.Nome);
            result.Tipo.ShouldBe(input.Tipo);
            result.Status.ShouldBe({NomeEntidade}Status.Ativo);

            // Verificar se foi salvo no banco
            var entity = await _repository.GetAsync(result.Id);
            entity.ShouldNotBeNull();
            entity.Nome.ShouldBe(input.Nome);
        }

        [Fact]
        public async Task Deve_Lancar_Excecao_Ao_Criar_Com_Nome_Duplicado()
        {
            // Arrange
            await CriarEntidadeAsync(new Faker().Random.Hash());

            var nomeDuplicado = new Faker().Random.Hash();
            await CriarEntidadeAsync(nomeDuplicado);

            var input = new Create{NomeEntidade}Dto
            {
                Nome = nomeDuplicado,
                Tipo = {NomeEntidade}Tipo.Padrao
            };

            // Act & Assert
            var exception = await Should.ThrowAsync<UserFriendlyException>(
                () => _appService.CreateAsync(input)
            );

            exception.Message.ShouldContain("já existe");
        }

        [Fact]
        public async Task Deve_Atualizar_{NomeEntidade}_Com_Sucesso()
        {
            // Arrange
            var entity = await CriarEntidadeAsync();
            var input = new Update{NomeEntidade}Dto
            {
                Nome = new Faker().Random.Hash(), // Usar Hash() para strings
                Tipo = {NomeEntidade}Tipo.Especial,
                Descricao = new Faker().Random.Hash()
            };

            // Act
            var result = await _appService.UpdateAsync(entity.Id, input);

            // Assert
            result.Nome.ShouldBe(input.Nome);
            result.Tipo.ShouldBe(input.Tipo);
            result.Descricao.ShouldBe(input.Descricao);
        }

        [Fact]
        public async Task Deve_Ativar_{NomeEntidade}_Com_Sucesso()
        {
            // Arrange
            var entity = await CriarEntidadeAsync();
            entity.Desativar();
            await _repository.UpdateAsync(entity);

            // Act
            var result = await _appService.AtivarAsync(entity.Id);

            // Assert
            result.Status.ShouldBe({NomeEntidade}Status.Ativo);
        }

        [Fact]
        public async Task Deve_Processar_{NomeEntidade}_Seguindo_Regras_SISPREC()
        {
            // Arrange
            var entity = await CriarEntidadeAsync();
            var input = new Processar{NomeEntidade}Dto
            {
                Observacao = "Processamento de teste",
                ValidarAutomaticamente = true
            };

            // Act
            var result = await _appService.ProcessarAsync(entity.Id, input);

            // Assert
            result.ShouldNotBeNull();
            // Verificar regras específicas do SISPREC
        }

        [Fact]
        public async Task Deve_Retornar_Lista_Paginada()
        {
            // Arrange
            await CriarMultiplasEntidadesAsync(5);
            var input = new Get{NomeEntidade}ListDto
            {
                MaxResultCount = 3,
                SkipCount = 0
            };

            // Act
            var result = await _appService.GetListAsync(input);

            // Assert
            result.ShouldNotBeNull();
            result.Items.Count.ShouldBeLessThanOrEqualTo(3);
            result.TotalCount.ShouldBeGreaterThanOrEqualTo(5);
        }

        [Fact]
        public async Task Deve_Filtrar_Por_Status()
        {
            // Arrange
            await CriarEntidadeComStatusAsync({NomeEntidade}Status.Ativo);
            await CriarEntidadeComStatusAsync({NomeEntidade}Status.Inativo);

            // Act
            var result = await _appService.GetByStatusAsync({NomeEntidade}Status.Ativo);

            // Assert
            result.Items.ShouldAllBe(x => x.Status == {NomeEntidade}Status.Ativo);
        }

        // Métodos auxiliares
        private async Task<{NomeEntidade}> CriarEntidadeAsync(string nome = null)
        {
            var nomeEntidade = nome ?? new Faker().Random.Hash(); // Usar Hash() para strings
            var entity = new {NomeEntidade}(nomeEntidade, {NomeEntidade}Tipo.Padrao);
            return await _repository.InsertAsync(entity, autoSave: true);
        }

        private async Task CriarMultiplasEntidadesAsync(int quantidade)
        {
            for (int i = 1; i <= quantidade; i++)
            {
                await CriarEntidadeAsync(new Faker().Random.Hash());
            }
        }

        private async Task<{NomeEntidade}> CriarEntidadeComStatusAsync({NomeEntidade}Status status)
        {
            var entity = await CriarEntidadeAsync();
            if (status == {NomeEntidade}Status.Inativo)
            {
                entity.Desativar();
                await _repository.UpdateAsync(entity, autoSave: true);
            }
            return entity;
        }
    }
}
```

## 3. Testes de Integração - Repository

### Arquivo: `test/TRF3.SISPREC.EntityFrameworkCore.Tests/EntityFrameworkCore/{Modulo}/{NomeEntidade}RepositoryTests.cs`

```csharp
using System.Linq;
using System.Threading.Tasks;
using Shouldly;
using TRF3.SISPREC.{Modulo};
using Xunit;
using Bogus;

namespace TRF3.SISPREC.{Modulo}
{
    public class {NomeEntidade}RepositoryTests : SISPRECEntityFrameworkCoreTestBase
    {
        private readonly I{NomeEntidade}Repository _repository;

        public {NomeEntidade}RepositoryTests()
        {
            _repository = GetRequiredService<I{NomeEntidade}Repository>();

            // IMPORTANTE: Sempre usar .Wait() após WithUnitOfWorkAsync no construtor
            WithUnitOfWorkAsync(async () =>
            {
                // Inserir dados de teste se necessário
                // var entity = new {NomeEntidade}(new Faker().Random.Hash(), {NomeEntidade}Tipo.Padrao);
                // await _repository.InsertAsync(entity, autoSave: true);
            })
            .Wait(); // Garante que erros na inserção sejam lançados imediatamente
        }

        [Fact]
        public async Task Deve_Buscar_Por_Nome()
        {
            // Arrange
            var entity = await CriarEntidadeAsync("Nome Específico");

            // Act
            var result = await _repository.FindByNomeAsync("Nome Específico");

            // Assert
            result.ShouldNotBeNull();
            result.Id.ShouldBe(entity.Id);
        }

        [Fact]
        public async Task Deve_Buscar_Por_Status()
        {
            // Arrange
            await CriarEntidadeComStatusAsync({NomeEntidade}Status.Ativo);
            await CriarEntidadeComStatusAsync({NomeEntidade}Status.Inativo);

            // Act
            var result = await _repository.GetListAsync(status: {NomeEntidade}Status.Ativo);

            // Assert
            result.ShouldAllBe(x => x.Status == {NomeEntidade}Status.Ativo);
        }

        [Fact]
        public async Task Deve_Incluir_Relacionamentos()
        {
            // Arrange
            var entity = await CriarEntidadeComRelacionamentosAsync();

            // Act
            var result = await _repository.GetWithDetailsAsync(entity.Id);

            // Assert
            result.ShouldNotBeNull();
            result.{EntidadeRelacionada}.ShouldNotBeNull();
        }

        [Fact]
        public async Task Deve_Paginar_Resultados()
        {
            // Arrange
            await CriarMultiplasEntidadesAsync(10);

            // Act
            var result = await _repository.GetListAsync(
                maxResultCount: 5,
                skipCount: 3
            );

            // Assert
            result.Count.ShouldBe(5);
        }

        private async Task<{NomeEntidade}> CriarEntidadeAsync(string nome = null)
        {
            var nomeEntidade = nome ?? new Faker().Random.Hash(); // Usar Hash() para strings
            var entity = new {NomeEntidade}(nomeEntidade, {NomeEntidade}Tipo.Padrao);
            return await _repository.InsertAsync(entity, autoSave: true);
        }

        private async Task<{NomeEntidade}> CriarEntidadeComStatusAsync({NomeEntidade}Status status)
        {
            var entity = await CriarEntidadeAsync();
            if (status == {NomeEntidade}Status.Inativo)
            {
                entity.Desativar();
                await _repository.UpdateAsync(entity, autoSave: true);
            }
            return entity;
        }

        private async Task<{NomeEntidade}> CriarEntidadeComRelacionamentosAsync()
        {
            // Implementar conforme relacionamentos específicos
            var entity = await CriarEntidadeAsync();
            // Adicionar relacionamentos
            return entity;
        }

        private async Task CriarMultiplasEntidadesAsync(int quantidade)
        {
            for (int i = 1; i <= quantidade; i++)
            {
                await CriarEntidadeAsync(new Faker().Random.Hash());
            }
        }
    }
}
```

## 4. Testes End-to-End - Web

### Arquivo: `test/TRF3.SISPREC.Web.Tests/Pages/{Modulo}/{NomeEntidade}WebTests.cs`

```csharp
using System.Threading.Tasks;
using HtmlAgilityPack;
using Shouldly;
using Xunit;
using Bogus;

namespace TRF3.SISPREC.{Modulo}
{
    public class {NomeEntidade}WebTests : SISPRECWebTestBase
    {
        private readonly {NomeEntidade} {nomeEntidade}Obj;

        public {NomeEntidade}WebTests()
        {
            // IMPORTANTE: Sempre usar .Wait() e autoSave: true no construtor
            WithUnitOfWorkAsync(async () =>
            {
                var faker = new Faker<{NomeEntidade}>()
                    .RuleFor(e => e.Nome, f => f.Random.Hash()) // Usar Hash() para strings
                    .RuleFor(e => e.Tipo, f => {NomeEntidade}Tipo.Padrao)
                    .RuleFor(e => e.Descricao, f => f.Random.Hash());

                {nomeEntidade}Obj = faker.Generate();
                var repository = GetRequiredService<I{NomeEntidade}Repository>();
                await repository.InsertAsync({nomeEntidade}Obj, autoSave: true);
            })
            .Wait(); // Garante que erros na inserção sejam lançados imediatamente
        }

        [Fact]
        public async Task Index_Page_Test()
        {
            // Arrange
            string url = "/{Modulo}/{NomeEntidade}";

            // Act
            var response = await GetResponseAsync(url);
            var responseString = await GetResponseAsStringAsync(url);

            // Assert
            response.ShouldNotBeNull();
            responseString.ShouldNotBeNull();
            response.StatusCode.ShouldBe(System.Net.HttpStatusCode.OK);

            var htmlDocument = new HtmlDocument();
            htmlDocument.LoadHtml(responseString);

            var tableElement = htmlDocument.GetElementbyId("{NomeEntidade}Table");
            tableElement.ShouldNotBeNull();
        }

        [Fact]
        public async Task Deve_Filtrar_Lista()
        {
            // Arrange
            await LoginAsAdminAsync();
            await CriarDadosTesteAsync();

            // Act
            await Page.GotoAsync("/{Modulo}/{NomeEntidade}");
            await Page.FillAsync("#filtro-nome", "Específico");
            await Page.ClickAsync("#btn-filtrar");

            // Assert
            await Expect(Page.Locator("table tbody tr")).ToHaveCountAsync(1);
        }

        [Fact]
        public async Task Deve_Validar_Campos_Obrigatorios()
        {
            // Arrange
            await LoginAsAdminAsync();

            // Act
            await Page.GotoAsync("/{Modulo}/{NomeEntidade}");
            await Page.ClickAsync("[data-test='btn-novo']");
            await Page.ClickAsync("[data-test='btn-salvar']");

            // Assert
            await Expect(Page.Locator(".field-validation-error")).ToBeVisibleAsync();
        }

        private async Task CriarDadosTesteAsync()
        {
            // Implementar criação de dados de teste via API ou diretamente no banco
        }
    }
}
```

## Checklist de Implementação

### Estrutura de Testes
- [ ] Testes de unidade para Domain layer
- [ ] Testes de Application Services
- [ ] Testes de integração para Repositories
- [ ] Testes end-to-end para interface web

### Cobertura e Qualidade
- [ ] Cobertura mínima atingida (Domain: 90%, Application: 85%)
- [ ] Testes de cenários positivos e negativos
- [ ] Testes de validações de negócio
- [ ] Testes de regras específicas SISPREC

### Ferramentas e Configuração SISPREC
- [ ] xUnit configurado (framework oficial)
- [ ] NSubstitute para mocking (NÃO usar Moq)
- [ ] Shouldly para assertions (NÃO usar FluentAssertions)
- [ ] Bogus para geração de dados (usar Random.Hash() para strings)
- [ ] SQLite para testes de integração
- [ ] HtmlAgilityPack para testes web

### Estrutura de Projetos SISPREC
- [ ] Domain Tests: `test/TRF3.SISPREC.Domain.Tests/`
- [ ] Application Tests: `test/TRF3.SISPREC.EntityFrameworkCore.Tests/EntityFrameworkCore/Applications/`
- [ ] Repository Tests: `test/TRF3.SISPREC.EntityFrameworkCore.Tests/EntityFrameworkCore/`
- [ ] Web Tests: `test/TRF3.SISPREC.Web.Tests/Pages/`

### Classes Base SISPREC
- [ ] Domain: `SISPRECDomainTestBase<SISPRECTestBaseModule>`
- [ ] Application: `BaseAppServiceTests<SISPRECEntityFrameworkCoreTestModule>`
- [ ] Repository: `SISPRECEntityFrameworkCoreTestBase`
- [ ] Web: `SISPRECWebTestBase`

### Regras Específicas SISPREC
- [ ] Sempre usar `.Wait()` após `WithUnitOfWorkAsync` no construtor
- [ ] Usar `autoSave: true` ao inserir registros
- [ ] Usar constantes de `SISPRECTestConsts` para IDs pré-carregados
- [ ] Validar regras de negócio específicas (fases finalizadas, propostas fechadas)
- [ ] Usar `Random.Hash()` para strings, não `Random.String()`
- [ ] Usar `IsDelete = true` como padrão no Bogus
