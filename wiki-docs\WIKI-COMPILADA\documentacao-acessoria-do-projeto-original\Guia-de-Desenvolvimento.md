# Índice

- [Banco de Dados](/Guia-de-Desenvolvimento/Banco-de-Dados)
  - [Nomes de tabelas e colunas](/Guia-de-Desenvolvimento/Banco-de-Dados/Nomes-de-tabelas-e-colunas)
- [Autenticação e Autorização](/Guia-de-Desenvolvimento/Autenticação-e-Autorização)
  - [SISPRECPermissoes](/Guia-de-Desenvolvimento/Autenticação-e-Autorização/SISPRECPermissoes)
  - [SISPRECPermissionChecker](/Guia-de-Desenvolvimento/Autenticação-e-Autorização/PermissionChecker.md)
  - [Autorização-JS](/Guia-de-Desenvolvimento/Autenticação-e-Autorização/Autorização-JS.md)
  - [AuthorizationService](/Guia-de-Desenvolvimento/Autenticação-e-Autorização/AuthorizationService.md)
- [Camada <PERSON> (Projeto Domain)](/Guia-de-Desenvolvimento/Camada-Domínio-(Projeto-Domain))
  - [Entidades](/Guia-de-Desenvolvimento/Camada-Domínio-(Projeto-Domain)/Entidades.md)
  - [Serviços de Domínio (Domain Services)](/Guia-de-Desenvolvimento/Camada-Domínio-(Projeto-Domain)/Serviços-de-Domínio-(Domain-Services).md)
- [Configurations e Migrations](/Guia-de-Desenvolvimento/Configurations-e-Migrations)
  - [Migration com SQL puro](/Guia-de-Desenvolvimento/Configurations-e-Migrations/Migration-com-SQL-puro.md)
  - [Views](/Guia-de-Desenvolvimento/Configurations-e-Migrations/Views.md)
- [Convenções de Código](/Guia-de-Desenvolvimento/Convenções-de-Código)
  - [Formato Nomes](/Guia-de-Desenvolvimento/Convenções-de-Código/Formato-Nomes.md)
  - [Nomenclatura](/Guia-de-Desenvolvimento/Convenções-de-Código/Nomenclatura.md)
- [Gerador de PDF](/Guia-de-Desenvolvimento/Gerador-de-PDF)
  - [1 - Macro Fluxo](/Guia-de-Desenvolvimento/Gerador-de-PDF/1-%2D-Macro-Fluxo.md)
  - [2 - Fluxo Detalhado - Utilizando o componente](/Guia-de-Desenvolvimento/Gerador-de-PDF/2-%2D-Fluxo-Detalhado-%2D-Utilizando-o-componente.md)
  - [3 - Comportamento do Gerador de PDF](/Guia-de-Desenvolvimento/Gerador-de-PDF/3-%2D-Comportamento-do-Gerador-de-PDF.md)
- [Guia de Solução de Problemas](/Guia-de-Desenvolvimento/Guia-de-Solução-de-Problemas)
  - [DependencyResolutionException](/Guia-de-Desenvolvimento/Guia-de-Solução-de-Problemas/DependencyResolutionException.md)
- [Testes](/Testes)
  - [Carga de Dados](/Testes/Carga-de-Dados.md)
  - [Cobertura de DTOs](/Testes/Cobertura-de-DTOs.md)
  - [Como Testar AppServices](/Testes/Como-Testar-AppServices.md)
  - [DomainServicesTests](/Testes/DomainServicesTests.md)
  - [Instruções Gerais](/Testes/Instruções-Gerais.md)
  - [Métodos com InlineData](/Testes/Métodos-com-InlineData.md)
  - [Mock de dependências](/Testes/Mock-de-dependências.md)
  - [Mock IReqPagUnitOfWork](/Testes/Mock-IReqPagUnitOfWork.md)
  - [Retornos Mockados](/Testes/Retornos-Mockados.md)
  - [Testar Modais](/Testes/Testar-Modais.md)
  - [Testar Páginas](/Testes/Testar-Páginas.md)
  - [Testes AppServices](/Testes/Testes-AppServices.md)
  - [Testes em AppServices](/Testes/Testes-em-AppServices.md)
  - [Testes Páginas](/Testes/Testes-Páginas.md)
  - [Testes que usam banco (testes Páginas e AppServices)](/Testes/Testes-que-usam-banco-(testes-Páginas-e-AppServices).md)
  - [Testes unitários](/Testes/Testes-unitários.md)
  - [Views SQL](/Testes/Views-SQL.md)
  - [WithUnitOfWorkAsync method](/Testes/WithUnitOfWorkAsync-method.md)

