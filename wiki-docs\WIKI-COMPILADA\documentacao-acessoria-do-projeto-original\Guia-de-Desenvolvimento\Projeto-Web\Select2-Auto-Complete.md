# Select2 (Auto-Complete Select)

Um componente select simples às vezes não é útil com uma grande quantidade de dados. O ABP fornece uma implementação de select que funciona com paginação e pesquisa no lado do servidor usando [Select2](https://select2.org/).

| Único | Múltiplo |
| --- | --- |
| ![autocomplete-select-example](/ABP-Docs/images/abp-select2-single.png) |![autocomplete-select-example](/ABP-Docs/images/abp-select2-multiple.png) |     

## Select2 Múltiplo via API

### 1. HTML (Razor Page)

```csharp
<abp-row>
    <abp-column size="_12">
        <label class="form-label">Autores</label>
        <select class="form-control" asp-for="ViewModel.AutoresIds"></select>
    </abp-column>
</abp-row>
```

### 2. JavaScript - Configuração do Select2 Múltiplo

```javascript
$('#ViewModel_AutoresIds').select2({
    // Define o container pai como o modal para corrigir problemas de z-index
    dropdownParent: $('#CreateModalLivro'),
    // Usa o tema Bootstrap 5 para manter consistência visual
    theme: 'bootstrap-5',
    // Requer pelo menos 3 caracteres para iniciar a busca
    minimumInputLength: 3,
    // Permite selecionar múltiplos autores
    multiple: true,
    // Texto placeholder quando nenhum autor está selecionado
    placeholder: 'Selecione um ou mais autores...',
    // Permite limpar todas as seleções
    allowClear: true,
    // Configuração da busca AJAX
    ajax: {
        // Endpoint da API que retorna autores filtrados por nome
        url: abp.appPath + 'api/app/autor/autor-por-nome',
        dataType: 'json',
        // Função que prepara os parâmetros da busca
        data: function (params) {
            return {
                nomeAutor: params.term
            };
        },
        // Função que processa o resultado da busca
        processResults: function (data) {
            // Mapeia os dados retornados para o formato esperado pelo Select2
            return {
                results: $.map(data, function (obj) {
                    return {
                        id: obj.autorId,      // ID usado internamente pelo Select2
                        text: obj.nomeCompleto // Texto exibido no dropdown
                    };
                })
            };
        }
    }
});
```

### 3. Inicialização com Dados Pré-carregados

Para inicializar o Select2 com opções pré-selecionadas **(útil em modais de edição/detalhes)**:

No Razor, adicione um campo hidden com o dicionário de dados
```csharp
<select class="form-control" asp-for="ViewModel.AutoresIds"></select>
<input type="hidden" asp-for="ViewModel.AutoresDicionario" 
       value='@(Model.ViewModel.AutoresDicionario == null ? "" : System.Text.Json.JsonSerializer.Serialize(Model.ViewModel.AutoresDicionario))' />
```

No JavaScript, carregue os dados do campo hidden
```javascript
let autoresDictStr = $('#ViewModel_AutoresDicionario').val();
let autoresDict = JSON.parse(autoresDictStr || '{}');

// Converte o dicionário para o formato do Select2
let options = Object.keys(autoresDict).map(id => ({
    id: id,
    text: autoresDict[id],
    selected: true
}));

// Inicializa o Select2 com os dados
$('#ViewModel_AutoresIds').select2({
    dropdownParent: $('#DetalheModalLivro'),
    theme: 'bootstrap-5',
    multiple: true,
    data: options
});
```

## Select2 Único com dados via API

Exemplo de implementação com dois campos com dados preenchidos via API (código e nome).
Neste exemplo, quando um é selecionado, o outro é atualizado.

Este é um caso mais complexo. Caso precise implementar apenas a busca e preenchimento simples do campo, basta ignorar o trecho do código que altera o comportamento do outro select.

### 1. HTML (Razor Page)

```csharp
<abp-row>
    <abp-column size="_4">
        <label class="form-label">Código SIAFI</label>
        <select class="form-control" asp-for="ViewModel.CodigoSiafi" maxlength="15"></select>
    </abp-column>
    <abp-column size="_7">
        <label class="form-label">Nome da unidade</label>
        <select class="form-control" maxlength="200" id="ViewModel_NomeUnidade"></select>
    </abp-column>
</abp-row>
```

### 2. JavaScript - Configuração e Busca

```javascript
// Serviço para chamadas à API
const unidadeOrcamentariaService = tRF3.sISPREC.unidadesOrcamentarias.unidadeOrcamentaria;

// Inicialização do Select2 para busca por CodigoSiafi
$('#ViewModel_CodigoSiafi').select2({
    dropdownParent: $('#CreateModal'),
    theme: 'bootstrap-5',
    minimumInputLength: 3,
    ajax: {
        url: abp.appPath + 'api/app/unidade-orcamentaria/todos-os-codigo-siafi-unidades-orcamentarias',
        dataType: 'json',
        data: function (params) {
            let query = {
                codigoSIAFI: params.term
            };
            return query;
        },
        processResults: function (data) {
            let result = $.map(data, function (obj) {
                obj.id = obj.codigoSIAFI;
                obj.text = obj.codigoSIAFI;
                return obj;
            });

            return {
                results: result
            };
        }
    }
});


// Inicialização do Select2 para busca por nome
$('#ViewModel_NomeUnidade').select2({
    dropdownParent: $('#CreateModal'),
    theme: 'bootstrap-5',
    minimumInputLength: 3,
    ajax: {
        url: abp.appPath + 'api/app/unidade-orcamentaria/todos-os-nomes-unidades-orcamentarias',
        dataType: 'json',
        data: function (params) {
            return {
                nome: params.term || ''
            };
        },
        processResults: function (data) {
            return {
                results: $.map(data, function (obj) {
                    return {
                        id: obj.codigoSIAFI,
                        text: obj.nome
                    };
                })
            };
        }
    }
});

// Sincronização: Ao selecionar um código, atualiza o nome
$('#ViewModel_CodigoSiafi').on('change', function () {
    let codigoSiafiSelecionado = $(this).val();
    
    unidadeOrcamentariaService.getNomeUnidadePeloCodigoSiafi(codigoSiafiSelecionado)
        .then(function (result) {
            // Limpa seleção atual
            $('#ViewModel_NomeUnidade').val(null).trigger('change');
            
            // Cria e seleciona nova opção
            let newOption = new Option(result, codigoSiafiSelecionado, true, true);
            $('#ViewModel_NomeUnidade').append(newOption).trigger('change');
        });
});

// Sincronização: Ao selecionar um nome, atualiza o código
$('#ViewModel_NomeUnidade').on('select2:select', function (e) {
    let data = e.params.data;
    let newOption = new Option(data.id, data.id, true, true);
    $('#ViewModel_CodigoSiafi').append(newOption).trigger('change');
});
```

## **Configurações Importantes**

- `dropdownParent`: Necessário quando usado em modais
- `theme`: Use 'bootstrap-5' para compatibilidade com Bootstrap 5
- `minimumInputLength`: Mínimo de caracteres para iniciar a busca
- `ajax.url`: Endpoint da API que retorna os dados
- `ajax.processResults`: Formata os dados retornados pela API
- `multiple`: true para seleção múltipla, false ou omitido para única
- `placeholder`: Texto exibido quando nenhum item está selecionado
- `allowClear`: Permite limpar a seleção (x) 
  - **Observação**: O botão de limpar só funciona quando o placeholder está configurado.

## Avisos

Se o usuário autenticado não tiver permissão na URL fornecida, o usuário receberá um erro de autorização. Você pode criar um endpoint/método com permissão que se encaixe no caso de uso para obter a lista de itens.
