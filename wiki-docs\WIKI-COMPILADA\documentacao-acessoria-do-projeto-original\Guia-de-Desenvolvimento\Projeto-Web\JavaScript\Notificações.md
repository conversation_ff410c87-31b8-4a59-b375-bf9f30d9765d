# Notificações

A API de Notificações é usada para mostrar notificações UI no estilo toast, que desaparecem automaticamente para o usuário final. Ela é implementada pela biblioteca [Toastr](https://github.com/CodeSeven/toastr) por padrão.

## Exemplo Rápido

Use a função `abp.notify.success(...)` para mostrar uma mensagem de sucesso:

```js
abp.notify.success(
    'O produto "Acme Atom Re-Arranger" foi excluído com sucesso.',
    'Produto Excluído'
);
```

Uma mensagem de notificação é exibida no canto inferior direito da página:

![js-message-success](/ABP-Docs/images/js-notify-success.png)

## Tipos de Notificação

Existem quatro tipos de notificações pré-definidas:

* `abp.notify.success(...)`
* `abp.notify.info(...)`
* `abp.notify.warn(...)`
* `abp.notify.error(...)`

Todos os métodos acima recebem os seguintes parâmetros:

* `message`: Uma mensagem (`string`) a ser exibida ao usuário.
* `title`: Um título opcional (`string`).
* `options`: Opções adicionais a serem passadas para a biblioteca subjacente, para o Toastr por padrão.

## Configuração do Toastr

A API de Notificações é implementada pela biblioteca [Toastr](https://github.com/CodeSeven/toastr) por padrão. Você pode ver suas próprias opções de configuração.

**Exemplo: Mostrar mensagens toast no canto superior direito da página**

```js
toastr.options.positionClass = 'toast-top-right';
```

> O ABP define esta opção como `toast-bottom-right` por padrão. Você pode substituí-la conforme mostrado acima.
