# <PERSON><PERSON><PERSON>lo Tema LeptonX

> Você deve ter uma licença ABP Team ou superior para usar este tema.

O Tema LeptonX é um tema profissional para o ABP.

* Confira o site do LeptonX em https://leptontheme.com/.
* Confira a demonstração ao vivo para vê-lo em ação. https://x.leptontheme.com/.

## Destaques

* Construído sobre a biblioteca [Bootstrap 5](https://getbootstrap.com).
* 100% compatível com a estrutura HTML e classes CSS do [Bootstrap 5](https://getbootstrap.com).
* Responsivo e compatível com dispositivos móveis.
* Fornece estilos diferentes como Dim, Dark e Light.

Uma captura de tela do estilo claro do tema:

![lepton-theme-light](/ABP-Docs/images/lepton-x-theme-light.png)

> [Veja todos os estilos de tema e crie uma demonstração para vê-lo em ação](https://abp.io/themes).

## Como Instalar

O módulo Tema LeptonX já vem pré-instalado nos [templates de inicialização](../../get-started). Portanto, não há necessidade de instalá-lo manualmente.

## Pacotes

Este módulo segue o [guia de melhores práticas de desenvolvimento de módulos](../../framework/architecture/best-practices) e consiste em vários pacotes NuGet e NPM. Consulte o guia se quiser entender os pacotes e as relações entre eles.

### Pacotes NuGet

* Volo.Abp.AspNetCore.Components.Server.LeptonXTheme
* Volo.Abp.AspNetCore.Components.Web.LeptonXTheme
* Volo.Abp.AspNetCore.Components.WebAssembly.LeptonXTheme
* Volo.Abp.AspNetCore.LeptonX.Shared
* Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonX

### Pacotes NPM

* @volo/abp.aspnetcore.mvc.ui.theme.leptonx
* @volo/aspnetcore.components.server.leptonxtheme

#### Angular

* @volo/abp.ng.lepton-x.core
* @volo/ngx-lepton-x.core
* @volosoft/abp.ng.theme.lepton-x
* @volosoft/ngx-lepton-x

## Interface de Usuário

O módulo Tema LeptonX não fornece nenhuma página de UI. Ele apenas altera as páginas de UI existentes de um aplicativo. Aqui estão algumas páginas de amostra:

### Página de Login

![lepton-theme-module-login-page](/ABP-Docs/images/lepton-x-theme-module-login-page.png)

### Página de Idiomas

![lepton-theme-module-languages-page](/ABP-Docs/images/lepton-x-theme-module-languages-page.png)

### Páginas

Este módulo não define nenhuma página.

#### UI de Configurações do Módulo de Identidade

O módulo Tema LeptonX adiciona uma nova aba à página de Configurações para personalizar o comportamento em tempo de execução.

![lepton-theme-module-settings-page](/ABP-Docs/images/lepton-x-theme-module-settings-page.png)

## Internos

### Configurações

O Módulo LeptonX não define nenhuma configuração.

### Permissões

O Módulo LeptonX não define nenhuma permissão.

### Código Fonte

Você pode usar o seguinte comando da CLI para baixar o código-fonte:

```bash
abp get-source Volo.Abp.LeptonXTheme
```

Se você quiser baixar o código-fonte da versão de pré-visualização, você pode usar o seguinte comando:

```bash
abp get-source Volo.Abp.LeptonXTheme --preview
```

> Você pode baixar o código fonte de uma determinada versão usando o parâmetro `--version`. Consulte a [documentação da ABP CLI](../../cli/index#get-source) para outras opções possíveis.

Clientes ABP também podem baixar o código fonte de [https://x.leptontheme.com/](https://x.leptontheme.com/) de [https://abp.io/api/download/samples/leptonx-demo](https://abp.io/api/download/samples/leptonx-demo).

Para entender a estrutura do código-fonte do LeptonX e construí-lo a partir de seu código-fonte, você pode verificar a [documentação do código-fonte do LeptonX](source-files.md).

## Customização do Tema LeptonX

Você pode usar os seguintes links para ver as customizações para diferentes tipos de UI:

* [Tema LeptonX: UI MVC](mvc.md)
* [Tema LeptonX: UI Angular](angular.md)
* [Tema LeptonX: UI Blazor](blazor.md)
