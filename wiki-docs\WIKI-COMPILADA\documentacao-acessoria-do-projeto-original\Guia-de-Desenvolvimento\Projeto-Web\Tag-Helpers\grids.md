# Grids

## Introdução

Abp tag helpers para o sistema de grid baseado em bootstrap.

## Demo

Veja a [página de demonstração de grids](https://bootstrap-taghelpers.abp.io/Components/Grids) para vê-lo em ação.

### Sizing

**Largura Igual:** Cria colunas com larguras iguais.

Exemplo:

````xml
<abp-container>
    <abp-row>
        <abp-column abp-border="Info">1 de 2</abp-column>
        <abp-column abp-border="Danger">2 de 2</abp-column>
    </abp-row>
    <abp-row>
        <abp-column abp-border="Primary">1 de 3</abp-column>
        <abp-column abp-border="Secondary">2 de 3</abp-column>
        <abp-column abp-border="Dark">3 de 3</abp-column>
    </abp-row>
</abp-container>
````

**Quebra de Coluna:** `abp-column-breaker` é usado para quebrar a largura automática de posicionamento da linha atual e começar em uma nova linha depois.

Exemplo:

````xml
<abp-container>
    <abp-row>
        <abp-column>coluna</abp-column>
        <abp-column>coluna</abp-column>
        <abp-column-breaker/>
        <abp-column>coluna</abp-column>
        <abp-column>coluna</abp-column>
    </abp-row>
</abp-container>
````

**Definindo a largura de uma coluna:** o atributo `size` é usado para definir a largura de uma coluna específica.

Exemplo:

```xml
<abp-container>
    <abp-row>
        <abp-column>1 de 3</abp-column>
        <abp-column size="_6">2 de 3 (mais larga)</abp-column>
        <abp-column>3 de 3</abp-column>
    </abp-row>
    <abp-row>
        <abp-column>1 de 3</abp-column>
        <abp-column size="_5">2 de 3 (mais larga)</abp-column>
        <abp-column>3 de 3</abp-column>
    </abp-row>
</abp-container>
```

**Conteúdo de largura variável:** Redimensionamento automático da coluna com base no conteúdo.

```xml
<abp-container>
    <abp-row h-align="Center">
        <abp-column size-lg="_2" abp-border="Info">1 de 3</abp-column>
        <abp-column size-md="Auto" abp-border="Danger">Ao contrário da crença popular, Lorem Ipsum não é simplesmente texto aleatório.</abp-column>
        <abp-column size-lg="_2" abp-border="Warning">3 de 3</abp-column>
    </abp-row>
    <abp-row>
        <abp-column>1 de 3</abp-column>
        <abp-column size-md="Auto">Conteúdo de largura variável</abp-column>
        <abp-column size-lg="_2">3 de 3</abp-column>
    </abp-row>
</abp-container>
```

### Classes Responsivas

Classes responsivas podem ser usadas com tipo forte dentro das tags `abp`.

```xml
<abp-row>
    <abp-column size-sm="_8">col-sm-8</abp-column>
    <abp-column size-sm="_4">col-sm-4</abp-column>
</abp-row>
<abp-row>
    <abp-column size-sm="_">col-sm</abp-column>
    <abp-column size-sm="_">col-sm</abp-column>
    <abp-column size-sm="_">col-sm</abp-column>
    <abp-column size-sm="_">col-sm</abp-column>
</abp-row>
<!-- Empilhe as colunas em dispositivos móveis, tornando uma largura total e a outra metade da largura -->
<abp-row>
    <abp-column size="_12" size-md="_8">.col-12 .col-md-8</abp-column>
    <abp-column size="_6" size-md="_4">.col-6 .col-md-4</abp-column>
</abp-row>

<!-- As colunas começam com 50% de largura em dispositivos móveis e aumentam para 33,3% de largura em desktops -->
<abp-row>
    <abp-column size="_6" size-md="_4">.col-6 .col-md-4</abp-column>
    <abp-column size="_6" size-md="_4">.col-6 .col-md-4</abp-column>
    <abp-column size="_6" size-md="_4">.col-6 .col-md-4</abp-column>
</abp-row>

<!-- As colunas têm sempre 50% de largura, em dispositivos móveis e desktops -->
<abp-row>
    <abp-column size="_6">.col-6</abp-column>
    <abp-column size="_6">.col-6</abp-column>
</abp-row>
```

### Alinhamento

Alinhamentos de coluna podem ser feitos com tipo forte em tags `abp` tanto vertical quanto horizontalmente.

**Alinhamento Vertical**: O valor do atributo `v-align` é usado para alinhar as colunas verticalmente.

Exemplo:

```xml
<abp-container>
    <abp-row v-align="Start">
        <abp-column>coluna</abp-column>
        <abp-column>coluna</abp-column>
        <abp-column>coluna</abp-column>
    </abp-row>
    <abp-row v-align="Center">
        <abp-column>coluna</abp-column>
        <abp-column>coluna</abp-column>
        <abp-column>coluna</abp-column>
    </abp-row>
    <abp-row v-align="End">
        <abp-column>coluna</abp-column>
        <abp-column>coluna</abp-column>
        <abp-column>coluna</abp-column>
    </abp-row>
</abp-container>
```

**Alinhamento Horizontal**: O valor do atributo `h-align` é usado para alinhar as colunas horizontalmente.

Exemplo:

```xml
<abp-container>
    <abp-row h-align="Start">
        <abp-column size="_4">Uma de duas colunas</abp-column>
        <abp-column size="_4">Uma de duas colunas</abp-column>
    </abp-row>
    <abp-row h-align="Center">
        <abp-column size="_4">Uma de duas colunas</abp-column>
        <abp-column size="_4">Uma de duas colunas</abp-column>
    </abp-row>
    <abp-row h-align="End">
        <abp-column size="_4">Uma de duas colunas</abp-column>
        <abp-column size="_4">Uma de duas colunas</abp-column>
    </abp-row>
    <abp-row h-align="Around">
        <abp-column size="_4">Uma de duas colunas</abp-column>
        <abp-column size="_4">Uma de duas colunas</abp-column>
    </abp-row>
    <abp-row h-align="Between">
        <abp-column size="_4">Uma de duas colunas</abp-column>
        <abp-column size="_4">Uma de duas colunas</abp-column>
    </abp-row>
</abp-container>
```

**Sem espaçamento**: Os espaçamentos entre colunas nas classes de grid predefinidas podem ser removidos com `gutters="false"`. Isso remove as `margin` negativas de `abp-row` e o `padding` horizontal de todas as colunas filhas diretas.

Exemplo:

```xml
<abp-row gutters="false">
    <abp-column size="_8">Uma de duas colunas</abp-column>
    <abp-column size="_4">Uma de duas colunas</abp-column>
</abp-row>
```

**Quebra de coluna**: Se mais de 12 colunas forem colocadas em uma única linha, cada grupo de colunas extras será, como uma unidade, envolvido em uma nova linha.

Exemplo:

```xml
<abp-row>
    <abp-column size="_9">.col-9</abp-column>
    <abp-column size="_4">.col-4<br>Como 9 + 4 = 13 &gt; 12, este div de 4 colunas de largura é envolvido em uma nova linha como uma unidade contígua.</abp-column>
    <abp-column size="_6">.col-6<br>Colunas subsequentes continuam ao longo da nova linha.</abp-column>
</abp-row>
```

### Reordenando

**Classes de Ordem**: O atributo `order` é usado para controlar a ordem visual do conteúdo.

Exemplo:

```xml
<abp-container>
    <abp-row>
        <abp-column order="_12">Primeira, mas Última</abp-column>
        <abp-column>Segunda, mas não ordenada</abp-column>
        <abp-column order="_6">Terceira, mas Segunda</abp-column>
    </abp-row>
</abp-container>
```

**Deslocamento de colunas**: O atributo `offset` é usado para definir o deslocamento das colunas do grid.

Exemplo:

```xml
<abp-container>
    <abp-row>
        <abp-column size-md="_4">.col-md-4</abp-column>
        <abp-column size-md="_4" offset-md="_4">.col-md-4 .offset-md-4</abp-column>
    </abp-row>
    <abp-row>
        <abp-column size-md="_3" offset-md="_3">.col-md-3 .offset-md-3</abp-column>
        <abp-column size-md="_3" offset-md="_3">.col-md-3 .offset-md-3</abp-column>
    </abp-row>
    <abp-row>
        <abp-column size-md="_6" offset-md="_3">.col-md-6 .offset-md-3</abp-column>
    </abp-row>
    <abp-row>
        <abp-column size-sm="_5" size-md="_6">.col-sm-5 .col-md-6</abp-column>
        <abp-column size-sm="_5" offset-sm="_2" size-md="_6" offset-md="_">.col-sm-5 .offset-sm-2 .col-md-6 .offset-md-0</abp-column>
    </abp-row>
    <abp-row>
        <abp-column size-sm="_6" size-md="_5" size-lg="_6">col-sm-6 .col-md-5 .col-lg-6</abp-column>
        <abp-column size-sm="_6" size-md="_5" offset-md="_2" size-lg="_6" offset-lg="_">.col-sm-6 .col-md-5 .offset-md-2 .col-lg-6 .offset-lg-0</abp-column>
    </abp-row>
</abp-container>
```

## Atributos de `abp-row`

-   **v-align:** Um valor indica o posicionamento vertical das colunas que o contêm. Deve ser um dos seguintes valores:
    *   `Default` (valor padrão)
    *   `Start`
    *   `Center`
    *   `End`

-   **h-align**: Um valor indica o posicionamento horizontal das colunas que o contêm. Deve ser um dos seguintes valores:
    *   `Default` (valor padrão)
    *   `Start`
    *   `Center`
    *   `Around`
    *   `Between`
    *   `End`
-   **gutter**: Um valor indica se a `margin` negativa e o `padding` horizontal serão removidos de todas as colunas filhas. Agirá como valor `true` se este atributo não estiver definido. Deve ser um dos seguintes valores:
    *   `true`
    *   `false`

## Atributos de `abp-column`

-   **size:** Um valor indica a largura da coluna de `_`, `Undefined`, `_1`..`_12`, `Auto`. Ou pode ser usado com valores predefinidos como:
    *   `size-sm`
    *   `size-md`
    *   `size-lg`
    *   `size-xl`
-   **order**: Um valor indica a ordem da coluna de `Undefined`, `_1`..`_12`, `First` e `Last`.
-   **offset:** Um valor indica o deslocamento da coluna de `_`, `Undefined`, `_1`..`_12`, `Auto`. Ou pode ser usado com valores predefinidos como:
    *   `offset-sm`
    *   `offset-md`
    *   `offset-lg`
    *   `offset-xl`
