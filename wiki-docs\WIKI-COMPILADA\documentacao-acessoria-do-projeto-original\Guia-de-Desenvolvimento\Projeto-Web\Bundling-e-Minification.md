# ASP.NET Core MVC Bundling & Minification

Existem várias maneiras de fazer o bundling & minification de recursos do lado do cliente (arquivos JavaScript e CSS). As formas mais comuns são:

* Usando a extensão [Bundler & Minifier](https://marketplace.visualstudio.com/items?itemName=MadsKristensen.BundlerMinifier) do Visual Studio ou o [pacote NuGet](https://www.nuget.org/packages/BuildBundlerMinifier/).
* Usando gerenciadores de tarefas [Gulp](https://gulpjs.com/)/[Grunt](https://gruntjs.com/) e seus plugins.

O ABP oferece uma maneira simples, dinâmica, poderosa, modular e integrada.

## Pacote Volo.Abp.AspNetCore.Mvc.UI.Bundling

> Este pacote já está instalado por padrão com os *startup templates*. Portanto, na maioria das vezes, você não precisa instalá-lo manualmente.

Se você não estiver usando um *startup template*, pode usar o [ABP CLI](../../../cli) para instalá-lo em seu projeto. Execute o seguinte comando na pasta que contém o arquivo .csproj do seu projeto:

````
abp add-package Volo.Abp.AspNetCore.Mvc.UI.Bundling
````

> Se você ainda não o fez, primeiro precisa instalar o [ABP CLI](../../../cli). Para outras opções de instalação, consulte [a página de descrição do pacote](https://abp.io/package-detail/Volo.Abp.AspNetCore.Mvc.UI.Bundling).

## Tag Helpers Razor para Bundling

A maneira mais simples de criar um bundle é usar os tag helpers `abp-script-bundle` ou `abp-style-bundle`. Exemplo:

````html
<abp-style-bundle name="MyGlobalBundle">
    <abp-style src="/libs/bootstrap/css/bootstrap.css" />
    <abp-style src="/libs/font-awesome/css/font-awesome.css" />
    <abp-style src="/libs/toastr/toastr.css" />
    <abp-style src="/styles/my-global-style.css" />
</abp-style-bundle>
````

Este bundle define um *style bundle* com um **nome único**: `MyGlobalBundle`. É muito fácil entender como usá-lo. Vamos ver como ele *funciona*:

* O ABP cria o bundle como **lazy** a partir dos arquivos fornecidos quando ele é **solicitado pela primeira vez**. Para as chamadas subsequentes, ele é retornado do **cache**. Isso significa que, se você adicionar os arquivos ao bundle condicionalmente, ele será executado apenas uma vez e quaisquer alterações na condição não afetarão o bundle para as próximas solicitações.
* O ABP adiciona os arquivos do bundle **individualmente** à página para o ambiente de `development`. Ele automaticamente faz o bundling & minification para outros ambientes (`staging`, `production`...). Consulte a seção *Modo de Bundling* para alterar esse comportamento.
* Os arquivos do bundle podem ser arquivos **físicos** ou arquivos [**virtuais/embutidos**](../../infrastructure/virtual-file-system.md).
* O ABP adiciona automaticamente uma **query string de versão** à URL do arquivo do bundle para impedir que os navegadores armazenem em cache quando o bundle está sendo atualizado. (como ?_v=67872834243042 - gerado a partir da última data de alteração dos arquivos relacionados). O versionamento funciona mesmo se os arquivos do bundle forem adicionados individualmente à página (no ambiente de desenvolvimento).

### Importando os Tag Helpers para Bundling

> Isso já é importado por padrão com os *startup templates*. Portanto, na maioria das vezes, você não precisa adicioná-lo manualmente.

Para usar os tag helpers de bundle, você precisa adicioná-lo ao seu arquivo `_ViewImports.cshtml` ou à sua página:

````
@addTagHelper *, Volo.Abp.AspNetCore.Mvc.UI.Bundling
````

### Bundles Sem Nome

O `name` é **opcional** para os tag helpers de bundle do razor. Se você não definir um nome, ele será **calculado** automaticamente com base nos nomes dos arquivos de bundle usados ​​(eles são **concatenados** e **hasheados**). Exemplo:

````html
<abp-style-bundle>
    <abp-style src="/libs/bootstrap/css/bootstrap.css" />
    <abp-style src="/libs/font-awesome/css/font-awesome.css" />
    <abp-style src="/libs/toastr/toastr.css" />
    @if (ViewBag.IncludeCustomStyles != false)
    {
        <abp-style src="/styles/my-global-style.css" />
    }
</abp-style-bundle>
````

Isso potencialmente criará **dois bundles diferentes** (um inclui o `my-global-style.css` e o outro não).

Vantagens de bundles **sem nome**:

* Pode **adicionar itens condicionalmente** ao bundle. Mas isso pode levar a várias variações do bundle com base nas condições.

Vantagens de bundles **nomeados**:

* Outros **módulos podem contribuir** para o bundle pelo seu nome (veja as seções abaixo).

### Arquivo Único

Se você precisar apenas adicionar um único arquivo à página, pode usar a tag `abp-script` ou `abp-style` sem envolver na tag `abp-script-bundle` ou `abp-style-bundle`. Exemplo:

````xml
<abp-script src="/scripts/my-script.js" />
````

O nome do bundle será *scripts.my-scripts* para o exemplo acima ("/" é substituído por "."). Todos os recursos de *bundling* funcionam como esperado também para bundles de arquivo único.

## Opções de Bundling

Se você precisar usar o mesmo bundle em **várias páginas** ou quiser usar alguns **recursos mais poderosos**, pode configurar os bundles **por código** em sua classe de [módulo](../../architecture/modularity/basics.md).

### Criando um Novo Bundle

Exemplo de uso:

````C#
[DependsOn(typeof(AbpAspNetCoreMvcUiBundlingModule))]
public class MyWebModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpBundlingOptions>(options =>
        {
            options
                .ScriptBundles
                .Add("MyGlobalBundle", bundle => {
                    bundle.AddFiles(
                        "/libs/jquery/jquery.js",
                        "/libs/bootstrap/js/bootstrap.js",
                        "/libs/toastr/toastr.min.js",
                        "/scripts/my-global-scripts.js"
                    );
                });                
        });
    }
}
````

> Você pode usar o mesmo nome (*MyGlobalBundle* aqui) para um *script & style bundle*, pois eles são adicionados a coleções diferentes (`ScriptBundles` e `StyleBundles`).

Depois de definir um bundle, ele pode ser incluído em uma página usando os mesmos tag helpers definidos acima. Exemplo:

````html
<abp-script-bundle name="MyGlobalBundle" />
````

Desta vez, nenhum arquivo definido na definição do tag helper porque os arquivos do bundle são definidos pelo código.

### Configurando um Bundle Existente

O ABP também oferece suporte a [modularidade](../../architecture/modularity/basics.md) para o *bundling*. Um módulo pode modificar um bundle existente criado por um módulo do qual ele depende. Exemplo:

````C#
[DependsOn(typeof(MyWebModule))]
public class MyWebExtensionModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpBundlingOptions>(options =>
        {
            options
                .ScriptBundles
                .Configure("MyGlobalBundle", bundle => {
                    bundle.AddFiles(
                        "/scripts/my-extension-script.js"
                    );
                });
        });
    }
}
````

Você também pode usar o método `ConfigureAll` para configurar todos os bundles existentes:

````C#
[DependsOn(typeof(MyWebModule))]
public class MyWebExtensionModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpBundlingOptions>(options =>
        {
            options
                .ScriptBundles
                .ConfigureAll(bundle => {
                    bundle.AddFiles(
                        "/scripts/my-extension-script.js"
                    );
                });
        });
    }
}
````

## Contribuidores de Bundle

Adicionar arquivos a um bundle existente parece útil. E se você precisar **substituir** um arquivo no bundle ou quiser adicionar arquivos **condicionalmente**? Definir um *bundle contributor* fornece poder extra para esses casos.

Um exemplo de *bundle contributor* que substitui bootstrap.css por uma versão personalizada:

````C#
public class MyExtensionGlobalStyleContributor : BundleContributor
{
    public override void ConfigureBundle(BundleConfigurationContext context)
    {
        context.Files.ReplaceOne(
            "/libs/bootstrap/css/bootstrap.css",
            "/styles/extensions/bootstrap-customized.css"
        );
    }
}
````

Então você pode usar este *contributor* como abaixo:

````C#
services.Configure<AbpBundlingOptions>(options =>
{
    options
        .ScriptBundles
        .Configure("MyGlobalBundle", bundle => {
            bundle.AddContributors(typeof(MyExtensionGlobalStyleContributor));
        });
});
````

> Você também pode adicionar *contributors* ao criar um novo bundle.

Os *contributors* também podem ser usados ​​nos tag helpers de bundle. Exemplo:

````xml
<abp-style-bundle>
    <abp-style type="@typeof(BootstrapStyleContributor)" />
    <abp-style src="/libs/font-awesome/css/font-awesome.css" />
    <abp-style src="/libs/toastr/toastr.css" />
</abp-style-bundle>
````

As tags `abp-style` e `abp-script` podem obter atributos `type` (em vez de atributos `src`), como mostrado neste exemplo. Quando você adiciona um *bundle contributor*, suas dependências também são adicionadas automaticamente ao bundle.

### Dependências de Contributor

Um *bundle contributor* pode ter uma ou mais dependências de outros *contributors*.
Exemplo:

````C#
[DependsOn(typeof(MyDependedBundleContributor))] //Define a dependência
public class MyExtensionStyleBundleContributor : BundleContributor
{
    //...
}
````

Quando um *bundle contributor* é adicionado, suas dependências são adicionadas **automaticamente e recursivamente**. As dependências são adicionadas pela **ordem de dependência**, evitando **duplicatas**. As duplicatas são evitadas mesmo que estejam em bundles separados. O ABP organiza todos os bundles em uma página e elimina duplicações.

Criar *contributors* e definir dependências é uma forma de organizar a criação de bundles em diferentes módulos.

### Extensões de Contributor

Em alguns cenários avançados, você pode querer fazer alguma configuração adicional sempre que um *bundle contributor* for usado. As extensões de *contributor* funcionam perfeitamente quando o *contributor* estendido é usado.

O exemplo abaixo adiciona alguns estilos para a biblioteca prism.js:

````csharp
public class MyPrismjsStyleExtension : BundleContributor
{
    public override void ConfigureBundle(BundleConfigurationContext context)
    {
        context.Files.AddIfNotContains("/libs/prismjs/plugins/toolbar/prism-toolbar.css");
    }
}
````

Então você pode configurar `AbpBundleContributorOptions` para estender o `PrismjsStyleBundleContributor` existente.

````csharp
Configure<AbpBundleContributorOptions>(options =>
{
    options
        .Extensions<PrismjsStyleBundleContributor>()
        .Add<MyPrismjsStyleExtension>();
});
````

Sempre que `PrismjsStyleBundleContributor` for adicionado a um bundle, `MyPrismjsStyleExtension` também será adicionado automaticamente.

### Acessando o IServiceProvider

Embora seja raramente necessário, o `BundleConfigurationContext` tem uma propriedade `ServiceProvider` que você pode usar para resolver dependências de serviço dentro do método `ConfigureBundle`.

### Contribuidores de Pacote Padrão

Adicionar um recurso de pacote NPM específico (arquivos js, css) em um bundle é bem simples para esse pacote. Por exemplo, você sempre adiciona o arquivo `bootstrap.css` para o pacote NPM do bootstrap.

Existem *contributors* embutidos para todos os [pacotes NPM padrão](client-side-package-management.md). Por exemplo, se o seu *contributor* depender do bootstrap, você pode simplesmente declará-lo, em vez de adicionar o bootstrap.css você mesmo.

````C#
[DependsOn(typeof(BootstrapStyleContributor))] //Define a dependência de estilo bootstrap
public class MyExtensionStyleBundleContributor : BundleContributor
{
    //...
}
````

Usando os *contributors* embutidos para pacotes padrão:

* Impede que você digite **caminhos de recursos inválidos**.
* Impede que você altere seu *contributor* se o **caminho do recurso mudar** (o *contributor* dependente irá tratá-lo).
* Impede que vários módulos adicionem **arquivos duplicados**.
* Gerencia **dependências recursivamente** (adiciona dependências de dependências, se necessário).

#### Pacote Volo.Abp.AspNetCore.Mvc.UI.Packages

> Este pacote já está instalado por padrão nos *startup templates*. Portanto, na maioria das vezes, você não precisa instalá-lo manualmente.

Se você não estiver usando um *startup template*, pode usar o [ABP CLI](../../../cli) para instalá-lo em seu projeto. Execute o seguinte comando na pasta que contém o arquivo .csproj do seu projeto:

````
abp add-package Volo.Abp.AspNetCore.Mvc.UI.Packages
````

> Se você ainda não o fez, primeiro precisa instalar o [ABP CLI](../../../cli). Para outras opções de instalação, consulte [a página de descrição do pacote](https://abp.io/package-detail/Volo.Abp.AspNetCore.Mvc.UI.Packages).

### Herança de Bundle

Em alguns casos específicos, pode ser necessário criar um **novo** bundle **herdado** de outros bundle(s). A herança de um bundle (recursivamente) herda todos os arquivos/contributors desse bundle. Então, o bundle derivado pode adicionar ou modificar arquivos/contributors **sem modificar** o bundle original.
Exemplo:

````c#
services.Configure<AbpBundlingOptions>(options =>
{
    options
        .StyleBundles
        .Add("MyTheme.MyGlobalBundle", bundle => {
            bundle
                .AddBaseBundles("MyGlobalBundle") //Pode adicionar múltiplos
                .AddFiles(
                    "/styles/mytheme-global-styles.css"
                );
        });
});
````

## Opções Adicionais

Esta seção mostra outras opções úteis para o sistema de *bundling* e minificação.

### Modo de Bundling

O ABP adiciona arquivos de bundle individualmente à página para o ambiente de `development`. Ele automaticamente faz o *bundling & minification* para outros ambientes (`staging`, `production`...). Na maioria das vezes, este é o comportamento que você deseja. No entanto, você pode querer configurá-lo manualmente em alguns casos. Existem quatro modos:

* `Auto`: Determina automaticamente o modo com base no ambiente.
* `None`: Sem *bundling* ou minificação.
* `Bundle`: *Bundled*, mas não minificado.
* `BundleAndMinify`: *Bundled* e minificado.

Você pode configurar `AbpBundlingOptions` no `ConfigureServices` do seu [módulo](../../architecture/modularity/basics.md).

**Exemplo:**

````csharp
Configure<AbpBundlingOptions>(options =>
{
    options.Mode = BundlingMode.Bundle;
});
````

### Ignorar para Minificação

É possível ignorar um arquivo específico para a minificação.

**Exemplo:**

````csharp
Configure<AbpBundlingOptions>(options =>
{
    options.MinificationIgnoredFiles.Add("/scripts/myscript.js");
});
````

O arquivo fornecido ainda é adicionado ao bundle, mas não é minificado neste caso.

### Carregar JavaScript e CSS de forma Assíncrona

Você pode configurar `AbpBundlingOptions` para carregar todos ou um único arquivo js/css de forma assíncrona.

**Exemplo:**

````csharp
Configure<AbpBundlingOptions>(options =>
{
    options.PreloadStyles.Add("/__bundles/Basic.Global");
    options.DeferScriptsByDefault = true;
});
````

**Saída HTML:**
````html
<link rel="preload" href="/__bundles/Basic.Global.F4FA61F368098407A4C972D0A6914137.css?_v=637697363694828051" as="style" onload="this.rel='stylesheet'"/>

<script defer src="/libs/timeago/locales/jquery.timeago.en.js?_v=637674729040000000"></script>
````

### Suporte para Arquivo Externo/CDN

O sistema de *bundling* reconhece automaticamente os arquivos externos/CDN e os adiciona à página sem nenhuma alteração.

#### Usando arquivos Externo/CDN em `AbpBundlingOptions`

````csharp
Configure<AbpBundlingOptions>(options =>
{
    options.StyleBundles
        .Add("MyStyleBundle", configuration =>
        {
            configuration
                .AddFiles("/styles/my-style1.css")
                .AddFiles("/styles/my-style2.css")
                .AddFiles("https://cdn.abp.io/bootstrap.css")
                .AddFiles("/styles/my-style3.css")
                .AddFiles("/styles/my-style4.css");
        });

    options.ScriptBundles
        .Add("MyScriptBundle", configuration =>
        {
            configuration
                .AddFiles("/scripts/my-script1.js")
                .AddFiles("/scripts/my-script2.js")
                .AddFiles("https://cdn.abp.io/bootstrap.js")
                .AddFiles("/scripts/my-script3.js")
                .AddFiles("/scripts/my-script4.js");
        });
});
````

**Saída HTML:**

````html
<link rel="stylesheet" href="/__bundles/MyStyleBundle.EA8C28419DCA43363E9670973D4C0D15.css?_v=638331889644609730" />
<link rel="stylesheet" href="https://cdn.abp.io/bootstrap.css" />
<link rel="stylesheet" href="/__bundles/MyStyleBundle.AC2E0AA6C461A0949A1295E9BDAC049C.css?_v=638331889644623860" />

<script src="/__bundles/MyScriptBundle.C993366DF8840E08228F3EE685CB08E8.js?_v=638331889644937120"></script>
<script src="https://cdn.abp.io/bootstrap.js"></script>
<script src="/__bundles/MyScriptBundle.2E8D0FDC6334D2A6B847393A801525B7.js?_v=638331889644943970"></script>
````

#### Usando arquivos Externo/CDN nos Tag Helpers.

````html
<abp-style-bundle name="MyStyleBundle">
    <abp-style src="/styles/my-style1.css" />
    <abp-style src="/styles/my-style2.css" />
    <abp-style src="https://cdn.abp.io/bootstrap.css" />
    <abp-style src="/styles/my-style3.css" />
    <abp-style src="/styles/my-style4.css" />
</abp-style-bundle>

<abp-script-bundle name="MyScriptBundle">
    <abp-script src="/scripts/my-script1.js" />
    <abp-script src="/scripts/my-script2.js" />
    <abp-script src="https://cdn.abp.io/bootstrap.js" />
    <abp-script src="/scripts/my-script3.js" />
    <abp-script src="/scripts/my-script4.js" />
</abp-script-bundle>
````

**Saída HTML:**

````html
<link rel="stylesheet" href="/__bundles/MyStyleBundle.C60C7B9C1F539659623BB6E7227A7C45.css?_v=638331889645002500" />
<link rel="stylesheet" href="https://cdn.abp.io/bootstrap.css" />
<link rel="stylesheet" href="/__bundles/MyStyleBundle.464328A06039091534650B0E049904C6.css?_v=638331889645012300" />

<script src="/__bundles/MyScriptBundle.55FDCBF2DCB9E0767AE6FA7487594106.js?_v=638331889645050410"></script>
<script src="https://cdn.abp.io/bootstrap.js"></script>
<script src="/__bundles/MyScriptBundle.191CB68AB4F41C8BF3A7AE422F19A3D2.js?_v=638331889645055490"></script>
````

## Temas

Os temas usam os *contributors* de pacote padrão para adicionar recursos de biblioteca aos layouts de página. Os temas também podem definir alguns bundles padrão/globais, para que qualquer módulo possa contribuir para esses bundles padrão/globais. Consulte a [documentação de *theming*](theming.md) para obter mais informações.

## Melhores Práticas e Sugestões

Sugere-se definir vários bundles para um aplicativo, cada um usado para diferentes finalidades.

*   **Bundle global**: Os bundles globais de estilo/script são incluídos em todas as páginas do aplicativo. Os temas já definem bundles globais de estilo e script. Seu módulo pode contribuir para eles.
*   **Bundles de layout**: Este é um bundle específico para um layout individual. Contém apenas recursos compartilhados entre todas as páginas que usam o layout. Use os tag helpers de *bundling* para criar o bundle como uma boa prática.
*   **Bundles de módulo**: Para recursos compartilhados entre as páginas de um módulo individual.
*   **Bundles de página**: Bundles específicos criados para cada página. Use os tag helpers de *bundling* para criar o bundle como uma melhor prática.

Estabeleça um equilíbrio entre desempenho, uso de largura de banda da rede e contagem de muitos bundles.

## Veja Também

*   [Gerenciamento de Pacotes do Lado do Cliente](client-side-package-management.md)
*   [*Theming*](theming.md)
