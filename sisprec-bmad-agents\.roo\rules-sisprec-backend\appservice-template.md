# Template: Application Service SISPREC

## Informações do Application Service

-   **Nome da Entidade**: {NomeEntidade}
-   **Módulo**: {Modulo}
-   **Tipo de Service**: {TipoService} (CRUD | ReadOnly | NoDelete | AtivaDesativa | Sincronizavel | Settings | Custom)
-   **Classe Base**: {ClasseBase} (BaseAppService | BaseReadOnlyAppService | BaseCrudAppService | BaseCrudNoDeleteAppService | BaseAtivaDesativaAppService | BaseSincronizavelAppService | SISPRECBaseSettingsAppService)
-   **Descrição**: {DescricaoService}

## Seleção da Classe Base

### Guia de Seleção

| Cenário                            | Classe Base                     | Características                           |
| ---------------------------------- | ------------------------------- | ----------------------------------------- |
| Entidade apenas para consulta      | `BaseReadOnlyAppService`        | Get, GetList apenas                       |
| Entidade com CRUD completo         | `BaseCrudAppService`            | Create, Update, Delete, Get, GetList      |
| Entidade que não pode ser excluída | `BaseCrudNoDeleteAppService`    | Create, Update, Get, GetList (sem Delete) |
| Entidade com estado ativo/inativo  | `BaseAtivaDesativaAppService`   | CRUD + AtivarDesativar                    |
| Entidade sincronizada com CJF      | `BaseSincronizavelAppService`   | CRUD + controle de sincronização          |
| Configurações do sistema           | `SISPRECBaseSettingsAppService` | Gerenciamento de settings                 |
| Operações específicas de negócio   | `BaseAppService`                | Flexibilidade total                       |

### Características das Classes Base

#### BaseReadOnlyAppService

-   **Quando usar**: Entidades de lookup, relatórios, dados de referência
-   **Operações**: Get, GetList
-   **Políticas**: VisualizarPolicyName

#### BaseCrudAppService

-   **Quando usar**: Entidades de domínio com operações completas
-   **Operações**: Create, Update, Delete, Get, GetList
-   **Políticas**: VisualizarPolicyName, GravarPolicyName
-   **Integração**: IBaseDomainManager para regras de negócio

#### BaseCrudNoDeleteAppService

-   **Quando usar**: Entidades críticas que não devem ser excluídas
-   **Operações**: Create, Update, Get, GetList
-   **Comportamento**: Delete lança InvalidOperationException

#### BaseAtivaDesativaAppService

-   **Quando usar**: Entidades com estado ativo/inativo
-   **Operações**: CRUD + AtivarDesativarAsync
-   **Requisito**: Entidade deve herdar de BaseAtivaDesativaEntity

#### BaseSincronizavelAppService

-   **Quando usar**: Entidades sincronizadas com sistemas externos
-   **Operações**: CRUD + controle de sincronização
-   **Comportamento**: Impede alteração de registros sincronizados
-   **Requisito**: Entidade deve herdar de BaseEntidadeDominio

#### SISPRECBaseSettingsAppService

-   **Quando usar**: Serviços de configuração do sistema
-   **Recursos**: ISettingManager, IBackgroundJobsService
-   **Métodos**: IsConfiguracaoAtiva

## 1. Application.Contracts - Interface e DTOs

### Arquivo: `src/TRF3.SISPREC.Application.Contracts/{Modulo}/I{NomeEntidade}AppService.cs`

```csharp
using System;
using System.Threading.Tasks;
using TRF3.SISPREC.{Modulo}.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.{Modulo}
{
    public interface I{NomeEntidade}AppService :
        ICrudAppService<{NomeEntidade}Dto, Guid, Get{NomeEntidade}ListDto, Create{NomeEntidade}Dto, Update{NomeEntidade}Dto>
    {
        // Métodos específicos de negócio
        Task<{NomeEntidade}Dto> AtivarAsync(Guid id);
        Task<{NomeEntidade}Dto> DesativarAsync(Guid id);
        Task<ListResultDto<{NomeEntidade}Dto>> GetLookupAsync();

        // Métodos específicos do domínio SISPREC
        Task<{NomeEntidade}Dto> ProcessarAsync(Guid id, Processar{NomeEntidade}Dto input);
        Task<bool> ValidarRegrasNegocioAsync(Guid id);
        Task<ListResultDto<{NomeEntidade}Dto>> GetByStatusAsync({NomeEntidade}Status status);
    }
}
```

### DTOs Específicos

#### Arquivo: `src/TRF3.SISPREC.Application.Contracts/{Modulo}/Dtos/Processar{NomeEntidade}Dto.cs`

```csharp
using System;
using System.ComponentModel.DataAnnotations;

namespace TRF3.SISPREC.{Modulo}.Dtos
{
    public class Processar{NomeEntidade}Dto
    {
        [Required]
        public string Observacao { get; set; }

        public DateTime? DataProcessamento { get; set; }

        public Guid? ResponsavelId { get; set; }

        // Propriedades específicas do processamento
        public bool ValidarAutomaticamente { get; set; } = true;

        public bool NotificarUsuario { get; set; } = true;
    }
}
```

## 2. Application - Implementação

### Arquivo: `src/TRF3.SISPREC.Application/{Modulo}/{NomeEntidade}AppService.cs`

#### Exemplo 1: BaseCrudAppService (Padrão)

```csharp
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using TRF3.SISPREC.{Modulo}.Dtos;
using TRF3.SISPREC.Permissions;
using TRF3.SISPREC.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC.{Modulo}
{
    public class {NomeEntidade}AppService :
        BaseCrudAppService<{NomeEntidade}, {NomeEntidade}Dto, {NomeEntidade}Dto, Guid, Get{NomeEntidade}ListDto, Create{NomeEntidade}Dto, Update{NomeEntidade}Dto>,
        I{NomeEntidade}AppService
    {
        protected override string VisualizarPolicyName => SISPRECPermissions.{NomeEntidade}.Default;
        protected override string GravarPolicyName => SISPRECPermissions.{NomeEntidade}.Create;

        public {NomeEntidade}AppService(
            IRepository<{NomeEntidade}> repository,
            IBaseDomainManager<{NomeEntidade}> manager) : base(repository, manager)
        {
        }

        // Métodos específicos de negócio
        public async Task<{NomeEntidade}Dto> ProcessarAsync(Guid id, Processar{NomeEntidade}Dto input)
        {
            var entity = await GetEntityByIdAsync(id);

            // Aplicar regras de negócio via DomainManager
            await Manager.ProcessarAsync(entity, input.Observacao);

            return await MapToGetOutputDtoAsync(entity);
        }
    }
}
```

#### Exemplo 2: BaseReadOnlyAppService

```csharp
namespace TRF3.SISPREC.{Modulo}
{
    public class {NomeEntidade}AppService :
        BaseReadOnlyAppService<{NomeEntidade}, {NomeEntidade}Dto, Guid, Get{NomeEntidade}ListDto>,
        I{NomeEntidade}AppService
    {
        protected override string VisualizarPolicyName => SISPRECPermissions.{NomeEntidade}.Default;

        public {NomeEntidade}AppService(IRepository<{NomeEntidade}> repository) : base(repository)
        {
        }

        public async Task<ListResultDto<{NomeEntidade}Dto>> GetLookupAsync()
        {
            var entities = await Repository.GetListAsync();
            return new ListResultDto<{NomeEntidade}Dto>(
                ObjectMapper.Map<List<{NomeEntidade}>, List<{NomeEntidade}Dto>>(entities)
            );
        }
    }
}
```

#### Exemplo 3: BaseAtivaDesativaAppService

```csharp
namespace TRF3.SISPREC.{Modulo}
{
    public class {NomeEntidade}AppService :
        BaseAtivaDesativaAppService<{NomeEntidade}, {NomeEntidade}Dto, {NomeEntidade}Dto, Guid, Get{NomeEntidade}ListDto, Create{NomeEntidade}Dto, Update{NomeEntidade}Dto>,
        I{NomeEntidade}AppService
    {
        protected override string VisualizarPolicyName => SISPRECPermissions.{NomeEntidade}.Default;
        protected override string GravarPolicyName => SISPRECPermissions.{NomeEntidade}.Create;

        public {NomeEntidade}AppService(
            IRepository<{NomeEntidade}> repository,
            IBaseDomainManager<{NomeEntidade}> manager) : base(repository, manager)
        {
        }

        // Método AtivarDesativarAsync já está disponível na classe base

        public async Task<{NomeEntidade}Dto> AtivarAsync(Guid id)
        {
            await AtivarDesativarAsync(id); // Usa o método da classe base
            return await GetAsync(id);
        }
    }
}
```

#### Exemplo 4: BaseSincronizavelAppService

```csharp
namespace TRF3.SISPREC.{Modulo}
{
    public class {NomeEntidade}AppService :
        BaseSincronizavelAppService<{NomeEntidade}, {NomeEntidade}Dto, {NomeEntidade}Dto, Guid, Get{NomeEntidade}ListDto, Create{NomeEntidade}Dto, Update{NomeEntidade}Dto>,
        I{NomeEntidade}AppService
    {
        protected override string VisualizarPolicyName => SISPRECPermissions.{NomeEntidade}.Default;
        protected override string GravarPolicyName => SISPRECPermissions.{NomeEntidade}.Create;

        public {NomeEntidade}AppService(
            IRepository<{NomeEntidade}> repository,
            ISincronizavelManager<{NomeEntidade}> manager) : base(repository, manager)
        {
        }

        // Controle de sincronização já implementado na classe base

        public async Task<bool> ValidarRegrasNegocioAsync(Guid id)
        {
            var entity = await GetEntityByIdAsync(id);
            return await Manager.ValidarRegrasAsync(entity);
        }
    }
}
```

#### Exemplo 5: SISPRECBaseSettingsAppService

```csharp
namespace TRF3.SISPREC.{Modulo}
{
    public class {NomeEntidade}SettingsAppService : SISPRECBaseSettingsAppService, I{NomeEntidade}SettingsAppService
    {
        public {NomeEntidade}SettingsAppService(
            ISettingManager settingManager,
            IBackgroundJobsService backgroundJobsService) : base(settingManager, backgroundJobsService)
        {
        }

        public async Task<bool> ProcessarConfiguracaoAsync(string configKey, object value)
        {
            if (!IsConfiguracaoAtiva(configKey))
            {
                return false;
            }

            await SettingManager.SetGlobalAsync(configKey, value.ToString());

            // Agendar job de processamento
            await BackgroundJobsService.EnqueueAsync<ProcessarConfiguracaoJob>(new { ConfigKey = configKey });

            return true;
        }
    }
}
```

#### Exemplo 6: BaseAppService (Operações Customizadas)

```csharp
namespace TRF3.SISPREC.{Modulo}
{
    public class {NomeEntidade}AppService : BaseAppService, I{NomeEntidade}AppService
    {
        private readonly I{NomeEntidade}Repository _{nomeEntidade}Repository;
        private readonly I{NomeEntidade}DomainService _{nomeEntidade}DomainService;

        public {NomeEntidade}AppService(
            I{NomeEntidade}Repository repository,
            I{NomeEntidade}DomainService domainService)
        {
            _{nomeEntidade}Repository = repository;
            _{nomeEntidade}DomainService = domainService;
            _{nomeEntidade}Repository = repository;
            _{nomeEntidade}DomainService = {nomeEntidade}DomainService;
            _logger = logger;

            // Configuração de permissões SISPREC
            GetPolicyName = SISPRECPermissoes.{NomeEntidade}.Visualizar;
            GetListPolicyName = SISPRECPermissoes.{NomeEntidade}.Visualizar;
            CreatePolicyName = SISPRECPermissoes.{NomeEntidade}.Gravar;
            UpdatePolicyName = SISPRECPermissoes.{NomeEntidade}.Gravar;
            DeletePolicyName = SISPRECPermissoes.{NomeEntidade}.Gravar;
        }

        protected override async Task<IQueryable<{NomeEntidade}>> CreateFilteredQueryAsync(Get{NomeEntidade}ListDto input)
        {
            var query = await ReadOnlyRepository.GetQueryableAsync();

            return query
                .WhereIf(!input.Filtro.IsNullOrWhiteSpace(), x => x.Nome.Contains(input.Filtro))
                .WhereIf(input.Status.HasValue, x => x.Status == input.Status)
                .WhereIf(input.DataInicio.HasValue, x => x.CreationTime >= input.DataInicio)
                .WhereIf(input.DataFim.HasValue, x => x.CreationTime <= input.DataFim);
        }

        public override async Task<{NomeEntidade}Dto> CreateAsync(Create{NomeEntidade}Dto input)
        {
            // Validações específicas de negócio
            await ValidarCriacaoAsync(input);

            // Criação usando domain service
            var entity = await _{nomeEntidade}DomainService.CriarAsync(
                input.Nome,
                input.Tipo,
                input.Descricao
            );

            // Salvar no repositório
            await _{nomeEntidade}Repository.InsertAsync(entity);

            _logger.LogInformation("Nova {NomeEntidade} criada: {Id}", entity.Id);

            return await MapToGetOutputDtoAsync(entity);
        }

        public override async Task<{NomeEntidade}Dto> UpdateAsync(Guid id, Update{NomeEntidade}Dto input)
        {
            var entity = await _{nomeEntidade}Repository.GetAsync(id);

            // Validações específicas de negócio
            await ValidarEdicaoAsync(entity, input);

            // Atualização usando métodos de domínio
            entity.AlterarNome(input.Nome);
            entity.AlterarDescricao(input.Descricao);

            if (input.Tipo != entity.Tipo)
            {
                await _{nomeEntidade}DomainService.AlterarTipoAsync(entity, input.Tipo);
            }

            await _{nomeEntidade}Repository.UpdateAsync(entity);

            _logger.LogInformation("{NomeEntidade} atualizada: {Id}", entity.Id);

            return await MapToGetOutputDtoAsync(entity);
        }

        [Authorize(SISPRECPermissoes.{NomeEntidade}.Gravar)]
        public async Task<{NomeEntidade}Dto> AtivarAsync(Guid id)
        {
            var entity = await _{nomeEntidade}Repository.GetAsync(id);

            // Validar se pode ativar
            if (!await _{nomeEntidade}DomainService.PodeAtivarAsync(entity))
            {
                throw new UserFriendlyException("Não é possível ativar esta {nomeEntidade} no momento");
            }

            entity.Ativar();
            await _{nomeEntidade}Repository.UpdateAsync(entity);

            _logger.LogInformation("{NomeEntidade} ativada: {Id}", entity.Id);

            return await MapToGetOutputDtoAsync(entity);
        }

        [Authorize(SISPRECPermissoes.{NomeEntidade}.Gravar)]
        public async Task<{NomeEntidade}Dto> DesativarAsync(Guid id)
        {
            var entity = await _{nomeEntidade}Repository.GetAsync(id);

            // Validar se pode desativar
            if (!await _{nomeEntidade}DomainService.PodeDesativarAsync(entity))
            {
                throw new UserFriendlyException("Não é possível desativar esta {nomeEntidade} no momento");
            }

            entity.Desativar();
            await _{nomeEntidade}Repository.UpdateAsync(entity);

            _logger.LogInformation("{NomeEntidade} desativada: {Id}", entity.Id);

            return await MapToGetOutputDtoAsync(entity);
        }

        public async Task<ListResultDto<{NomeEntidade}Dto>> GetLookupAsync()
        {
            var entities = await _{nomeEntidade}Repository.GetListAsync(
                status: {NomeEntidade}Status.Ativo,
                sorting: nameof({NomeEntidade}.Nome)
            );

            return new ListResultDto<{NomeEntidade}Dto>(
                ObjectMapper.Map<List<{NomeEntidade}>, List<{NomeEntidade}Dto>>(entities)
            );
        }

        [Authorize(SISPRECPermissoes.{NomeEntidade}.Gravar)]
        public async Task<{NomeEntidade}Dto> ProcessarAsync(Guid id, Processar{NomeEntidade}Dto input)
        {
            var entity = await _{nomeEntidade}Repository.GetAsync(id);

            // Validar regras de negócio específicas do SISPREC
            if (!await ValidarRegrasNegocioAsync(id))
            {
                throw new UserFriendlyException("Entidade não atende aos critérios para processamento");
            }

            // Processar usando domain service
            await _{nomeEntidade}DomainService.ProcessarAsync(
                entity,
                input.Observacao,
                CurrentUser.Id,
                input.DataProcessamento ?? Clock.Now
            );

            await _{nomeEntidade}Repository.UpdateAsync(entity);

            _logger.LogInformation("{NomeEntidade} processada: {Id} por {UserId}", entity.Id, CurrentUser.Id);

            return await MapToGetOutputDtoAsync(entity);
        }

        public async Task<bool> ValidarRegrasNegocioAsync(Guid id)
        {
            var entity = await _{nomeEntidade}Repository.GetAsync(id);
            return await _{nomeEntidade}DomainService.ValidarRegrasNegocioAsync(entity);
        }

        public async Task<ListResultDto<{NomeEntidade}Dto>> GetByStatusAsync({NomeEntidade}Status status)
        {
            var entities = await _{nomeEntidade}Repository.GetListAsync(status: status);

            return new ListResultDto<{NomeEntidade}Dto>(
                ObjectMapper.Map<List<{NomeEntidade}>, List<{NomeEntidade}Dto>>(entities)
            );
        }

        // Métodos privados de validação
        private async Task ValidarCriacaoAsync(Create{NomeEntidade}Dto input)
        {
            // Validar se já existe com o mesmo nome
            var existente = await _{nomeEntidade}Repository.FindByNomeAsync(input.Nome);
            if (existente != null)
            {
                throw new UserFriendlyException($"Já existe uma {nomeEntidade} com o nome '{input.Nome}'");
            }

            // Outras validações específicas
            if (input.Tipo == {NomeEntidade}Tipo.Especial && !await CurrentUser.IsInRoleAsync("Admin"))
            {
                throw new UserFriendlyException("Apenas administradores podem criar {nomeEntidade}s especiais");
            }
        }

        private async Task ValidarEdicaoAsync({NomeEntidade} entity, Update{NomeEntidade}Dto input)
        {
            // Validar se pode editar
            if (entity.Status == {NomeEntidade}Status.Finalizado)
            {
                throw new UserFriendlyException("Não é possível editar uma {nomeEntidade} finalizada");
            }

            // Validar mudança de nome
            if (input.Nome != entity.Nome)
            {
                var existente = await _{nomeEntidade}Repository.FindByNomeAsync(input.Nome);
                if (existente != null && existente.Id != entity.Id)
                {
                    throw new UserFriendlyException($"Já existe uma {nomeEntidade} com o nome '{input.Nome}'");
                }
            }
        }
    }
}
```

## 3. AutoMapper Profile

### Arquivo: `src/TRF3.SISPREC.Application/{Modulo}/{NomeEntidade}AutoMapperProfile.cs`

```csharp
using AutoMapper;
using TRF3.SISPREC.{Modulo}.Dtos;

namespace TRF3.SISPREC.{Modulo}
{
    public class {NomeEntidade}AutoMapperProfile : Profile
    {
        public {NomeEntidade}AutoMapperProfile()
        {
            CreateMap<{NomeEntidade}, {NomeEntidade}Dto>()
                .ForMember(dest => dest.StatusDescricao, opt => opt.MapFrom(src => src.Status.ToString()))
                .ForMember(dest => dest.TipoDescricao, opt => opt.MapFrom(src => src.Tipo.ToString()));

            CreateMap<Create{NomeEntidade}Dto, {NomeEntidade}>();
            CreateMap<Update{NomeEntidade}Dto, {NomeEntidade}>();

            // Mapeamentos específicos para lookup
            CreateMap<{NomeEntidade}, LookupDto<Guid>>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.DisplayName, opt => opt.MapFrom(src => src.Nome));
        }
    }
}
```

## Checklist de Implementação

### Application Service

-   [ ] Interface I{NomeEntidade}AppService criada
-   [ ] Implementação {NomeEntidade}AppService completa
-   [ ] Herança de CrudAppService configurada
-   [ ] Permissões ABP configuradas
-   [ ] Logging implementado
-   [ ] Validações de negócio implementadas

### DTOs e Contratos

-   [ ] DTOs básicos (Create, Update, Get, List)
-   [ ] DTOs específicos de negócio (Processar, etc.)
-   [ ] Validações via Data Annotations
-   [ ] AutoMapper profiles configurados

### Integração com Domain

-   [ ] Domain Service injetado e utilizado
-   [ ] Repository injetado e utilizado
-   [ ] Regras de negócio delegadas ao domain
-   [ ] Métodos de domínio utilizados

### Qualidade e Testes

-   [ ] Testes unitários implementados
-   [ ] Testes de integração criados
-   [ ] Documentação XML adicionada
-   [ ] Code review realizado

### Específico SISPREC

-   [ ] Padrões SISPREC seguidos
-   [ ] Regras de negócio judiciário implementadas
-   [ ] Integração com auditoria ABP
-   [ ] Permissões específicas configuradas

