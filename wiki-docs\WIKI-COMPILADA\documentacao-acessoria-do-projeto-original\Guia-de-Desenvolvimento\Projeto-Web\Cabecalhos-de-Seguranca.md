# Cabeçalhos de Segurança

O ABP Framework permite que você adicione cabeçalhos de segurança usados com frequência em seu aplicativo. Os seguintes cabeçalhos de segurança serão adicionados como cabeçalhos de resposta ao seu aplicativo se você usar o middleware `UseAbpSecurityHeaders`:

* `X-Content-Type-Options`: Diz ao navegador para não tentar adivinhar qual poderia ser o tipo MIME de um recurso e apenas usar qual tipo MIME o servidor retornou.
* `X-XSS-Protection`: Este é um recurso do Internet Explorer, Chrome e Safari que impede que as páginas sejam carregadas quando detectam ataques de cross-site scripting (XSS) refletidos.
* `X-Frame-Options`: Este cabeçalho pode ser usado para indicar se um navegador deve ou não ter permissão para renderizar uma página em uma tag `<iframe>`. Ao especificar este valor de cabeçalho como *SAMEORIGIN*, você pode fazer com que ele seja exibido em um frame na mesma origem da própria página.
* `Content-Security-Policy`: Este cabeçalho de resposta permite restringir quais recursos (como JavaScript, CSS, imagens, manifests, etc.) podem ser carregados e os URLs dos quais eles podem ser carregados. Este cabeçalho de segurança só será adicionado se você configurar a classe `AbpSecurityHeadersOptions` e habilitá-lo.

## Configuração

### AbpSecurityHeadersOptions

`AbpSecurityHeadersOptions` é a classe principal para habilitar o cabeçalho `Content-Security-Policy`, definir seu valor e definir outros cabeçalhos de segurança que você deseja adicionar ao seu aplicativo.

**Exemplo:**

```csharp
Configure<AbpSecurityHeadersOptions>(options => 
{
    options.UseContentSecurityPolicyHeader = true; //false por padrão
    options.ContentSecurityPolicyValue = "object-src 'none'; form-action 'self'; frame-ancestors 'none'"; //valor padrão

    //adicionando cabeçalhos de segurança adicionais
    options.Headers["Referrer-Policy"] = "no-referrer";
});
```

> Se o cabeçalho for o mesmo, os cabeçalhos de segurança adicionais que você definiu têm precedência sobre os cabeçalhos de segurança padrão. Em outras palavras, ele substitui os valores dos cabeçalhos de segurança padrão.

## Middleware de Cabeçalhos de Segurança

O middleware de Cabeçalhos de Segurança é um middleware de pipeline de solicitação ASP.NET Core que adiciona cabeçalhos de segurança predefinidos ao seu aplicativo, incluindo `X-Content-Type-Options`, `X-XSS-Protection` e `X-Frame-Options`. Além disso, este middleware também inclui esses cabeçalhos de segurança exclusivos em seu aplicativo se você configurar o `AbpSecurityHeadersOptions` conforme mencionado acima.

**Exemplo:**

```csharp
app.UseAbpSecurityHeaders();
```

> Você pode adicionar este middleware após `app.UseRouting()` no método `OnApplicationInitialization` da sua classe de módulo para registrá-lo no pipeline de solicitação. Este middleware já está configurado nos [Templates de Inicialização do ABP Commercial](https://docs.abp.io/en/commercial/latest/startup-templates/index), então você não precisa adicioná-lo manualmente se estiver usando um desses templates de inicialização.

Depois disso, você registrou o middleware `UseAbpSecurityHeaders` no pipeline de solicitação, os cabeçalhos de segurança definidos serão mostrados nos cabeçalhos de resposta como na figura abaixo:

![](/ABP-Docs/images/security-response-headers.png)

## Script Nonce da Política de Segurança de Conteúdo

O ABP Framework fornece uma propriedade para adicionar um valor nonce script-src dinâmico ao cabeçalho Content-Security-Policy. Com este recurso, ele adiciona automaticamente um valor nonce dinâmico ao lado do cabeçalho. E com a ajuda do tag helper de script, ele adiciona este valor [`script nonce`](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/nonce) às tags de script em suas páginas (o `ScriptNonceTagHelper` no namespace `Volo.Abp.AspNetCore.Mvc.UI.Bundling` deve ser anexado como um taghelper.).
> Se você precisar adicionar o script nonce manualmente, você pode usar 'Html.GetScriptNonce()' para adicionar o valor nonce ou 'Html.GetScriptNonceAttribute()' para adicionar o valor do atributo nonce.

Este recurso está desabilitado por padrão. Você pode habilitá-lo definindo a propriedade `UseContentSecurityPolicyScriptNonce` da classe `AbpSecurityHeadersOptions` como `true`.

### Ignorar Script Nonce

Você pode ignorar o script nonce para algumas páginas ou alguns seletores. Você pode usar as propriedades `IgnoredScriptNoncePaths` e `IgnoredScriptNonceSelectors` da classe `AbpSecurityHeadersOptions`.

**Exemplo:**

```csharp
Configure<AbpSecurityHeadersOptions>(options => 
{
    //adicionando script-src nonce
    options.UseContentSecurityPolicyScriptNonce = true; //false por padrão

    //ignorar a fonte script nonce para esses caminhos
    options.IgnoredScriptNoncePaths.Add("/my-page");

    //ignorar script nonce por Elsa Workflows e outros seletores
    options.IgnoredScriptNonceSelectors.Add(context =>
    {
        var endpoint = context.GetEndpoint();
        return Task.FromResult(endpoint?.Metadata.GetMetadata<PageRouteMetadata>()?.RouteTemplate == "/{YOURHOSTPAGE}");
    });
});
```

### Ignorar Cabeçalhos de Segurança Abp

Você pode ignorar os Cabeçalhos de Segurança Abp para algumas ações ou páginas. Você pode usar o atributo `IgnoreAbpSecurityHeaderAttribute` para isso.

**Exemplo:**

```csharp
@using Volo.Abp.AspNetCore.Security
@attribute [IgnoreAbpSecurityHeaderAttribute]
```

**Exemplo:**

```csharp
[IgnoreAbpSecurityHeaderAttribute]
public class IndexModel : AbpPageModel
{
    public void OnGet()
    {
    }
}
