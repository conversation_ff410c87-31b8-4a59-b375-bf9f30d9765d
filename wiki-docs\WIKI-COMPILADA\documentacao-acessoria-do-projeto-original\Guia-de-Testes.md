# Índice do Guia de Testes

Este guia fornece informações sobre como realizar testes no projeto SISPREC. Abaixo, você encontrará um índice com links para as diferentes seções do guia:

- [Instruções Gerais](Guia-de-Testes/Instruções-Gerais.md): Este documento apresenta os princípios gerais para a criação de testes eficazes, incluindo o uso de bibliotecas como NSubstitute, Shouldly e Bogus, além de boas práticas para testes unitários, de Application Services e Domain Services.
- [Carga de Dados](Guia-de-Testes/Carga-de-Dados.md): Este documento explica como utilizar a carga de dados em SQL para o SQLite, que fornece registros básicos para os testes que usam banco de dados. Ele também detalha como usar a classe `SISPRECTestConsts` para acessar os IDs dos registros inseridos.
- [Domain Services](Guia-de-Testes/DomainServices.md): Este guia demonstra como implementar testes unitários para serviços de domínio no SISPREC, fornecendo exemplos práticos e padrões recomendados, incluindo o uso de NSubstitute para mocks, Shouldly para asserções e Bogus para dados de teste.
- [Application Services](Guia-de-Testes/ApplicationServices.md): Este guia demonstra como implementar testes de integração para serviços de aplicação (Application Services) no ABP Framework 8, incluindo a configuração do ambiente de teste, o uso de `WithUnitOfWorkAsync` e exemplos de testes CRUD, validações, consultas e regras de negócio.
- [Testes Web](Guia-de-Testes/Testes-Web.md): Este guia demonstra como implementar testes automatizados para a camada web do SISPREC, focando nos testes de Razor Pages, incluindo testes da página de índice e dos modais de criação, edição e detalhes.
- [Mocks](Guia-de-Testes/Mocks.md): Este documento fornece dicas sobre como usar o Bogus para criar fakers para mocks, como usar `MockQueryable.NSubstitute` para simular `DbSet` e `IQueryable` em mocks de repositórios, e como usar `Arg.Any<>()` e `Arg.Is<Expression<Func<T, bool>>>()` para mockar retornos de métodos.
- [Métodos com InlineData](Guia-de-Testes/Métodos-com-InlineData.md): Este documento explica como usar a anotação `[InlineData]` para parametrizar testes no xUnit, permitindo executar o mesmo teste com diferentes conjuntos de dados, especialmente útil para validar a lógica de negócios em diferentes cenários.
- [Views SQL](Guia-de-Testes/Views-SQL.md): Este documento (em construção) tem como objetivo explicar como criar views para o SQLite para testes.
- [Mock de dependências](Guia-de-Testes/Mock-de-Dependências.md): Este documento fornece instruções detalhadas sobre como usar mocks de dependências.