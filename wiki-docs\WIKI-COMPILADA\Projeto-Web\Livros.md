[[_TOC_]]

# Projeto Web - Livros

O projeto Web contém a interface de usuário (UI) implementada usando ASP.NET Core MVC com Razor Pages. Esta seção aborda a implementação específica para a entidade Livro.

## Estrutura do Projeto

```
TRF3.SISPREC.Web/
├── Pages/
│   └── Livros/                 # Páginas relacionadas a Livros
│       ├── Index.cshtml        # Página principal (listagem)
│       ├── CreateModal.cshtml  # Modal de criação
│       ├── EditModal.cshtml    # Modal de edição
│       ├── DetalheModal.cshtml # Modal de detalhes
│       ├── index.css          # Estilos específicos
│       ├── index.js           # JavaScript da listagem
│       ├── CreateModal.js     # JavaScript do modal de criação
│       ├── EditModal.js       # JavaScript do modal de edição
│       └── ViewModels/        # ViewModels específicos
└── Menus/                     # Configuração de menus
```

## Páginas Razor

### Página de Listagem (Index.cshtml)

A página principal que exibe a lista de livros em uma grid DataTables com funcionalidades de paginação, ordenação e filtros.

```csharp
@page
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using TRF3.SISPREC.Web.Pages.Livros
@using TRF3.SISPREC.Web.Menus
@model IndexModel
@inject IPageLayout PageLayout
@{
    // Configura o layout da página usando serviços do ABP Framework
    PageLayout.Content.Title = "Livro";
    PageLayout.Content.BreadCrumb.Add("Livros");
    PageLayout.Content.MenuItemName = SISPRECMenus.Livro;
}

@* 
    Página principal de listagem de livros usando componentes do ABP Framework.
    
    Características principais:
    - Filtros expansíveis
    - DataTable com ordenação e paginação
    - Botões de ação para CRUD
    - Integração com modais
    
    Tag Helpers e componentes utilizados:
    - abp-card: Container principal com cabeçalho e corpo
    - abp-collapse: Seção expansível para filtros
    - abp-table: DataTable com recursos avançados
    - abp-button: Botões estilizados do ABP
    - input-dinheiro: Componente customizado para valores monetários
*@

@* Inclui scripts necessários para a página *@
@section scripts
{
    <abp-script src="/libs/jquery-mask-plugin/jquery.mask.js" />
    <abp-script src="/Pages/Livros/index.js" />
}

@* Inclui estilos específicos da página *@
@section styles
{
    <abp-style src="/Pages/Livros/index.css" />
}

@* Container principal em card *@
<abp-card>
    @* Cabeçalho com botão de filtro e novo registro *@
    <abp-card-header>
        <abp-row class="justify-content-between align-items-center">
            <abp-column>
                <a abp-collapse-id="LivroCollapse" class="text-secondary">Filtrar</a>
            </abp-column>
            <abp-column class="text-end">
                <abp-button id="NewLivroButton" text="Novo" icon="plus" button-type="Primary" />
            </abp-column>
        </abp-row>
    </abp-card-header>

    @* Corpo do card com filtros e tabela *@
    <abp-card-body>
        @* Formulário de filtros expansível *@
        <form abp-model="LivroFilter" id="LivroFilter" required-symbols="false" data-filtro-from="true">
            <abp-collapse-body id="LivroCollapse">
                <abp-row>
                    @* Filtro por ID *@
                    <abp-column size="_1">
                        <abp-input asp-for="LivroFilter.LivroId" />
                    </abp-column>

                    @* Filtro por título *@
                    <abp-column size="_3">
                        <abp-input asp-for="LivroFilter.Titulo" />
                    </abp-column>

                    @* Filtro por categoria *@
                    <abp-column size="_2">
                        <abp-select asp-for="LivroFilter.Categoria" />
                    </abp-column>

                    @* Filtro por data de publicação (início) *@
                    <abp-column size="_2">
                        <abp-date-picker asp-for="LivroFilter.DataPublicacaoInicio" />
                    </abp-column>

                    @* Filtro por data de publicação (fim) *@
                    <abp-column size="_2">
                        <abp-date-picker asp-for="LivroFilter.DataPublicacaoFinal" />
                    </abp-column>

                    @* Filtro por preço mínimo *@
                    <abp-column size="_1">
                        <input-dinheiro asp-for="LivroFilter.PrecoMin" com-simbolo-moeda="false" />
                    </abp-column>

                    @* Filtro por preço máximo *@
                    <abp-column size="_1">
                        <input-dinheiro asp-for="LivroFilter.PrecoMax" com-simbolo-moeda="false" />
                    </abp-column>
                </abp-row>
            </abp-collapse-body>
        </form>

        @* Tabela de livros com suporte a ordenação e paginação *@
        <abp-table striped-rows="true" id="LivroTable" class="nowrap" />
    </abp-card-body>
</abp-card>

```

### Modal de Criação (CreateModal.cshtml)

Modal para criar um novo livro, usando formulário dinâmico do ABP Framework.

```csharp
@page
@using System.Globalization
@using TRF3.SISPREC.Enums
@using TRF3.SISPREC.Livros
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal
@model TRF3.SISPREC.Web.Pages.Livros.CreateModalModel
@{
    Layout = null; // Modal não precisa de layout pois será carregada via AJAX
}

@* 
    Modal de criação de livro usando componentes do ABP Framework.
    
    Principais características:
    - Formulário AJAX com validação client-side
    - Componentes ABP Bootstrap para layout responsivo
    - Select2 para seleção múltipla de autores
    - Validações e máscaras nos campos
    
    Tag Helpers utilizados:
    - abp-modal: Container principal da modal com suporte a scroll
    - abp-input: Campos de entrada com validação integrada
    - abp-select: Dropdown para enums com tradução automática
    - abp-date-picker: Seletor de data com formato localizado
    - input-dinheiro: Componente customizado para valores monetários
*@
<form id="NovoLivroForm" method="post" data-ajaxForm="true" asp-page="CreateModal" abp-model="ViewModel">
    <abp-modal scrollable="true" size="Large" id="CreateModalLivro">
        <abp-modal-header title="Cadastro de Livro"></abp-modal-header>
        <abp-modal-body>
            @* Primeira linha: Título, Categoria e Disponibilidade *@
            <abp-row>
                <abp-column size="_6">
                    <abp-input asp-for="ViewModel.Titulo" placeholder="Digite o título do livro" />
                </abp-column>
                <abp-column size="_3">
                    <abp-select asp-for="ViewModel.Categoria"
                                asp-items="EnumExtensions.GetEnumSelectList<TRF3.SISPREC.Enums.ECategoriaLivro>()"
                                placeholder="Selecione a categoria" />
                </abp-column>
                <abp-column size="_3">
                    <label class="form-label">Disponível</label>
                    <input class="form-check-input d-block" type="checkbox" asp-for="ViewModel.Disponivel" checked />
                </abp-column>
            </abp-row>

            @* Segunda linha: Data de Publicação, Preço e Quantidade *@
            <abp-row>
                <abp-column size="_4">
                    <abp-date-picker asp-for="ViewModel.DataPublicacao" />
                </abp-column>

                <abp-column size="_4">
                    <input-dinheiro asp-for="ViewModel.Preco" />
                </abp-column>

                <abp-column size="_4">
                    <abp-input asp-for="ViewModel.Quantidade"
                               type="number"
                               min="0"
                               placeholder="Digite a quantidade" />
                </abp-column>
            </abp-row>

            @* Terceira linha: Descrição em texto multilinha *@
            <abp-row>
                <abp-column size="_12">
                    <abp-input asp-for="ViewModel.Descricao"
                               placeholder="Digite uma descrição para o livro"
                               rows="4" />
                </abp-column>
            </abp-row>

            @* Quarta linha: Select2 para múltiplos autores *@
            <abp-row>
                <abp-column size="_12">
                    <label class="form-label">Autores</label>
                    <select class="form-control" maxlength="500" asp-for="ViewModel.AutoresIds"></select>
                </abp-column>
            </abp-row>
        </abp-modal-body>
        <abp-modal-footer buttons="@(AbpModalButtons.Cancel|AbpModalButtons.Save)"></abp-modal-footer>
    </abp-modal>
</form>

```

### Modal de Edição (EditModal.cshtml)

Modal para editar um livro existente, similar ao modal de criação.

```csharp
@page
@using TRF3.SISPREC.Enums
@using TRF3.SISPREC.Livros
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal;
@model TRF3.SISPREC.Web.Pages.Livros.EditModalModel
@{
    Layout = null; // Modal não precisa de layout pois será carregada via AJAX
}

@* 
    Modal de edição de livro usando componentes do ABP Framework.
    
    Características principais:
    - Formulário AJAX com validação client-side
    - Campos pré-preenchidos com dados do livro
    - Select2 para seleção múltipla de autores
    - Mesma estrutura visual do formulário de criação
    
    Tag Helpers utilizados:
    - abp-modal: Container principal da modal com suporte a scroll
    - abp-input: Campos de entrada com validação integrada
    - abp-select: Dropdown para enums com tradução automática
    - abp-date-picker: Seletor de data com formato localizado
    - input-dinheiro: Componente customizado para valores monetários
*@
<form method="post" data-ajaxForm="true" asp-page="EditModal" abp-model="ViewModel">
    <abp-modal scrollable="true" size="Large" id="EditModalLivro">
        <abp-modal-header title="Alterar Livro"></abp-modal-header>
        <abp-modal-body>
            @* Campo oculto para o ID do livro *@
            <abp-input asp-for="Id" />

            @* Primeira linha: Título, Categoria e Disponibilidade *@
            <abp-row>
                <abp-column size="_6">
                    <abp-input asp-for="ViewModel.Titulo" />
                </abp-column>
                <abp-column size="_3">
                    <abp-select asp-for="ViewModel.Categoria"
                                asp-items="EnumExtensions.GetEnumSelectList<TRF3.SISPREC.Enums.ECategoriaLivro>()" />
                </abp-column>
                <abp-column size="_3">
                    <label class="form-label">Disponível</label>
                    <input class="form-check-input d-block" type="checkbox" asp-for="ViewModel.Disponivel" />
                </abp-column>
            </abp-row>

            @* Segunda linha: Data de Publicação, Preço e Quantidade *@
            <abp-row>
                <abp-column size="_4">
                    <abp-date-picker asp-for="ViewModel.DataPublicacao" />
                </abp-column>

                <abp-column size="_4">
                    <input-dinheiro asp-for="ViewModel.Preco" />
                </abp-column>

                <abp-column size="_4">
                    <abp-input asp-for="ViewModel.Quantidade"
                               type="number"
                               min="0"
                               placeholder="Digite a quantidade" />
                </abp-column>
            </abp-row>

            @* Terceira linha: Descrição em texto multilinha *@
            <abp-row>
                <abp-column size="_12">
                    <abp-input asp-for="ViewModel.Descricao"
                               type="textarea"
                               placeholder="Digite uma descrição para o livro"
                               rows="4" />
                </abp-column>
            </abp-row>

            @* Quarta linha: Select2 para múltiplos autores *@
            <abp-row>
                <abp-column size="_12">
                    <label class="form-label">Autores</label>
                    <select class="form-control"
                            asp-for="ViewModel.AutoresIds"></select>
                    @* Campo oculto com dicionário de autores para inicialização do Select2 *@
                    <input type="hidden" asp-for="ViewModel.AutoresDicionario" value='@(Model.ViewModel.AutoresDicionario == null ? "" : System.Text.Json.JsonSerializer.Serialize(Model.ViewModel.AutoresDicionario))' />
                </abp-column>
            </abp-row>
        </abp-modal-body>
        <abp-modal-footer buttons="@(AbpModalButtons.Cancel|AbpModalButtons.Save)"></abp-modal-footer>
    </abp-modal>
</form>

```

### Modal de Detalhes (DetalheModal.cshtml)

Modal para visualizar os detalhes de um livro em modo somente leitura.

```csharp
@page
@using TRF3.SISPREC.Enums
@using TRF3.SISPREC.Localization
@using Microsoft.AspNetCore.Mvc.Localization
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal;
@using TRF3.SISPREC.Livros;
@model TRF3.SISPREC.Web.Pages.Livros.DetalheModalModel
@{
    Layout = null; // Modal não precisa de layout pois será carregada via AJAX
}

@* 
    Modal de visualização de detalhes do livro usando componentes do ABP Framework.
    
    Características principais:
    - Todos os campos são somente leitura (disabled="true")
    - Mantém a mesma estrutura visual do formulário de criação
    - Usa os mesmos componentes ABP Bootstrap para consistência
    - Select2 de autores é carregado com dados pré-selecionados
    
    Tag Helpers utilizados:
    - abp-modal: Container principal da modal com suporte a scroll
    - abp-input: Campos de entrada desabilitados
    - abp-select: Dropdown para enums com tradução automática
    - abp-date-picker: Seletor de data formatado
    - input-dinheiro: Componente customizado para valores monetários
*@
<abp-modal size="Large" scrollable="true" id="DetalheModalLivro">
    <abp-modal-header title="Detalhes do Livro"></abp-modal-header>
    <abp-modal-body>
        @* Primeira linha: ID, Título, Categoria e Disponibilidade *@
        <abp-row>
            <abp-column size="_1">
                <abp-input asp-for="ViewModel.LivroId" disabled="true" />
            </abp-column>
            <abp-column size="_5">
                <abp-input asp-for="ViewModel.Titulo" disabled="true" />
            </abp-column>
            <abp-column size="_3">
                <abp-select asp-for="ViewModel.Categoria"
                            asp-items="EnumExtensions.GetEnumSelectList<TRF3.SISPREC.Enums.ECategoriaLivro>()" disabled="true" />
            </abp-column>
            <abp-column size="_3">
                <label class="form-label">Disponível</label>
                <input class="form-check-input d-block" type="checkbox" asp-for="ViewModel.Disponivel" disabled="true" />
            </abp-column>
        </abp-row>

        @* Segunda linha: Data de Publicação, Preço e Quantidade *@
        <abp-row>
            <abp-column size="_4">
                <abp-date-picker asp-for="ViewModel.DataPublicacao" disabled="true" />
            </abp-column>

            <abp-column size="_4">
                <input-dinheiro asp-for="ViewModel.Preco" disabled="true" />
            </abp-column>

            <abp-column size="_4">
                <abp-input asp-for="ViewModel.Quantidade"
                           type="number"
                           min="0"
                           placeholder="Digite a quantidade" disabled="true" />
            </abp-column>
        </abp-row>

        @* Terceira linha: Descrição em texto multilinha *@
        <abp-row>
            <abp-column size="_12">
                <abp-input asp-for="ViewModel.Descricao" rows="4" disabled="true" />
            </abp-column>
        </abp-row>

        @* Quarta linha: Select2 de autores pré-carregado e desabilitado *@
        <abp-row>
            <abp-column size="_12" aria-disabled="true" style="pointer-events: none !important;cursor: not-allowed;cursor: not-allowed;">
                <label class="form-label">Autores</label>
                <select class="form-control" asp-for="ViewModel.AutoresIds" disabled="true"></select>
                @* Campo oculto com dicionário de autores para inicialização do Select2 *@
                <input type="hidden" asp-for="ViewModel.AutoresDicionario" value='@(Model.ViewModel.AutoresDicionario == null ? "" : System.Text.Json.JsonSerializer.Serialize(Model.ViewModel.AutoresDicionario))' />
            </abp-column>
        </abp-row>
    </abp-modal-body>
    <abp-modal-footer buttons="@(AbpModalButtons.Close)"></abp-modal-footer>
</abp-modal>
```

## JavaScript

### Configuração do DataTables (index.js)

Configuração da grid DataTables com suporte a paginação server-side, ordenação, filtros e ações CRUD.

```javascript
/**
 * Script principal da página de listagem de livros.
 * Responsável por configurar o DataTable, modais e eventos.
 * 
 * Namespace: TRF3.SISPREC.Web.Pages.Livros
 * Diretório: .\src\TRF3.SISPREC.Web\Pages\Livros
 */
$(function () {
    /**
     * Configura evento de filtro automático ao digitar.
     * Exclui campos com máscara de dinheiro para evitar
     * recargas durante a digitação dos valores.
     */
    $("#LivroFilter :input").not(".input-dinheiro-mascara").on('input', function () {
        dataTable.ajax.reload();
    });

    /**
     * Função auxiliar que coleta os valores do formulário de filtro.
     * Converte os nomes dos campos para camelCase e remove prefixos.
     * Retorna apenas campos preenchidos para otimizar a consulta.
     */
    const getFilter = function () {
        const input = {};
        $("#LivroFilter")
            .serializeArray()
            .forEach(function (data) {
                if (data.value != '') {
                    input[abp.utils.toCamelCase(data.name.replace(/LivroFilter./g, ''))] = data.value;
                }
            })
        return input;
    };

    /**
     * Obtém referência ao serviço de aplicação de livros.
     * Este serviço é gerado automaticamente pelo ABP Framework
     * a partir da interface ILivroAppService.
     */
    const service = tRF3.sISPREC.livros.livro;

    /**
     * Configura os gerenciadores de modais usando ABP ModalManager.
     * Cada modal tem sua própria view e script associados.
     */
    const detalheModal = new abp.ModalManager({
        viewUrl: abp.appPath + 'Livros/DetalheModal',
        scriptUrl: abp.appPath + 'Pages/Livros/DetalheModal.js'
    });
    const createModal = new abp.ModalManager({
        viewUrl: abp.appPath + 'Livros/CreateModal',
        scriptUrl: abp.appPath + 'Pages/Livros/CreateModal.js'
    });
    const editModal = new abp.ModalManager({
        viewUrl: abp.appPath + 'Livros/EditModal',
        scriptUrl: abp.appPath + 'Pages/Livros/EditModal.js'
    });

    /**
     * Inicializa o DataTable com configurações do ABP Framework.
     * Características:
     * - Paginação e ordenação server-side
     * - Filtros customizados via formulário
     * - Colunas com formatação específica
     * - Botões de ação para cada registro
     */
    const dataTable = $('#LivroTable').DataTable(abp.libs.datatables.normalizeConfiguration({
        processing: true,
        serverSide: true,
        paging: true,
        searching: false, // Desabilita busca padrão, usa filtros customizados
        autoWidth: false,
        scrollCollapse: true,
        order: [[1, "asc"]], // Ordena por ID por padrão
        ajax: abp.libs.datatables.createAjax(service.getList, getFilter),
        columnDefs: [
            {
                // Coluna de ações com botões de detalhe, edição e exclusão
                rowAction: {
                    items:
                        [
                            {
                                text: "Detalhe",
                                action: function (data) {
                                    detalheModal.open({ id: data.record.livroId });
                                }
                            }
                            ,
                            {
                                text: "Alterar",
                                action: function (data) {
                                    editModal.open({ id: data.record.livroId });
                                }
                            },
                            {
                                text: "Excluir",
                                confirmMessage: function (data) {
                                    return "Tem certeza de que deseja excluir?"
                                },
                                action: function (data) {
                                    abp.ui.block({ elm: 'body', busy: true });
                                    service.delete(data.record.livroId)
                                        .then(function () {
                                            abp.notify.info('Registro excluído com sucesso!');
                                            dataTable.ajax.reload();
                                        }).always(function () { abp.ui.unblock(); });
                                }
                            }]
                },
                width: "0.1%",
                sorting: false
            },
            {
                title: "Id",
                data: "livroId",
                width: "4%"
            },
            {
                title: "Título",
                data: "titulo"
            },
            {
                title: "Categoria",
                data: "categoria"
            },
            {
                title: "Data de Publicação",
                data: "dataPublicacao",
                render: DataTable.render.datetime('DD/MM/YYYY')
            },
            {
                title: "Preço",
                data: "preco",
                render: data => formatarExibicaoDinheiro(data)
            },
            {
                title: "Quantidade",
                data: "quantidade"
            },
            {
                title: "Disponível",
                data: "disponivel",
                render: data => formatarExibicaoBool(data)
            },
        ]
    }));

    /**
     * Configura eventos para recarregar a tabela após
     * operações de criação e edição nas modais.
     */
    createModal.onResult(function () {
        dataTable.ajax.reload();
    });

    editModal.onResult(function () {
        dataTable.ajax.reload();
    });

    /**
     * Configura evento do botão "Novo" para abrir a modal de criação.
     */
    $('#NewLivroButton').click(function (e) {
        e.preventDefault();
        createModal.open();
    });
});

```

### CreateModal.js


```javascript
/**
 * Script para o modal de criação de livros.
 * Configura o componente Select2 para seleção de autores e
 * adiciona comportamento de recarga após fechar o modal.
 * 
 * Namespace: TRF3.SISPREC.Web.Pages.Livros
 * Diretório: .\src\TRF3.SISPREC.Web\Pages\Livros
 */
$(function () {
    // Configura o Select2 para seleção de autores com busca dinâmica via AJAX
    // O Select2 é inicializado no elemento que receberá os IDs dos autores selecionados
    $('#ViewModel_AutoresIds').select2({
        // Define o container pai como o modal para corrigir problemas de z-index
        dropdownParent: $('#CreateModalLivro'),
        // Usa o tema Bootstrap 5 para manter consistência visual
        theme: 'bootstrap-5',
        // Requer pelo menos 3 caracteres para iniciar a busca
        minimumInputLength: 3,
        // Permite selecionar múltiplos autores
        multiple: true,
        // Texto placeholder quando nenhum autor está selecionado
        placeholder: 'Selecione um ou mais autores...',
        // Permite limpar todas as seleções
        allowClear: true,
        // Configuração da busca AJAX
        ajax: {
            // Endpoint da API que retorna autores filtrados por nome
            url: abp.appPath + 'api/app/autor/autor-por-nome',
            dataType: 'json',
            // Função que prepara os parâmetros da busca
            data: function (params) {
                let query = {
                    nomeAutor: params.term
                };
                return query;
            },
            // Função que processa o resultado da busca
            processResults: function (data) {
                // Mapeia os dados retornados para o formato esperado pelo Select2
                let result = $.map(data, function (obj) {
                    obj.id = obj.autorId;      // ID usado internamente pelo Select2
                    obj.text = obj.nomeCompleto; // Texto exibido no dropdown
                    return obj;
                });

                return {
                    results: result
                };
            }
        }
    });

    // Adiciona evento para recarregar a página quando o modal for fechado
    // Isso garante que a lista de livros seja atualizada após uma criação
    $('#EditModalLivro').on('hidden.bs.modal', function () {
        window.location.reload();
    });
});

```

### EditModal.js


```javascript
/**
 * Script para o modal de edição de livros.
 * Responsável por inicializar o Select2 com os autores pré-selecionados,
 * configurar a busca dinâmica de autores e o comportamento de recarga.
 * 
 * Namespace: TRF3.SISPREC.Web.Pages.Livros
 * Diretório: .\src\TRF3.SISPREC.Web\Pages\Livros
 */
$(function () {
    /**
     * Obtém os dados dos autores do campo hidden que contém o dicionário serializado.
     * O dicionário é criado no PageModel e contém o mapeamento de ID -> Nome dos autores.
     * Se não houver dados, usa um objeto vazio para evitar erros de parse.
     */
    let autoresDictStr = $('#ViewModel_AutoresDicionario').val();
    let autoresDict = JSON.parse(autoresDictStr || '{}');

    /**
     * Converte o dicionário de autores para o formato esperado pelo Select2:
     * Array de objetos com id, text e selected.
     * - id: ID do autor (chave do dicionário)
     * - text: Nome do autor (valor do dicionário)
     * - selected: true para todos os itens pois são os autores atuais do livro
     */
    let options = Object.keys(autoresDict).map(id => ({
        id: id,
        text: autoresDict[id],
        selected: true
    }));

    /**
     * Inicializa o Select2 com suporte a busca dinâmica via AJAX.
     * Configurações:
     * - dropdownParent: Define o modal como container pai (corrige z-index)
     * - theme: Usa o tema Bootstrap 5 para consistência visual
     * - minimumInputLength: Requer 3 caracteres para iniciar busca
     * - multiple: Permite múltiplos autores
     * - placeholder: Texto quando nenhum autor selecionado
     * - allowClear: Permite limpar seleções
     * - data: Carrega os autores pré-selecionados
     */
    $('#ViewModel_AutoresIds').select2({
        dropdownParent: $('#EditModalLivro'),
        theme: 'bootstrap-5',
        minimumInputLength: 3,
        multiple: true,
        placeholder: 'Selecione um ou mais autores...',
        allowClear: true,
        data: options,
        ajax: {
            // Endpoint da API que retorna autores filtrados por nome
            url: abp.appPath + 'api/app/autor/autor-por-nome',
            dataType: 'json',
            // Função que prepara os parâmetros da busca
            data: function (params) {
                let query = {
                    nomeAutor: params.term
                };
                return query;
            },
            // Função que processa o resultado da busca
            processResults: function (data) {
                let result = $.map(data, function (obj) {
                    obj.id = obj.autorId;      // ID usado internamente pelo Select2
                    obj.text = obj.nomeCompleto; // Texto exibido no dropdown
                    return obj;
                });

                return {
                    results: result
                };
            }
        }
    });

    /**
     * Seleciona os autores previamente vinculados ao livro.
     * Usa trigger('change') para notificar o Select2 da mudança.
     */
    if (Object.keys(autoresDict).length > 0) {
        $('#ViewModel_AutoresIds').val(Object.keys(autoresDict)).trigger('change');
    }

    /**
     * Adiciona evento para recarregar a página quando o modal for fechado.
     * Isso garante que a lista de livros seja atualizada após uma edição.
     */
    $('#EditModalLivro').on('hidden.bs.modal', function () {
        window.location.reload();
    });
});


```


### DetalheModal.js


```javascript
/**
 * Script para o modal de detalhes do livro.
 * Responsável por inicializar o Select2 com os autores pré-selecionados
 * e configurar o comportamento de recarga após fechar o modal.
 * 
 * Namespace: TRF3.SISPREC.Web.Pages.Livros
 * Diretório: .\src\TRF3.SISPREC.Web\Pages\Livros
 */
$(function () {
    /**
     * Obtém os dados dos autores do campo hidden que contém o dicionário serializado.
     * O dicionário é criado no PageModel e contém o mapeamento de ID -> Nome dos autores.
     * Se não houver dados, usa um objeto vazio para evitar erros de parse.
     */
    let autoresDictStr = $('#ViewModel_AutoresDicionario').val();
    let autoresDict = JSON.parse(autoresDictStr || '{}');
    console.log('autoresDict', autoresDict);

    /**
     * Converte o dicionário de autores para o formato esperado pelo Select2:
     * Array de objetos com id, text e selected.
     * - id: ID do autor (chave do dicionário)
     * - text: Nome do autor (valor do dicionário)
     * - selected: true para todos os itens pois são os autores do livro
     */
    let options = Object.keys(autoresDict).map(id => ({
        id: id,
        text: autoresDict[id],
        selected: true
    }));
    console.log('options', options);

    /**
     * Inicializa o Select2 em modo somente leitura.
     * Configurações:
     * - dropdownParent: Define o modal como container pai (corrige z-index)
     * - theme: Usa o tema Bootstrap 5 para consistência visual
     * - multiple: Permite múltiplos autores
     * - data: Carrega os autores pré-selecionados
     */
    $('#ViewModel_AutoresIds').select2({
        dropdownParent: $('#DetalheModalLivro'),
        theme: 'bootstrap-5',
        multiple: true,
        data: options,
    });

    /**
     * Adiciona evento para recarregar a página quando o modal for fechado.
     * Isso garante que a lista de livros seja atualizada caso tenha havido
     * alguma alteração em outro modal.
     */
    $('#DetalheModalLivro').on('hidden.bs.modal', function () {
        window.location.reload();
    });
});

```


## Code-Behind das Páginas Razor

### Página de Listagem (Index.cshtml.cs)

A página principal que gerencia a listagem de livros. Responsável por:
- Definir o modelo de filtros para a listagem
- Inicializar o estado da página
- Prover dados para o DataTable via JavaScript

```csharp
using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Livros;

namespace TRF3.SISPREC.Web.Pages.Livros;

/// <summary>
/// Modelo da página Razor para listagem de livros.
/// Herda de SISPRECPageModel para aproveitar funcionalidades comuns do ABP Framework.
/// Namespace: TRF3.SISPREC.Web.Pages.Livros
/// Diretório: .\src\TRF3.SISPREC.Web\Pages\Livros
/// </summary>
/// <remarks>
/// Esta classe é responsável por:
/// - Definir o modelo de filtros para a listagem
/// - Inicializar o estado da página
/// - Prover dados para o DataTable via JavaScript
/// 
/// O carregamento dos dados é feito de forma assíncrona via AJAX,
/// por isso o método OnGetAsync apenas inicializa o estado da página.
/// A listagem real é feita pelo DataTable no arquivo index.js.
/// </remarks>
public class IndexModel : SISPRECPageModel
{
    /// <summary>
    /// Modelo de filtros para a listagem de livros.
    /// Inicializado com valores padrão para evitar null reference.
    /// </summary>
    public LivroFilterInput LivroFilter { get; set; } = new();

    /// <summary>
    /// Método chamado quando a página é carregada via GET.
    /// Apenas inicializa o estado da página, pois os dados
    /// são carregados de forma assíncrona pelo DataTable.
    /// </summary>
    public virtual async Task OnGetAsync()
    {
        await Task.CompletedTask;
    }
}

/// <summary>
/// Modelo para os filtros da listagem de livros.
/// Contém todas as propriedades que podem ser usadas para filtrar a lista.
/// </summary>
/// <remarks>
/// Características dos filtros:
/// - Todos os campos são opcionais (nullable)
/// - Usa Data Annotations para validação e exibição
/// - Campos numéricos têm valores padrão para evitar erros
/// - Datas e booleanos são nullable para permitir "sem filtro"
/// </remarks>
public class LivroFilterInput
{
    /// <summary>
    /// Filtro por ID do livro
    /// </summary>
    [Display(Name = "Id")]
    public int? LivroId { get; set; }

    /// <summary>
    /// Filtro por título do livro (busca parcial case-insensitive)
    /// </summary>
    [Display(Name = "Titulo")]
    public string? Titulo { get; set; }

    /// <summary>
    /// Filtro por categoria do livro
    /// </summary>
    [Display(Name = "Categoria")]
    public TRF3.SISPREC.Enums.ECategoriaLivro? Categoria { get; set; }

    /// <summary>
    /// Filtro por data inicial de publicação (inclusive)
    /// </summary>
    [Display(Name = "Dt. Publi. Início")]
    public DateTime? DataPublicacaoInicio { get; set; }

    /// <summary>
    /// Filtro por data final de publicação (inclusive)
    /// </summary>
    [Display(Name = "Dt. Publi. Final")]
    public DateTime? DataPublicacaoFinal { get; set; }

    /// <summary>
    /// Filtro por preço mínimo (inclusive)
    /// </summary>
    [Display(Name = "Preço Mín.")]
    [DataType(DataType.Currency)]
    public decimal PrecoMin { get; set; } = 0;

    /// <summary>
    /// Filtro por preço máximo (inclusive)
    /// </summary>
    [Display(Name = "Preço Máx.")]
    [DataType(DataType.Currency)]
    public decimal PrecoMax { get; set; } = 0;

    /// <summary>
    /// Filtro por descrição do livro (busca parcial case-insensitive)
    /// </summary>
    [Display(Name = "Descrição")]
    public string? Descricao { get; set; }

    /// <summary>
    /// Filtro por quantidade em estoque
    /// </summary>
    [Display(Name = "Quantidade")]
    public int? Quantidade { get; set; }

    /// <summary>
    /// Filtro por status de disponibilidade
    /// </summary>
    [Display(Name = "Disponível")]
    public bool? Disponivel { get; set; }
}

```

### Modal de Criação (CreateModal.cshtml.cs)

Modal para criar um novo livro. Responsável por:
- Binding do formulário com o ViewModel
- Validação do modelo
- Mapeamento entre ViewModel e DTO
- Comunicação com o LivroAppService

```csharp
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Livros;
using TRF3.SISPREC.Livros.Dtos;
using TRF3.SISPREC.Web.Pages.Livros.ViewModels;

namespace TRF3.SISPREC.Web.Pages.Livros;

/// <summary>
/// Modelo da página Razor para o modal de criação de livros.
/// Herda de SISPRECPageModel para aproveitar funcionalidades comuns do ABP Framework.
/// Namespace: TRF3.SISPREC.Web.Pages.Livros
/// Diretório: .\src\TRF3.SISPREC.Web\Pages\Livros
/// </summary>
/// <remarks>
/// Esta classe é responsável por:
/// - Binding do formulário com o ViewModel
/// - Validação do modelo
/// - Mapeamento entre ViewModel e DTO
/// - Comunicação com o LivroAppService
/// 
/// O fluxo de criação é:
/// 1. Modal é aberta via AJAX
/// 2. Usuário preenche o formulário
/// 3. OnPostAsync é chamado no submit
/// 4. Dados são validados e mapeados
/// 5. LivroAppService.CreateAsync é invocado
/// 6. Modal é fechada após sucesso
/// </remarks>
public class CreateModalModel : SISPRECPageModel
{
    /// <summary>
    /// ViewModel para binding do formulário.
    /// O atributo [BindProperty] indica que este modelo receberá os dados do POST.
    /// </summary>
    [BindProperty]
    public CreateEditLivroViewModel ViewModel { get; set; } = new();

    private readonly ILivroAppService _service;

    /// <summary>
    /// Lista de IDs dos autores selecionados.
    /// Esta propriedade é populada pelo select2 no frontend.
    /// </summary>
    public List<int> AutoresIds { get; set; }

    /// <summary>
    /// Construtor que recebe o serviço de aplicação via injeção de dependência.
    /// </summary>
    /// <param name="service">Serviço de aplicação para operações com livros</param>
    public CreateModalModel(ILivroAppService service)
    {
        _service = service;
    }

    /// <summary>
    /// Método POST chamado quando o formulário é submetido.
    /// Valida o modelo, mapeia para DTO e chama o serviço de aplicação.
    /// </summary>
    /// <returns>NoContent em caso de sucesso, permitindo que o JavaScript feche o modal</returns>
    /// <remarks>
    /// Excluído da cobertura de testes pois não há infraestrutura
    /// para testar métodos POST que gerem relatório no VS2022 para sonarqube
    /// </remarks>
    [ExcludeFromCodeCoverage]
    public virtual async Task<IActionResult> OnPostAsync()
    {
        ValidateModel();
        var dto = ObjectMapper.Map<CreateEditLivroViewModel, CreateUpdateLivroDto>(ViewModel);
        await _service.CreateAsync(dto);
        return NoContent();
    }
}
```

### Modal de Edição (EditModal.cshtml.cs)

Modal para editar um livro existente. Responsável por:
- Receber o ID do livro via query string
- Carregar os dados do livro para edição
- Binding do formulário com o ViewModel
- Validação do modelo
- Mapeamento entre ViewModel e DTO
- Comunicação com o LivroAppService

```csharp
using Microsoft.AspNetCore.Mvc;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Livros;
using TRF3.SISPREC.Livros.Dtos;
using TRF3.SISPREC.Web.Pages.Livros.ViewModels;

namespace TRF3.SISPREC.Web.Pages.Livros;

/// <summary>
/// Modelo da página Razor para o modal de edição de livros.
/// Herda de SISPRECPageModel para aproveitar funcionalidades comuns do ABP Framework.
/// Namespace: TRF3.SISPREC.Web.Pages.Livros
/// Diretório: .\src\TRF3.SISPREC.Web\Pages\Livros
/// </summary>
/// <remarks>
/// Esta classe é responsável por:
/// - Receber o ID do livro via query string
/// - Carregar os dados do livro para edição
/// - Binding do formulário com o ViewModel
/// - Validação do modelo
/// - Mapeamento entre ViewModel e DTO
/// - Comunicação com o LivroAppService
/// 
/// O fluxo de edição é:
/// 1. Modal é aberta via AJAX com ID na URL
/// 2. OnGetAsync carrega os dados do livro
/// 3. Usuário edita o formulário
/// 4. OnPostAsync é chamado no submit
/// 5. Dados são validados e mapeados
/// 6. LivroAppService.UpdateAsync é invocado
/// 7. Modal é fechada após sucesso
/// </remarks>
public class EditModalModel : SISPRECPageModel
{
    /// <summary>
    /// ID do livro recebido via query string.
    /// HiddenInput indica que será um campo oculto no HTML.
    /// SupportsGet permite receber o valor via query string.
    /// </summary>
    [HiddenInput]
    [BindProperty(SupportsGet = true)]
    public int Id { get; set; }

    /// <summary>
    /// ViewModel para binding do formulário.
    /// Compartilha o mesmo modelo do formulário de criação.
    /// </summary>
    [BindProperty]
    public CreateEditLivroViewModel ViewModel { get; set; } = new();

    private readonly ILivroAppService _service;

    /// <summary>
    /// Construtor que recebe o serviço de aplicação via injeção de dependência.
    /// </summary>
    /// <param name="service">Serviço de aplicação para operações com livros</param>
    public EditModalModel(ILivroAppService service)
    {
        _service = service;
    }

    /// <summary>
    /// Método chamado via GET quando a modal é aberta.
    /// Carrega os dados do livro e prepara o ViewModel para edição.
    /// </summary>
    public virtual async Task OnGetAsync()
    {
        var dto = await _service.GetAsync(Id);
        ViewModel = ObjectMapper.Map<LivroDto, CreateEditLivroViewModel>(dto);

        // Converte a lista de Autores para Dictionary para uso no Select2
        if (dto.Autores != null)
        {
            Dictionary<int, string> dictionary = dto.Autores.ToDictionary(a => a.AutorId, a => a.NomeCompleto);
            ViewModel.AutoresDicionario = dictionary;
        }
    }

    /// <summary>
    /// Método POST chamado quando o formulário é submetido.
    /// Valida o modelo, mapeia para DTO e chama o serviço de aplicação.
    /// </summary>
    /// <returns>NoContent em caso de sucesso, permitindo que o JavaScript feche o modal</returns>
    /// <remarks>
    /// Excluído da cobertura de testes pois não há infraestrutura
    /// para testar métodos POST que gerem relatório no VS2022 para sonarqube
    /// </remarks>
    [ExcludeFromCodeCoverage]
    public virtual async Task<IActionResult> OnPostAsync()
    {
        ValidateModel();
        var dto = ObjectMapper.Map<CreateEditLivroViewModel, CreateUpdateLivroDto>(ViewModel);
        await _service.UpdateAsync(Id, dto);
        return NoContent();
    }
}
```

### Modal de Detalhes (DetalheModal.cshtml.cs)

Modal para visualizar os detalhes de um livro. Responsável por:
- Receber o ID do livro via query string
- Carregar os dados do livro através do LivroAppService
- Mapear o DTO para o ViewModel de detalhes
- Preparar os dados dos autores para o Select2

```csharp
using Microsoft.AspNetCore.Mvc;
using TRF3.SISPREC.Livros;
using TRF3.SISPREC.Livros.Dtos;
using TRF3.SISPREC.Web.Pages.Livros.ViewModels;

namespace TRF3.SISPREC.Web.Pages.Livros;

/// <summary>
/// Modelo da página Razor para o modal de detalhes do livro.
/// Herda de SISPRECPageModel para aproveitar funcionalidades comuns do ABP Framework.
/// Namespace: TRF3.SISPREC.Web.Pages.Livros
/// Diretório: .\src\TRF3.SISPREC.Web\Pages\Livros
/// </summary>
/// <remarks>
/// Esta classe é responsável por:
/// - Receber o ID do livro via query string
/// - Carregar os dados do livro através do LivroAppService
/// - Mapear o DTO para o ViewModel de detalhes
/// - Preparar os dados dos autores para o Select2
/// 
/// O fluxo de exibição é:
/// 1. Modal é aberta via AJAX com ID na URL
/// 2. OnGetAsync carrega os dados do livro
/// 3. Dados são mapeados para o ViewModel
/// 4. View exibe os dados em modo somente leitura
/// </remarks>
public class DetalheModalModel : SISPRECPageModel
{
    /// <summary>
    /// ID do livro recebido via query string.
    /// HiddenInput indica que será um campo oculto no HTML.
    /// SupportsGet permite receber o valor via query string.
    /// </summary>
    [HiddenInput]
    [BindProperty(SupportsGet = true)]
    public int Id { get; set; }

    /// <summary>
    /// ViewModel contendo os dados do livro para exibição.
    /// Inclui propriedades adicionais como AutoresDicionario para o Select2.
    /// </summary>
    [BindProperty]
    public DetalheLivroViewModel ViewModel { get; set; } = new();

    private readonly ILivroAppService _service;

    /// <summary>
    /// Construtor que recebe o serviço de aplicação via injeção de dependência.
    /// </summary>
    /// <param name="service">Serviço de aplicação para operações com livros</param>
    public DetalheModalModel(ILivroAppService service)
    {
        _service = service;
    }

    /// <summary>
    /// Método chamado via GET quando a modal é aberta.
    /// Carrega os dados do livro e prepara o ViewModel para exibição.
    /// </summary>
    public virtual async Task OnGetAsync()
    {
        var dto = await _service.GetAsync(Id);
        ViewModel = ObjectMapper.Map<LivroDto, DetalheLivroViewModel>(dto);
        
        // Converte a lista de Autores para Dictionary para uso no Select2
        if (dto.Autores != null)
        {
            Dictionary<int, string> dictionary = dto.Autores.ToDictionary(a => a.AutorId, a => a.NomeCompleto);
            ViewModel.AutoresDicionario = dictionary;
        }
    }
}
```

## ViewModels

Os ViewModels são classes que representam os dados necessários para as views. No caso dos livros, temos dois ViewModels principais:

### CreateEditLivroViewModel

Este ViewModel é compartilhado entre os modais de criação e edição de livros. Ele contém todas as propriedades necessárias para o formulário, com suas respectivas validações e atributos de exibição.

Características principais:
- Usa Data Annotations para validação client e server-side
- Segue as mesmas regras de negócio da entidade Livro
- Inclui atributos de UI para melhor experiência do usuário
- Suporta seleção múltipla de autores via Select2

```csharp
using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Livros;

namespace TRF3.SISPREC.Web.Pages.Livros.ViewModels;

/// <summary>
/// ViewModel compartilhado entre os modais de criação e edição de livros.
/// Contém todas as propriedades necessárias para o formulário, com suas
/// respectivas validações e atributos de exibição.
/// Namespace: TRF3.SISPREC.Web.Pages.Livros.ViewModels
/// Diretório: .\src\TRF3.SISPREC.Web\Pages\Livros\ViewModels
/// </summary>
/// <remarks>
/// Características:
/// - Usa Data Annotations para validação client e server-side
/// - Segue as mesmas regras de negócio da entidade Livro
/// - Inclui atributos de UI para melhor experiência do usuário
/// - Suporta seleção múltipla de autores via Select2
/// 
/// O fluxo de dados é:
/// 1. Usuário preenche o formulário
/// 2. ViewModel é validado no cliente e servidor
/// 3. AutoMapper converte para CreateUpdateLivroDto
/// 4. AppService processa a operação
/// </remarks>
public class CreateEditLivroViewModel
{
    /// <summary>
    /// Título do livro.
    /// Obrigatório e com tamanho máximo definido em LivroConsts.
    /// </summary>
    [Required(ErrorMessage = "O título é obrigatório")]
    [StringLength(LivroConsts.TITULO_TAMANHO_MAX, ErrorMessage = "O título deve ter no máximo {1} caracteres")]
    [Display(Name = "Título")]
    public string Titulo { get; set; } = string.Empty;

    /// <summary>
    /// Categoria do livro.
    /// Renderizada como dropdown usando UIHint.
    /// </summary>
    [Required(ErrorMessage = "A categoria do livro é obrigatória")]
    [Display(Name = "Categoria")]
    [UIHint("EnumSelect")]
    public ECategoriaLivro Categoria { get; set; }

    /// <summary>
    /// Data de publicação do livro.
    /// Formatada no padrão brasileiro.
    /// </summary>
    [Required(ErrorMessage = "A data de publicação é obrigatória")]
    [DataType(DataType.Date)]
    [Display(Name = "Data de Publicação")]
    [DisplayFormat(DataFormatString = "{0:dd/MM/yyyy}", ApplyFormatInEditMode = true)]
    public DateTime DataPublicacao { get; set; }

    /// <summary>
    /// Preço do livro.
    /// Usa DataType.Currency para formatação monetária.
    /// </summary>
    [Required(ErrorMessage = "O preço é obrigatório")]
    [Range(1, 9999.99, ErrorMessage = "O preço deve estar entre {1} e {2}")]
    [Display(Name = "Preço")]
    [DataType(DataType.Currency)]
    public decimal Preco { get; set; }

    /// <summary>
    /// Descrição opcional do livro.
    /// Renderizada como textarea usando DataType.MultilineText.
    /// </summary>
    [StringLength(LivroConsts.DESCRICAO_TAMANHO_MAX, ErrorMessage = "A descrição deve ter no máximo {1} caracteres")]
    [Display(Name = "Descrição")]
    [DataType(DataType.MultilineText)]
    public string? Descricao { get; set; }

    /// <summary>
    /// Quantidade em estoque.
    /// Deve ser maior ou igual a zero.
    /// </summary>
    [Required(ErrorMessage = "A quantidade é obrigatória")]
    [Range(0, int.MaxValue, ErrorMessage = "A quantidade deve ser maior ou igual a zero")]
    [Display(Name = "Quantidade")]
    public int Quantidade { get; set; }

    /// <summary>
    /// Status de disponibilidade do livro.
    /// Renderizado como checkbox.
    /// </summary>
    [Required(ErrorMessage = "O status de disponibilidade é obrigatório")]
    [Display(Name = "Disponível")]
    public bool Disponivel { get; set; }

    /// <summary>
    /// IDs dos autores selecionados no Select2.
    /// Usado para vincular autores ao livro.
    /// </summary>
    [Display(Name = "Autores")]
    public List<int> AutoresIds { get; set; } = new();

    /// <summary>
    /// Dicionário de autores para inicialização do Select2.
    /// Chave: ID do autor, Valor: Nome do autor.
    /// </summary>
    public Dictionary<int, string> AutoresDicionario { get; set; } = new();
}

```

### DetalheLivroViewModel

Este ViewModel é usado para exibição dos detalhes de um livro na modal de detalhes. Similar ao CreateEditLivroViewModel, mas sem validações pois é somente leitura.

Características principais:
- Usa Data Annotations apenas para exibição (Display)
- Formatação específica para data e valores monetários
- Suporta exibição de autores via Select2 desabilitado

```csharp
using System.ComponentModel.DataAnnotations;

namespace TRF3.SISPREC.Web.Pages.Livros.ViewModels;

/// <summary>
/// ViewModel para exibição dos detalhes de um livro na modal de detalhes.
/// Similar ao CreateEditLivroViewModel, mas sem validações pois é somente leitura.
/// Namespace: TRF3.SISPREC.Web.Pages.Livros.ViewModels
/// Diretório: .\src\TRF3.SISPREC.Web\Pages\Livros\ViewModels
/// </summary>
/// <remarks>
/// Características:
/// - Usa Data Annotations apenas para exibição (Display)
/// - Formatação específica para data e valores monetários
/// - Suporta exibição de autores via Select2 desabilitado
/// 
/// O fluxo de dados é:
/// 1. LivroAppService retorna LivroDto
/// 2. AutoMapper converte para DetalheLivroViewModel
/// 3. ViewModel é exibido na modal em modo somente leitura
/// </remarks>
public class DetalheLivroViewModel
{
    /// <summary>
    /// Identificador único do livro
    /// </summary>
    [Display(Name = "Id")]
    public int LivroId { get; set; }

    /// <summary>
    /// Título do livro
    /// </summary>
    [Display(Name = "Título")]
    public string Titulo { get; set; }

    /// <summary>
    /// Categoria do livro (texto do enum)
    /// </summary>
    [Display(Name = "Categoria")]
    public string Categoria { get; set; }

    /// <summary>
    /// Data de publicação formatada no padrão brasileiro
    /// </summary>
    [DataType(DataType.Date)]
    [Display(Name = "Data de Publicação")]
    [DisplayFormat(DataFormatString = "{0:dd/MM/yyyy}")]
    public DateTime DataPublicacao { get; set; }

    /// <summary>
    /// Preço do livro
    /// </summary>
    [Display(Name = "Preço")]
    public decimal Preco { get; set; }

    /// <summary>
    /// Descrição opcional do livro
    /// </summary>
    [Display(Name = "Descrição")]
    public string? Descricao { get; set; }

    /// <summary>
    /// Quantidade em estoque
    /// </summary>
    [Display(Name = "Quantidade")]
    public int Quantidade { get; set; }

    /// <summary>
    /// Status de disponibilidade
    /// </summary>
    [Display(Name = "Disponível")]
    public bool Disponivel { get; set; }

    /// <summary>
    /// IDs dos autores vinculados ao livro
    /// </summary>
    [Display(Name = "Autores")]
    public List<int> AutoresIds { get; set; } = new();

    /// <summary>
    /// Dicionário de autores para inicialização do Select2 em modo somente leitura.
    /// Chave: ID do autor, Valor: Nome do autor
    /// </summary>
    public Dictionary<int, string> AutoresDicionario { get; set; } = new();
}

```

## Mapeamento - AutoMapper

Adicione os mapeamentos entre DTOs e ViewModels no construtor do perfil de mapeamento do Projeto Web.
Use ReverseMap() para mapear bidirecionalmente, evitando criar uma nova linha com o mapeamento inverso.

```csharp
public class SISPRECWebAutoMapperProfile : Profile
{
    public SISPRECWebAutoMapperProfile()
    {
        //Mapeamento de Livro
        CreateMap<LivroDto, CreateEditLivroViewModel>();
        CreateMap<CreateUpdateLivroDto, CreateEditLivroViewModel>().ReverseMap();
        CreateMap<LivroDto, DetalheLivroViewModel>();
    }
}
```

**[Próximo: Projeto Web - Autores](/Tutorial-de-Início/Projeto-Web/Autores.md)**
