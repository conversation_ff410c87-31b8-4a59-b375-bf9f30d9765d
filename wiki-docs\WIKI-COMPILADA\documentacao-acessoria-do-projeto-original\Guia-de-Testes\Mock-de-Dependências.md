# Guia de Mocks para Testes

Este guia apresenta as práticas recomendadas e exemplos para criação de mocks nos testes do SISPREC, abordando tanto testes unitários quanto testes de integração.

## Mocks em Testes Unitários (DomainServices)

Em testes unitários, testamos uma unidade isolada do código, tipicamente uma classe de domínio. Para isso, é necessário mockar todas as dependências da classe.

### Criando Mocks Básicos

```csharp
// Criar mock de uma interface
private readonly IControleProcessamentoRepository _controleProcessamentoRepository = Substitute.For<IControleProcessamentoRepository>();

// Criar o serviço testado passando as dependências mockadas
_importarPrecatorioService = new ImportarPrecatorioAutomatizado(
    _controleProcessamentoRepository,
    _controleProcessamentoProcessoRepository,
    _serviceProvider,
    _getLoggerService,
    _unitOfWorkManager,
    _hub<PERSON>ontext,
    _controleProcessamentoManager
);
```

### Configurando Retornos de Métodos

```csharp
// Retorno simples
_controleProcessamentoRepository
    .ListarPendentesImportacao()
    .Returns(controles.AsQueryable().BuildMock());

// Retornos múltiplos para chamadas subsequentes
_controleProcessamentoRepository
    .ObterPrecatoriosOrigem(controleProcessamento.Fase.TipoPrecatorio, controleProcessamento.Id)
    .Returns(
        listaPreenchida,  // primeira chamada
        listaPreenchida,  // segunda chamada
        listaVazia        // terceira chamada
    );

// Lançar exceção
_importarProcessoService
    .Importar(Arg.Any<PrecatorioOrigem>())
    .Throws(new Exception("Erro de importação"));
```

### Mockando FirstOrDefaultAsync

```csharp
// Para cenários genéricos
_pessoaRepositoryMock
    .FirstOrDefaultAsync(Arg.Any<Expression<Func<Pessoa, bool>>>())
    .Returns(Task.FromResult(pessoaObj1));

// Para cenários específicos
_tipoIndicadorEconomicoRepository
    .FirstOrDefaultAsync(Arg.Is<Expression<Func<IndicadorEconomicoTipo, bool>>>(
        expr => expr.Compile().Invoke(new IndicadorEconomicoTipo { Codigo = "xxx" })
    ))
    .Returns((IndicadorEconomicoTipo)null);
```

### Mockando IQueryable com MockQueryable.NSubstitute

```csharp
// Criar mock de lista queryable
var lista = new List<ControleProcessamento> { controleProcessamento }
    .AsQueryable()
    .BuildMockDbSet();

_controleProcessamentoRepository
    .GetQueryableAsync()
    .Returns(lista);
```

### Usando When() para Comportamentos Condicionais

```csharp
// Configurar comportamento baseado em condição
repository.When(x => x.GetAsync(Arg.Is<int>(id => id > 0)))
    .Do(x => Task.FromResult(new Entidade { Id = 1 }));

repository.When(x => x.GetAsync(Arg.Is<int>(id => id <= 0)))
    .Do(x => throw new ArgumentException("Id inválido"));
```

### Verificando Chamadas de Métodos

```csharp
// Verificar se método foi chamado
await _controleProcessamentoManager
    .Received(1)
    .IniciarProcessamento(controleProcessamento.Id);

// Verificar se método NÃO foi chamado
await _controleProcessamentoManager
    .DidNotReceive()
    .IniciarProcessamento(Arg.Any<int>());

// Verificar chamada com argumentos específicos
_logger.Received().LogWarning(
    "A execução ImportarPrecatorioAutomatizado foi interrompida."
);
```

## Mocks em Testes de Integração (AppServices)

Em testes de integração, testamos a integração entre diferentes componentes usando o container de injeção de dependência.

### Configurando Mocks no Container

```csharp
protected override void AfterAddApplication(IServiceCollection services)
{
    // Remover serviços existentes
    services.RemoveAll(typeof(IPessoaManager));
    services.RemoveAll(typeof(IPessoaRepository));

    // Adicionar mocks
    services.AddTransient(typeof(IPessoaManager), _ => _pessoaManagerMock);
    services.AddTransient(typeof(IPessoaRepository), _ => _pessoaRepositoryMock);
}
```

### Obtendo Serviços Mockados

```csharp
// Obter serviço do container
var appService = GetRequiredService<IPessoaAppService>();
_pessoaRepositoryMock = GetRequiredService<IPessoaRepository>();
```

### Mockando IReqPagUnitOfWork

```csharp
// Mock do UnitOfWork
var reqPagUnitOfWorkMock = Substitute.For<IReqPagUnitOfWork>();
var advogadoJudicialRepositoryMock = Substitute.For<IAdvogadoJudicialRepository>();

// Configurar retorno do repositório
advogadoJudicialRepositoryMock
    .GetQueryableAsync()
    .Returns(Task.FromResult(listaAdvogados.AsQueryable()));

reqPagUnitOfWorkMock
    .AdvogadoJudicialRepository
    .Returns(advogadoJudicialRepositoryMock);

// Simular erro no SaveChanges
reqPagUnitOfWorkMock
    .SaveChangesAsync()
    .Returns(Task.FromException<int>(new Exception("Erro ao salvar")));
```

### Usando Bogus para Dados de Teste

```csharp
// Configurar gerador de dados
var faker = new Faker<Pessoa>("pt_BR")
    .RuleFor(p => p.PessoaId, f => f.Random.Long(1, 1000))
    .RuleFor(p => p.Nome, f => f.Random.Hash())  // Preferir Hash() ao invés de String()
    .RuleFor(p => p.NomeSocial, f => f.Random.Hash())
    .RuleFor(p => p.TipoPessoa, f => ETipoPessoa.F)
    .RuleFor(p => p.NumeroCnpjCpf, f => f.Person.Cpf())
    .RuleFor(p => p.IsDelete, f => true);  // Usar valor padrão true

// Gerar dados
var pessoa = faker.Generate();
var listaPessoas = faker.Generate(3);
```

## Recomendações Importantes

1. Para testes unitários:
   - Instancie o objeto testado com `new`
   - Mocke todas as dependências
   - Use `Substitute.For<T>()` para criar mocks

2. Para testes de integração:
   - Use `GetRequiredService<T>()` para obter serviços
   - Configure mocks no método `AfterAddApplication`
   - Permita que o container gerencie o ciclo de vida dos objetos

3. Ao usar Bogus:
   - Prefira `Random.Hash()` para strings
   - Use valor padrão `true` para `IsDelete`
   - Mantenha os dados de teste relevantes para o cenário

4. Ao mockar FirstOrDefaultAsync:
   - Use `Arg.Any<>()` para casos genéricos
   - Use `Arg.Is<>()` com `Compile().Invoke()` para casos específicos

5. Ao usar MockQueryable:
   - Use `BuildMockDbSet()` para simular DbSet
   - Use `BuildMock()` para IQueryable simples

6. Ao verificar chamadas:
   - Use `Received()` para confirmar chamadas
   - Use `DidNotReceive()` para confirmar que método não foi chamado
   - Verifique argumentos específicos quando relevante
