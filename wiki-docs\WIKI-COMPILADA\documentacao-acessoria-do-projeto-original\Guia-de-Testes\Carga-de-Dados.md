[[_TOC_]]
# Carga de Dados

- Foi criada uma carga de dados em SQL puro para o SQLite para disponibilizar registros básicos para os testes que usam banco. Demais registros relacionados deverão ser criados no construtor do método de teste. O arquivo dessa carga é `test\TRF3.SISPREC.EntityFrameworkCore.Tests\EntityFrameworkCore\Sql\CARGA_BASICA_SQLITE.sql`.

##**Usando registros inseridos**:
- A classe **SISPRECTestConsts** foi incrementada com constantes dos IDs dos registros inseridos.
Para utilizá-los, use os repositórios das respectivas classes para obter os registros.
- **Observações:** 
  - **Sempre** use nos testes os IDs por meio das constantes
  - **Nem todos registros inseridos possuem o ID nas constantes**, caso seja necessário, podem ser adicionados conforme necessário.
  - Como todos os testes foram implementados usando inserção do registro no construtor, nem todos foram refatorados para usar os registros inseridos. **Caso algum teste falhe por duplicação de chave (chave gerada aleatoreamente pelo bogus), refatore-o para usar o registro já inserido.**
  - Caso a estrutura do banco tenha sido alterada, e seja necessário alterar o inserts, faça os ajustes no arquivo `test\TRF3.SISPREC.EntityFrameworkCore.Tests\EntityFrameworkCore\Sql\CARGA_BASICA_SQLITE.sql`

##**Tabelas com registros preenchidos**
- **REQ_REQUISICAO_PARTE**  
  - PKs inseridas:  
    - SEQ_REQUIS_PARTE: 1, 2, 3

- **REQ_REQUISICAO_PARTE_REQUERENTE**  
  - PKs inseridas:  
    - SEQ_REQUIS_PARTE: 2

- **REQ_REQUISICAO_PARTE_REU**  
  - PKs inseridas:  
    - SEQ_REQUIS_PARTE: 1

- **REQ_REQUISICAO_PROCESSO_ORIGEM**  
  - PKs inseridas (chave composta):  
    - NUM_PROTOC_REQUIS: '20240086838'  
    - NUM_PROCES_ORIGIN: '00006175120228260660'

- **REQ_SITUACAO_REQUISICAO_PROTOCOLO**  
  - PKs inseridas:  
    - SEQ_SITUAC_REQUIS: 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20

- **REQ_REQUISICAO_PROTOCOLO**  
  - PKs inseridas:  
    - NUM_PROTOC_REQUIS: '20240086838'

- **REQ_REQUISICAO_PROPOSTA**  
  - PKs inseridas (chave composta):  
    - SEQ_PROPOS: 3  
    - NUM_PROTOC_REQUIS: '20240086838'
- **CJF_Assunto**  
  - PKs inseridas:  
    - SEQ_ASSUNT: 2028

- **CJF_Benefi_Identi_Tipo**  
  - PKs inseridas:  
    - Seq_Benefi_Identi_Tipo: 1000000, 1000001, 1000002, 1000003, 1000004, 1000005, 1000006, 1000007, 1000008, 1000009, 1000010, 1000011

- **CJF_Movime_Tipo**  
  - PKs inseridas:  
    - Seq_Movime_Tipo: 1000000, 1000001, 1000002, 1000003, 1000004, 1000005, 1000006, 1000007, 1000008

- **CJF_Unidade**  
  - PKs inseridas:  
    - SEQ_UNIDAD: 1050

- **CJF_Unidade_Judici**  
  - PKs inseridas:  
    - SEQ_UNIDAD_JUDICI: 16312

- **CJF_Unidade_Judici_Tipo**  
  - PKs inseridas:  
    - SEQ_UNIDAD_JUDICI_TIPO: 1, 2, 3, 4, 5

- **CJF_Unidade_Judici_Tipo_Nature**  
  - PKs inseridas:  
    - SEQ_UNIDAD_JUDICI_TIPO_NATURE: 1, 2

- **CJF_Valor_Tipo**  
  - PKs inseridas:  
    - SEQ_VALOR_TIPO: 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 26, 27, 28

- **REQ_Advogado_Judicial**  
  - PKs inseridas:  
    - COD_ADVOGA: 18689, 407940

- **REQ_Pessoa**  
  - PKs inseridas:  
    - SEQ_PESSOA: 1, 2

- **REQ_Proposta**  
  - PKs inseridas:  
    - SEQ_PROPOS: 3

- **REQ_Tipo_Procedimento**  
  - PKs inseridas:  
    - COD_TIPO_PROCED: 'PRC', 'RPV'

- **SIN_PESSOA_REQPAG**  
  - PKs inseridas (provável chave primária):  
    - COD_CADAST_PESSOA: 943, 2418026

- **TRF_INDICADOR_ECONOMICO**  
  - PKs inseridas:  
    - SEQ_INDICA_ECONOM: 2705

- **TRF_INDICADOR_ECONOMICO_TIPO**  
  - PKs inseridas:  
    - SEQ_INDICA_ECONOM_TIPO: 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14

- **TRF_MUNICIPIO**  
  - PKs inseridas:  
    - Seq_Munici: 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20, 21, 22, 23, 24, 26, 27, 28, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107

- **TRF_UF**  
  - PKs inseridas (possivelmente Sig_Uf):  
    - Sig_Uf: 'AC', 'AL', 'AM', 'AP', 'BA', 'CE', 'DF', 'ES', 'GO', 'MA', 'MG', 'MS', 'MT', 'PA', 'PB', 'PE', 'PI', 'PR', 'RJ', 'RN', 'RO', 'RR', 'RS', 'SC', 'SE', 'SP', 'TO'