[[_TOC_]]
# RESUMO DO GERADOR DE PDF:

1. GenerateFile: O método começa carregando um arquivo de template HTML, onde a estrutura básica do PDF está definida. Esse template será modificado dinamicamente com base nos dados fornecidos.

2. Gerar HTML dinâmico: O método percorre as entidades de dados fornecidas, insere os valores nas seções apropriadas do HTML (baseado no ID das tags) e, se houver listas ou coleções, clona e adiciona novas seções conforme necessário.

3. Salvar o documento no fluxo de memória: Após gerar o HTML dinâmico, o documento é salvo em um fluxo de memória (MemoryStream), permitindo que ele seja manipulado e usado posteriormente.

4. Fazer parsing do HTML usando Scryber: O fluxo de memória contendo o HTML é analisado pelo Scryber, que é uma biblioteca utilizada para converter o HTML em um documento PDF.

5. Salvar o resultado como PDF: O documento final é salvo como um arquivo PDF temporário no sistema, e o conteúdo desse arquivo é retornado como um array de bytes.

6. Javascript é responsável por decodificar os bytes e gerar novamente o arquivo PDF


## Para geração de um PDF utilizando um template HTML, foram utilizadas a seguintes bibliotecas:


- NuGet\Install-Package Scryber.Core -Version 5.0.7
- NuGet\Install-Package HtmlAgilityPack -Version 1.11.67

**Scryber.Core** está responsável por salvar como PDF as informações geradas pelo **HtmlAgilityPack**, exemplo do codigo do Scryber abaixo:

![image.png](/.attachments/image-913d21ef-a6f3-40cc-b5af-0eee65ac73f1.png)

o HtmlAgilityPack é responsável por gerar dinamicamente seções de codigo HTML, ou seja, se houver uma lista de nomes de advogados a serem exportados, o HTML será gerado dinamicamente contento o nome de cada advogado e em sequência o PDF será gerado com essas informações.


O gerador de PDF pega dinamicamente as propriedades de uma classe, captura os valores dessas propriedades e insere no arquivo HTML, exemplo:

* Na TAG "th" é a tag de cabeçalho, já a tag "td" é a tag onde o gerador de pdf vai localizá-la utilizando o id informado e inserir a informações exatamente onde está o traço amarelo, ou seja, insere entre a abertura e fechamento da TAG "td"

no exemplo a seguir, a tag:  ![image.png](/.attachments/image-a27dc2ad-4517-45e5-9e97-0923e19154b9.png) possui o id "OcorrenciaMotivo.CodigoMotivo", onde identifica que será capturado da classe: "OcorrenciaMotivo" o valor da propriedade ".CodigoMotivo".

o código responsável identificar estas TAGS está localizado no print abaixo:

![image.png](/.attachments/image-fff5ee06-96bc-47a1-8bf2-bf7c2a7d9322.png)

# JAVASCRIPT:

No arquivo util.js, há uma função resposável por converter o BASE64 em PDF e realizar o download do mesmo:

![image.png](/.attachments/image-0578ecb5-d5a4-4c45-8423-2c809dccebd4.png)

