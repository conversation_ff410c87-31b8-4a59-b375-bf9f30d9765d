[[_TOC_]]

# ABP Framework

- Framework adotado para criação do Sistema de Precatórios.
- Framework opensource com arquitetura opinativa baseado nas plataformas .NET Core e ASP.NET Core ([github.com/abpframework/abp](https://github.com/abpframework/abp), [docs.abp.io](http://docs.abp.io/))
- Promove práticas do Domain Driven Design e Clean Architecture.
- Propósitos do ABP Framework:
    1. Fornecer framework projetado para seguir as melhores práticas e convenções no desenvolvimento de software. 
    2. Oferecer estrutura de alto nível e ecossistema que ajudam a implementar o princípio "Não Se Repita" (DRY) e focar no código do negócio.

**Representação resumida das camadas da arquitetura de uma aplicação ABP Framework**

![image.png](/.attachments/image-7203cd99-26b0-4b02-b8b2-dcd9b1d1c75c.png =400x)

**Representação ampliada das camadas da arquitetura de uma aplicação ABP Framework**

![image.png](/.attachments/image-b96846c8-4ef4-4909-a55d-e80abfb8cd32.png =800x)


#Arquitetura TRF3.SISPREC

A arquitetura do SISPREC segue a mesma estrutura da arquitetura do ABP Framework, demonstrada acima, e possui a seguinte estrutura:

![image.png](/.attachments/image-49403607-c970-4fb3-9c90-ed80f7125fe2.png =600x)

## **Projeto .Domain.Shared**
Camada de Domínio Compartilhado: Inclui as definições e constantes compartilhadas entre as camadas de domínio e aplicação, como enums, constantes (strings de erro), entre outros.

Este projeto contém constantes, enums e outros objetos que são na verdade parte da camada de domínio, mas precisam ser usados por todas as camadas/projetos na solução.
Um enum `TipoLivroEnum` e uma classe `LivrosConsts` (que pode ter alguns campos constantes para a entidade `Livro`, como `MaxNameLength`) seriam bons candidatos para este projeto.
Este projeto não tem dependência de outros projetos na solução. Todos os outros projetos dependem deste direta ou indiretamente.

## **Projeto .Domain**
Camada de Domínio: Contém entidades, interfaces e serviços de domínio. Contém código que não pode depender das outras camadas, exceto da camada de Domínio Compartilhado.

Esta é a camada de domínio da solução. Contém principalmente [entidades](https://docs.abp.io/en/abp/latest/Entities), [serviços de domínio](https://docs.abp.io/en/abp/latest/Domain-Services), [objetos de valor](https://docs.abp.io/en/abp/latest/Value-Objects), [interfaces de repositório](https://docs.abp.io/en/abp/latest/Repositories) e outros objetos de domínio.

Uma entidade `Livro`, um serviço de domínio `LivroManager` e uma interface `ILivroRepository` seriam bons candidatos para este projeto.
- Depende do `.Domain.Shared` porque utiliza constantes, enums e outros objetos definidos naquele projeto.

- Cada entidade deve conter uma pasta no plural, que será o namespace base da entidade. Exemplo: entidade Livro, tera uma pasta Livros e namespace TRF3.SISPREC.Livros.
- A pasta da entidade deverá conter:
    - A entidade Livro ([clique aqui](https://docs.abp.io/en/abp/latest/Entities) para saber mais sobre entidades no ABP Framework)
    - Interfaces do repositório; ILivroRepository, que deve herdar do repositório generico, IRepository<Livro>.
    - Interfaces do Manager da entidade; ILivroManager.
    - Pasta Servicos, que deverá conter:
		- Domain Manager da entidade (serviços de domínio). 
			- LivroManager : BaseDomainManager<Livro>, ILivroManager
			- Outros serviços de validação que possam ser consumidos pelo Manager

**Uso de DomainManagers**
O uso de DomainManagers para entidades que em que a aplicação realiza operaçõs de inserção,alteração e exclusão é importante pois serve como ponto único para acionar operações de comando (incluir, alterar e excluir), isso garante que sempre ao usar seus métodos, esses comandos poderão ser validados por regras de negócio específicas para cada entidade antes e/ou depois de realizar os comandos. Exemplo:
- Ao inserir um livro, deve-se garantir que que o id do autor informado esteja cadastrado no banco de dados.
	- O ILivroManager deve ter um método  de validação para isso.
	- O LivroManager implementará esse método.
	- O método de inserção do LivroManager deverá ser sobrescrito para que o método de validação seja executado antes de salvar a entidade via repositório. (Os manager possuem dependência das INTERFACES dos repositórios, e não da implementação - a camada de domínio não deve depender da camada EFCore)
	- Para manter o princípio da responsabilidade única, o ideal é criar uma classe específica para a validação citada, e injetá-la como dependência na classe Manager. Um exemplo pode ser encontrado [aqui](/Tutorial-de-Início)
      - Essa abordagem é uma solução para o problema de usar diretamente o repositório, que deve conter apenas código relacioando ao acesso ao Banco de Dados.
    
**Interface do repositório**
Caso sejam criados novos métodos de consulta ao banco de dados, estes deverão ser declarados na interface do repositório e implementados na classe concreta, que está na camada EntityframeworkCore.

**Outras características**

- O Abp framework soluções prontas como softdelete, por meio da herança da classe FullAuditedEntity, auditoria de mudanças, entre outras funcionalidades, que podem ser conhecidas com mais detalhes no [link](https://docs.abp.io/en/abp/latest/Entities)


## **Projeto .Application.Contracts**

Contratos de Aplicação: este projeto contém principalmente interfaces de [serviço de aplicação](https://docs.abp.io/en/abp/latest/Application-Services) e [Data Transfer Objects - DTOs ](https://docs.abp.io/en/abp/latest/Data-Transfer-Objects) a serem usados na camada de aplicação.

Essa camada existe para separar a interface e a implementação da camada de aplicação. Desta forma, o projeto de interface pode ser compartilhado com os clientes como um pacote de contrato.

Uma interface `ILivroAppService` e uma classe `LivroDto` são bons candidatos para este projeto.

- Depende do `.Domain.Shared` porque pode usar constantes, enums e outros objetos compartilhados deste projeto nas interfaces de serviço de aplicação e DTOs.
- Cada AppService deve conter uma pasta com sua interface, e dentro desta pasta uma pasta para armazenar os DTOs.
- O padrão é o mesmo da camada Domain. Seguindo o exemplo de uma entidade Livro, tería uma pasta Livros.
- ReadonlyAppservice e CrudAppService
	- Caso o serviço de aplicação seja apenas de consulta, a interface poderá herdar da interface IReadOnlyAppService
		- neste caso, os parametros genéricos da interface IReadOnlyAppService serão respectivamente o tipo do DTO de listagem/exibição, o tipo do DTO da chave primária e o DTO de filtro para o método de listagem do AppService. Exemplo:
		`public interface IAssuntoAppService : IReadOnlyAppService<AssuntoDto, AssuntoKey, AssuntoGetListInput>`
	- Caso o seja um CRUD, poderá herdar de ICrudAppService.
		- neste caso, os parametros genéricos da interface ICrudAppService serão respectivamente o tipo do DTO de listagem/exibição, o tipo do DTO da chave primária, o DTO de filtro para o método de listagem do AppService, o DTO do método de inserção e o DTO do método de alteração. Exemplo:
		`public interface ILivroAppService : ICrudAppService< LivroDto, int, LivroGetListInput, CreateUpdateLivroDto, CreateUpdateLivroDto>`
- Caso seja um AppService que não esteja relacionado a nenhuma entidade, a interface deverá ser criada herdando apenas de IApplicationService. Para saber mais acesse o [link](https://docs.abp.io/en/abp/8.1/Application-Services)
- Validação via DTOs
	- Validações simples de entrada de dados, como tamanho de campos e formatação, podem ser implementadas por anotações nas propriedades dos DTOs.

- Para saber mais sobre DTOs e validação nos DTOs, acesse os links https://docs.abp.io/en/abp/latest/Data-Transfer-Objects e https://docs.abp.io/en/abp/latest/Validation

## **Projeto .Application**

Camada de Aplicação: Contém os serviços da aplicação. É onde as funcionalidades são expostas para consumo de algum usuário, que pode acessar por uma camada de apresentação ou por uma API Rest.
    - Todos os métodos públicos são criados com vista à exposição ao uso, isto é:
        - os métodos são criados de acordo com o método HTTP desejado (GET, POST, etc).
        - o acesso pode ser controlado, exemplo, uso de [Authorize], entre outros.
    - Algumas convenções importantes:
        - Todos os serviços devem terminar com sufixo “AppService”, para que o framework o injete no conteiner de dependências de forma automática.
        - O prefixo utilizado no nome dos métodos indica como ele será exposto em uma API. Exemplo: GetAutoresAsync() → exposto como método get “autores” em uma API Rest.
Este projeto contém as implementações de serviço de aplicação das interfaces definidas no projeto `.Application.Contracts`.

Uma classe `LivroAppService` é uma boa candidata para este projeto.

Este projeto contém as implementações de serviço de aplicação das interfaces definidas no projeto `.Application.Contracts`.

- Depende do projeto `.Application.Contracts` para poder implementar as interfaces e usar os DTOs.
- Depende do projeto `.Domain` para poder usar objetos de domínio (entidades, interfaces de repositório, DomainManagers, entre outros) para executar a lógica da aplicação.

Embora tanto os Serviços de Aplicação quanto os Serviços de Domínio implementem as regras de negócio, existem diferenças lógicas e formais fundamentais;

| Serviços de Domínio | Serviços de Aplicação |
| --- | --- |
| métodos de Serviço de Domínio tipicamente obtêm e retornam os objetos de domínio (entidades, objetos de valor) | Serviços de Aplicação obtêm/retornam Objetos de Transferência de Dados |
| Serviços de domínio são tipicamente usados pelos Serviços de Aplicação ou outros Serviços de Domínio | Serviços de Aplicação são usados pela Camada de Apresentação ou Aplicações Cliente |
| Serviços de Domínio não aplicação regras de autorização/permissão | Serviços de Aplicação aceitam anotações em seus métodos para verificar permissão do usuário e/ou sistema para executá-los |
| Serviços de Domínio implementam a lógica do domínio central, independente dos casos de uso. | Serviços de Aplicação implementam os casos de uso da aplicação (interações do usuário em uma aplicação web típica) |

**Ciclo de Vida**

- Tanto Serviços de Domínio quanto Serviço de Aplicação possuem ciclo de vida Transient e são automaticamente registrados no sistema de injeção de dependência.

**Observação**: Para serviços de aplicação serem registrados automaticamente no sistema de injeção de dependência é necessário terminar com "AppService", exemplo: Livro*AppService*

###BaseCrudAppService

Para utilizar os DomainManagers como ponto único de operaçõs de inserção, alteração e exclusão, a classe base fornecidas pelo Abp Framework para implementação de um AppService CRUD completo (`AbstractKeyCrudAppService`) foi sobrescrita para utilizar os métodos do Manager ao invés de utilizar diretamente o repositório. Por isso, os AppServices que são CRUDs de entidades de domínio, devem herdar de `BaseCrudAppService`.

###BaseReadOnlyAppService
Em casos que o serviço de aplicação apenas expõe métodos de leitura, o AppService deve herdar da classe BaseReadOnlyAppService, que também é uma classe que sobrescreve a classe de implementação do Abp Framework para serviços somente de leitura `AbstractKeyReadOnlyAppService`, porém não depende de DomainManagers. 


Para mais detalhes, acessar as páginas de referência do assunto na documentação do ABP Framework

- https://docs.abp.io/en/abp/8.2/Application-Services
- https://docs.abp.io/en/abp/8.2/Domain-Services


## **Projeto EntityFrameworkCore**
- Integração com Entity Framework Core: Esta camada abstrai a interação com o banco de dados usando o ORM Entity Framework Core, incluindo as configurações das entidades, as implementações dos repositórios, e migrations.

Este é o projeto de integração para o EF Core. Define o `DbContext` e implementa interfaces de repositório definidas no projeto `.Domain`.

- Depende do projeto `.Domain` para poder referenciar entidades e interfaces de repositório.

- Mapeamento objeto relacional do projeto atual é feito na pasta EntityFrameworkcore > Configuration
    - Criação da classe de configuração de mapeamento deve ser mais agnóstica possível. Lembre-se, o mapeamento é usado em SQLite para os testes. Leia mais na página [Configurations e Migrations](/Guia-de-Desenvolvimento/Configurations-e-Migrations)

    - Nomes de tabelas e colunas do banco devem seguir o padrão da DIAD - Divisão de Administração de Dados e Banco de Dados. Mais informações sobre padronização no uso do BD, acesse a página [Banco-de-Dados](/Guia-de-Desenvolvimento/Banco-de-Dados)

###Unif of Work (Conexão com BD e Transações)

O UoW é o principal sistema utilizado pelo ABP para iniciar, gerenciar e descartar conexões de banco de dados e transações. 

É necessário realizar todas as operações de banco de dados dentro de um escopo UoW, pois o UoW é o método de gerenciamento de conexões de banco de dados e transações no ABP Framework. A ausência de um escopo UoW resultará em uma exceção.

[Leia mais sobre Unit of Work](/Guia-de-Desenvolvimento/Unit-Of-Work)

###Repositórios
- Deve ser uma implementação da interface definida na pasta da entidade no projeto domain e deve ser colocada numa pasta com mesmo nome do namespace da entidade, seguindo nosso exemplo, seria criada a classe LivroRepository na pasta Livros dentro do projeto EntityFrameworkCore.
- Dentro da mesma pasta é possível incluir um usar extension method para configurar a busca com entidades relacionadas ou nao. Exemplo:
```csharp
using System.Linq;
using Microsoft.EntityFrameworkCore;

namespace TRF3.SISPREC.Livros;

public static class LivroEfCoreQueryableExtensions
{
    public static IQueryable<Livro> IncludeDetails(this IQueryable<Livro> queryable, bool include = true)
    {
        if (!include)
        {
            return queryable;
        }

        return queryable.Include(x => x.Autor);
    }
}

```

## **Projeto HttpApi**

Este projeto é usado para definir API Controllers. Atualmente não o utilizamos. Não há nenhuma implementação nele específica para o SISPREC. Mas para fins de conhecimento, segue a descrição uma breve descrição do motivo de sua existência.

Na maioria das vezes você não precisa definir manualmente API Controllers, já que o recurso de [Auto API Controllers](https://docs.abp.io/en/abp/latest/API/Auto-API-Controllers) do ABP os cria automaticamente com base na sua camada de aplicação. No entanto, caso você precise escrever API controllers, este é o melhor lugar para fazê-lo.

- Depende do projeto `.Application.Contracts` para poder injetar as interfaces de serviço de aplicação.

É útil para definir especificações típicas de API Controllers, áreas, rotas, parametros das rotas, entre outros. Para mais detalhes, visualizar o projeto EventHub.

[Leia mais](/ABP-Docs/Desenvolvimento-de-API/Auto-API-Controllers)

## **Projeto Web**

Este projeto contém a Interface de Usuário (UI) da aplicação quando usa-se Razos Pages (ASP.NET Core MVC UI). Ele contém páginas Razor, arquivos JavaScript, arquivos CSS, imagens, entre outros.

Este projeto contém o principal arquivo `appsettings.json` que contém a string de conexão e outras configurações da aplicação.

Leia mais na wiki do [Projeto Web](/Guia-de-Desenvolvimento/Projeto-Web)

##**Infraestrutura**:
- Camada de Infraestrutura: Camada para implementações de serviços como classes de acesso a arquivos, background jobs, classes utilitárias de Requisições HTTP, StringHelpers, entre outros.

## Pasta ProcessaPecatorio

- Conjunto de módulos específicos para o processamento das etapas de transmissão de precatórios e RPVs para o CJF.

## Pasta SincronizaDominio

- Conjunto de módulos específicos para o sincronização de registros obtidos na API do CFJ.


## **Projetos de Teste**

A solução tem vários projetos de teste, um para cada camada e um projeto base:

- `.Domain.Tests` é usado para testar a camada de domínio.
- `.Application.Tests` é usado para testar a camada de aplicação.
- `.EntityFrameworkCore.Tests` é usado para testar a configuração do EF Core, repositórios personalizados, e realizar testes de integração dos AppServices.
- `.Web.Tests` é usado para testar as páginas do projeto Web.
- `.TestBase` é um projeto base (compartilhado) para todos os testes.

Os projetos de teste são preparados para testes de integração;

- É totalmente integrado ao framework ABP e a todos os serviços da sua aplicação.
- Usa o banco de dados SQLite em memória para o EF Core.
- A autorização é desativada, então qualquer serviço de aplicação pode ser facilmente usado nos testes.

Leia mais no [Guia de Testes](/Guia-de-Testes)
# Modularidade

A modularidade em um projeto ABP é um princípio de design que promove a separação de funcionalidades em módulos distintos, cada um responsável por uma parte específica da aplicação. Cada camada tem uma classe de módulo para configurar sua inicialização, seus módulos dos quais depende, entre outras funções.

Uma breve descrição de como a modularidade funciona em um projeto ABP:

1. **Dependência entre Módulos**:
    - Módulos podem depender uns dos outros. Por exemplo, um módulo de aplicação pode depender de um módulo de domínio para entidades e lógica de negócios.
    - As dependências são explicitamente definidas para garantir que os módulos sejam inicializados na ordem correta.
2. **Inicialização de Módulos**:
    - Cada módulo no ABP possui uma classe de inicialização, geralmente com sufixo “Module” (por exemplo, “WebModule”), onde as configurações específicas do módulo são realizadas.
    - Durante a inicialização, um módulo registra seus próprios serviços, configurações e pode também configurar serviços provenientes de suas dependências, isto é,
3. **Ordem de Inicialização**:
    - Os módulos devem ser inicializados em uma ordem que respeite suas dependências. Por exemplo, o módulo de domínio deve ser inicializado antes do módulo de aplicação que o utiliza.
    - O ABP framework gerencia automaticamente a ordem de inicialização com base nas dependências definidas nas classes dos módulos.
4. **Funções de um Módulo**:
    - A inicialização do módulo faz várias coisas, como:
        - Registrar serviços específicos do módulo no contêiner de injeção de dependência.
        - Configurar middlewares e outras configurações necessárias para o funcionamento do módulo.
        - Preparar o módulo para integrar-se com outros módulos da aplicação.
5. **Camada e Módulo**:
    - Cada camada da aplicação pode ser tratada como um módulo separado (por exemplo, módulo de infraestrutura, módulo de aplicação, módulo de domínio).
6. **Exemplo com o Projeto Web**:
    - O projeto `TRF3.SISPREC.Web` é um módulo de apresentação que depende de outros módulos para funcionar corretamente.
    - Antes de ser inicializado, ele precisa que os módulos de `HttpApi`, `Application`, e `Domain` já estejam configurados e prontos, pois estes fornecem a lógica de negócios e a comunicação necessária para que a interface de usuário funcione.
    - No momento da inicialização, o módulo web não só configura os recursos relacionados à interface do usuário (como Razor Pages ou SPA services), mas também se liga aos serviços disponibilizados pelos módulos dos quais depende.
- Métodos de ciclo de vida:
    
    ```csharp
    public virtual Task PreConfigureServicesAsync(ServiceConfigurationContext context);
    
    public virtual void PreConfigureServices(ServiceConfigurationContext context);
    
    public virtual Task ConfigureServicesAsync(ServiceConfigurationContext context);
    
    public virtual void ConfigureServices(ServiceConfigurationContext context);
    
    public virtual Task PostConfigureServicesAsync(ServiceConfigurationContext context);
    
    public virtual void PostConfigureServices(ServiceConfigurationContext context);
    
    public virtual Task OnPreApplicationInitializationAsync(ApplicationInitializationContext context);
    
    public virtual void OnPreApplicationInitialization(ApplicationInitializationContext context);
    
    public virtual Task OnApplicationInitializationAsync(ApplicationInitializationContext context);
    
    public virtual void OnApplicationInitialization(ApplicationInitializationContext context);
    
    public virtual Task OnPostApplicationInitializationAsync(ApplicationInitializationContext context);
    
    public virtual void OnPostApplicationInitialization(ApplicationInitializationContext context);
    
    public virtual Task OnApplicationShutdownAsync(ApplicationShutdownContext context);
    
    public virtual void OnApplicationShutdown(ApplicationShutdownContext context);
    ```
    

# Injeção de dependências

- Feita por meio da própria infraestrutura de DI do ASP.NET Core, de maneira aperfeiçoada, em vez de usar um framework de injeção de dependências de terceiros.
- O ABP registra automaticamente serviços para injeção de dependências para tipos como Serviços de aplicação, Serviços de domínio, Repositórios, Controllers MVC, entre outros.

Para saber mais sobre modularidade, acesse a página da documentação do ABP Framework https://docs.abp.io/en/abp/latest/Module-Development-Basics


## Appsettings.json

- Em ambiente de desenvolvimento, deve-se usar user-secrets, conforme o [guia de início](/Ambiente-Desenvolvimento#configurando-variáveis-de-ambiente)
- Não há versionamento dos arquivos appsettings.Homolog.json e appsettings.Production.json

## Migrations
Leia mais:
- [Banco de Dados](/Guia-de-Desenvolvimento/Banco-de-Dados#controle-de-mudanças-no-banco-de-dados)
- [Configurations e Migrations](/Guia-de-Desenvolvimento/Configurations-e-Migrations)


## Autenticação e Autorização

Atualmente a autenticação utilizada é via client OIDC, consumindo um servidor do próprio TRF3 chamado CAU.

[Leia mais](/Guia-de-Desenvolvimento/Autenticação-e-Autorização)


## Auditoria
[Leia mais](/Guia-de-Desenvolvimento/Camada-Domínio-\(Projeto-Domain\)/Entidades#auditoria)


## Referências

- [eBook Abp Framework](https://trf3jusbr-my.sharepoint.com/:b:/g/personal/tiviana_trf3_jus_br/EamKShdIrlFLtlyqM9b8JbMBnoOkxyQ2nRncDiq_LRPMlg?e=4gZAqn) 
- [Abo.io](http://Abo.io) Docs
- Projetos
    - https://github.com/abpframework/eventhub
    - https://github.com/abpframework/abp
    - https://github.com/abpframework/abp-samples
