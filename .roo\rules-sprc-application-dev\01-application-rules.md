---
description: Regras específicas para desenvolvimento nas camadas Application.Contracts e Application do TRF3.SISPREC
globs: src/TRF3.SISPREC.Application*/**/*
alwaysApply: true
---

# Regras para SISPREC Application Developer

## **Responsabilidades Principais**
- **Application Services**: Orquestrar operações de negócio
- **DTOs**: Definir objetos de transferência de dados
- **Contratos**: Manter interfaces de serviços
- **Mapeamentos**: Configurar AutoMapper profiles
- **Validações**: Implementar validações de entrada

## **Estrutura das Camadas**
### **Application.Contracts**
- **Interfaces de Serviços**: Contratos dos application services
- **DTOs**: Objetos de transferência de dados
- **Input DTOs**: Objetos para entrada de dados
- **Validações**: Data annotations para validação

### **Application**
- **Application Services**: Implementações dos contratos
- **AutoMapper Profiles**: Mapeamentos entre entidades e DTOs
- **Base Classes**: Classes base para padronização
- **Permissões**: Controle de acesso integrado

## **Padrões de Desenvolvimento**
- **CQRS**: Separação entre comandos e consultas
- **DTO Pattern**: Transferência de dados entre camadas
- **Service Layer**: Camada de serviços de aplicação
- **Mapping Pattern**: Mapeamento automático entre objetos
- **Validation Pattern**: Validação de entrada de dados

## **Application Services Principais**
- **45+ Application Services**: Operações CRUD e regras de negócio
- **BaseCrudAppService**: Classe base para operações CRUD
- **BaseAtivaDesativaAppService**: Classe base para ativação/desativação
- **Controle de Permissões**: Integrado via ABP Framework

## **Diretrizes de Qualidade**
- **Separação de Responsabilidades**: Contratos vs Implementações
- **Orquestração**: Coordenar operações de domínio sem implementar regras
- **Transações**: Gerenciar transações adequadamente
- **Performance**: Otimizar consultas e mapeamentos
- **Segurança**: Aplicar controle de acesso e validações

## **DTOs e Mapeamentos**
- **200+ DTOs**: Para transferência de dados entre camadas
- **AutoMapper Profile**: 100+ mapeamentos configurados
- **Validações**: Via Data Annotations
- **Extensibilidade**: Via DtoExtensions

## **Tecnologias e Frameworks**
- **ABP Framework 8**: Base para application services
- **AutoMapper**: Mapeamento objeto-objeto
- **FluentValidation**: Validações complexas
- **C# 12**: Recursos modernos da linguagem
- **.NET 8**: Framework base

## **Boas Práticas**
- Manter application services focados em orquestração
- Não implementar regras de negócio na camada de aplicação
- Usar transações adequadamente
- Implementar validações robustas
- Otimizar mapeamentos para performance
- Seguir convenções de nomenclatura ABP
