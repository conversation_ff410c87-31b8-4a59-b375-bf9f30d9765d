# Template: <PERSON><PERSON><PERSON><PERSON> Razor SISPREC

## Informações da Página

- **Nome da Entidade**: {NomeEntidade}
- **<PERSON><PERSON><PERSON><PERSON>**: {Modulo}
- **Tipo de Página**: {TipoPagina} (Index | Create | Edit | Details)
- **Per<PERSON><PERSON>ões**: {Permissoes}

## 1. Index Page - Listagem

### Arquivo: `src/TRF3.SISPREC.Web/Pages/{Modulo}/{NomeEntidade}/Index.cshtml`

```html
@page "/{modulo}/{nome-entidade}"
@using TRF3.SISPREC.{Modulo}
@using TRF3.SISPREC.{Modulo}.Dtos
@using TRF3.SISPREC.Permissions
@using TRF3.SISPREC.Web.Pages.{Modulo}.{NomeEntidade}
@model IndexModel
@{
    PageLayout.Content.Title = "{NomeEntidade}";
    PageLayout.Content.BreadCrumb.Add("{NomeEntidade}");
}

<abp-card>
    <abp-card-header>
        <abp-row>
            <abp-column size-md="6">
                <abp-card-title>Lista de {NomeEntidade}</abp-card-title>
            </abp-column>
            <abp-column size-md="6" class="text-end">
                @if (await AuthorizationService.IsGrantedAsync(SISPRECPermissions.{NomeEntidade}.Create))
                {
                    <abp-button id="btn-novo-{nome-entidade}"
                                text="Novo {NomeEntidade}"
                                icon="plus"
                                button-type="Primary"
                                data-test="btn-novo" />
                }
            </abp-column>
        </abp-row>
    </abp-card-header>
    <abp-card-body>
        <!-- Filtros -->
        <abp-row class="mb-3">
            <abp-column size-md="4">
                <div class="input-group">
                    <input type="text" id="filtro-nome" class="form-control" placeholder="Buscar..." />
                    <button class="btn btn-outline-secondary" type="button" id="btn-filtrar">
                        <i class="fa fa-search"></i>
                    </button>
                </div>
            </abp-column>
            <abp-column size-md="3">
                <abp-select id="filtro-status"
                           asp-items="@Model.StatusOptions"
                           label="Status"
                           data-placeholder="Todos" />
            </abp-column>
            <abp-column size-md="3">
                <abp-select id="filtro-tipo"
                           asp-items="@Model.TipoOptions"
                           label="Tipo"
                           data-placeholder="Todos" />
            </abp-column>
            <abp-column size-md="2">
                <button type="button" id="btn-limpar-filtros" class="btn btn-secondary w-100">
                    Limpar
                </button>
            </abp-column>
        </abp-row>

        <!-- Tabela -->
        <abp-table striped-rows="true" id="tabela-{nome-entidade}">
            <thead>
                <tr>
                    <th>Nome</th>
                    <th>Tipo</th>
                    <th>Status</th>
                    <th>Data de Criação</th>
                    <th>Ações</th>
                </tr>
            </thead>
        </abp-table>
    </abp-card-body>
</abp-card>

<!-- Modal de Criação/Edição -->
<div class="modal fade" id="modal-{nome-entidade}" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-{nome-entidade}-title"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="modal-{nome-entidade}-body">
                <!-- Conteúdo carregado dinamicamente -->
            </div>
        </div>
    </div>
</div>
```
