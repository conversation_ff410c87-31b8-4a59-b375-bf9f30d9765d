# Os Temas Oficiais

O ABP fornece um sistema completo de Theming de UI. Embora você possa construir seus próprios temas, você pode usar diretamente os seguintes temas pré-construídos em suas aplicações.

## O Tema LeptonX

O [Tema LeptonX](https://x.leptontheme.com/) é o tema oficial e padrão quando você cria uma nova solução ABP. Uma captura de tela do tema LeptonX:

![Layout da aplicação LeptonX Lite](../images/leptonx-theme-users-page.png)

O tema LeptonX possui duas versões:

*   [Tema LeptonX](lepton-x/index.md) está incluído nas licenças comerciais.
*   [Tema LeptonX Lite](lepton-x-lite/index.md) é fornecido gratuitamente.

> Se você está procurando pela documentação legada do Tema Lepton, por favor [clique aqui](lepton/index.md).

## O Tema Básico

O Tema Básico é um tema minimalista que não adiciona nenhum estilo em cima dos estilos [Bootstrap](https://getbootstrap.com/) puros. Você pode usar o Tema Básico como tema base e construir seu próprio tema ou estilo em cima dele. Aqui, uma captura de tela do tema:

![Layout da aplicação do tema básico](../images/basic-theme-application-layout.png)

Veja os seguintes documentos com base no tipo de UI que você está usando:

-   [Tema Básico - MVC UI](../framework/ui/mvc-razor-pages/basic-theme.md)
-   [Tema Básico - Blazor UI](../framework/ui/blazor/basic-theme.md)
-   [Tema Básico - Angular UI](../framework/ui/angular/basic-theme.md)

## Veja Também

*   [Theming - MVC UI](../framework/ui/mvc-razor-pages/theming.md)
*  [Theming - Blazor UI](../framework/ui/blazor/theming.md)
*   [Theming - Angular UI](../framework/ui/angular/theming.md)
