# Uso de Constantes

## Propósito

As constantes no projeto Domain.Shared definem valores imutáveis utilizados em múltiplas camadas do sistema. São empregadas principalmente para:

- Definir tamanhos máximos de campos
- Estabelecer limites de tamanho para validação de formulários e/ou DTOs (validação frontend e backend)
- Configurar tamanho das colunas nas configurations do EF Core
- Validar ViewModels

## Organização

### Estrutura de Arquivos

As constantes são organizadas em:

- Arquivos específicos por entidade
- Namespace correspondente à entidade
- Nomenclatura padrão: `[EntidadeNome]Consts.cs`
- Criar constantes no padrão SCREAMING_SNAKE_CASE

### Localização

```
src/TRF3.SISPREC.Domain.Shared/
├── [EntidadeNome]/
│   └── [EntidadeNome]Consts.cs
```

## Implementação

### Definição de Constantes

```csharp
namespace TRF3.SISPREC.Entidades;

/// <summary>
/// Define constantes relacionadas à entidade.
/// Utilizado em configurações EF Core e validações.
/// </summary>
public class EntidadeConsts
{
    /// <summary>
    /// Tamanho máximo do campo nome
    /// </summary>
    public const int NOME_TAMANHO_MAX = 150;

    /// <summary>
    /// Tamanho máximo do campo descrição
    /// </summary>
    public const int DESCRICAO_TAMANHO_MAX = 500;
}
```

### Documentação XML

- Obrigatória para classe e constantes
- Descrição clara do propósito
- Indicação do uso (configuração/validação)

## Utilização

### Entity Framework Core

```csharp
public class EntidadeConfiguration : IEntityTypeConfiguration<Entidade>
{
    public void Configure(EntityTypeBuilder<Entidade> builder)
    {
        builder.Property(x => x.Nome)
            .HasMaxLength(EntidadeConsts.NOME_TAMANHO_MAX);
    }
}
```

### ViewModels

```csharp
public class EntidadeViewModel
{
    [MaxLength(EntidadeConsts.NOME_TAMANHO_MAX)]
    public string Nome { get; set; }
}
```
## Exemplos
- Consultar a página [Tutorial de Início - Projeto Domain.Shared](/Tutorial-de-Início/Projeto-Domain.Shared)

## Boas Práticas

1. **Nomenclatura**
   - Use SCREAMING_SNAKE_CASE para constantes
   - Sufixo '_TAMANHO_MAX' para constantes de tamanho
   - Nomes claros e descritivos em maiúsculas com underscore

2. **Organização**
   - Uma classe de constantes por entidade
   - Agrupar constantes relacionadas
   - Manter no Domain.Shared apenas constantes compartilhadas

3. **Documentação**
   - Documentar classe e todas as constantes
   - Explicar propósito e uso
   - Manter documentação atualizada

4. **Manutenção**
   - Evitar duplicação de valores
   - Centralizar alterações de limites
   - Validar impacto de mudanças

5. **Compartilhamento**
   - Utilizar apenas para valores realmente compartilhados
   - Evitar constantes específicas de uma camada
   - Considerar o impacto em todas as camadas ao modificar
