# Bordas

## Introdução

`abp-border` é um atributo Tag Helper do ABP para estilização de bordas.

Uso básico:

```html
<span abp-border="Default"></span>
<span abp-border="Top"></span>
<span abp-border="Right"></span>
<span abp-border="Bottom"></span>
<span abp-border="Left"></span>
```

## Demo

Veja a [página de demonstração de bordas](https://bootstrap-taghelpers.abp.io/Components/Borders) para vê-lo em ação.

## Valores

Um valor indica o tipo, a posição e a cor da borda. Deve ser um dos seguintes valores:

*   `Default`
*   `_0`
*   `Primary`
*   `Secondary`
*   `Success`
*  `Danger`
*   `Warning`
*   `Info`
*   `Light`
*   `Dark`
*   `White`
*   `Primary_0`
*   `Secondary_0`
*   `Success_0`
*   `Danger_0`
*   `Warning_0`
*   `Info_0`
*   `Light_0`
*   `Dark_0`
*   `White_0`
*   `Top`
*   `Top_0`
*   `Top_Primary`
*   `Top_Secondary`
*   `Top_Success`
*   `Top_Danger`
*   `Top_Warning`
*   `Top_Info`
*   `Top_Light`
*   `Top_Dark`
*   `Top_White`
*   `Top_Primary_0`
*   `Top_Secondary_0`
*   `Top_Success_0`
*   `Top_Danger_0`
*   `Top_Warning_0`
*   `Top_Info_0`
*   `Top_Light_0`
*   `Top_Dark_0`
*   `Top_White_0`
*   `Right`
*   `Right_0`
*   `Right_Primary`
*   `Right_Secondary`
*  `Right_Success`
*   `Right_Danger`
*   `Right_Warning`
*   `Right_Info`
*   `Right_Light`
*   `Right_Dark`
*   `Right_White`
*   `Right_Primary_0`
*  `Right_Secondary_0`
*   `Right_Success_0`
*   `Right_Danger_0`
*   `Right_Warning_0`
*   `Right_Info_0`
*   `Right_Light_0`
*   `Right_Dark_0`
*   `Right_White_0`
*   `Left`
*   `Left_0`
*   `Left_Primary`
*   `Left_Secondary`
*   `Left_Success`
*   `Left_Danger`
*   `Left_Warning`
*   `Left_Info`
*   `Left_Light`
*   `Left_Dark`
*   `Left_White`
*   `Left_Primary_0`
*   `Left_Secondary_0`
*   `Left_Success_0`
*   `Left_Danger_0`
*   `Left_Warning_0`
*   `Left_Info_0`
*   `Left_Light_0`
*   `Left_Dark_0`
*   `Left_White_0`
*   `Bottom`
*   `Bottom_0`
*   `Bottom_Primary`
*  `Bottom_Secondary`
*   `Bottom_Success`
*   `Bottom_Danger`
*   `Bottom_Warning`
*   `Bottom_Info`
*   `Bottom_Light`
*   `Bottom_Dark`
*   `Bottom_White`
*   `Bottom_Primary_0`
*   `Bottom_Secondary_0`
*   `Bottom_Success_0`
*   `Bottom_Danger_0`
*   `Bottom_Warning_0`
*   `Bottom_Info_0`
*   `Bottom_Light_0`
*  `Bottom_Dark_0`
*   `Bottom_White_0`

(Valores com `_0` no final são para usos [Subtrativos](https://getbootstrap.com/docs/4.0/utilities/borders/#subtractive))
