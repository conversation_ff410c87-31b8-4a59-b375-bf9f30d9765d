[[_TOC_]]
# Herança
- Deve herdar de Volo.Abp.Domain.Entities.Entity (não herdar de Entity<TKey>)

# Chaves Primárias
- Se chave primária possuir apenas uma propriedade, usar o padrão NomeEntidadeId, salvo exceções.
- PKs devem ser declaradas por meio do método GetKeys(), tanto para PKs com uma ou mais propriedades. Exemplos:
    - ````csharp
        public override object?[] GetKeys()
        {
            return new object[] { NumeroProtocoloRequisicaoId, DataPlanoOrcamentoId };
        }
      ````
    - ````csharp
        public override object?[] GetKeys()
        {
            return new object[] { PropostaId };
        }
      ````
    - manter o uso do `new object[] ` para manter compatível com o gerador de código (AbpHelper).

# Mapeamento
É possível mapear as entidades tanto para tabelas quanto para views. Para mais informações, visualizar a seção [Configurations e Migrations](/Guia-de-Desenvolvimento/Configurations-e-Migrations)

# Models (Entidades sem vinculo no DB)
Em alguns cenários, será necessário criar uma classe que não é uma entidade. Para isso, podem ser criados models apenas com propriedades, que, por exemplo, podem ser utilizadas para representar o retorno de um método de consulta de algum repositório
> Como as interfaces dos repositórios são definidas na camada de domínio, os models que representam o retorno dos métodos dessa interface podem ser criadas na camada de domínio, no mesmo namespace da interface do Repositório.

# SoftDelete
Para implementar o SoftDelete, basta que a entidade implemente a interface ISoftDelete. Isso fará a entidade possuir uma propriedade chamada IsDeleted.
````csharp
namespace Volo.Abp;

//
// Summary:
//     Used to standardize soft deleting entities. Soft-delete entities are not actually
//     deleted, marked as IsDeleted = true in the database, but can not be retrieved
//     to the application normally.
public interface ISoftDelete
{
    //
    // Summary:
    //     Used to mark an Entity as 'Deleted'.
    bool IsDeleted { get; }
}
````

**Mapeamento da propriedade IsDeleted** 
Na configuration da entidade, a propriedade deverá ser mapeada seguindo o padrão estabelecido, da seguinte forma:
````csharp
builder.Property(p => p.IsDeleted).HasColumnName("SIN_EXCLUI").IsRequired().HasDefaultValue(false);
````

Por padrão, qualquer consulta via repositórios NÃO RETORNA as entidades com `IsDeleted=true`.

Para forçar o retorno deve-se utilizar o `IDataFilter<ISoftDelete>`.
Exemplo utilizado em um teste automatizado:
````csharp
// Assert
//Temporary disable the ISoftDelete filter
using (_softDeleteFilter.Disable())
{
    var objetoDeletado = await _repository.FindAsync(a => a.PropostaId == objetoParaExcluir.PropostaId);
    objetoDeletado.IsDeleted.ShouldBeTrue();
}
````

# Auditoria
Por padrão todas entidades devem ser auditadas, isto é, salvar o histórico de alterações. Exceto entidades que não são de negócio, por exemplo, entidade de controle de processamento.

Para habilitar a auditoria em alguma entidade, basta usar a anotação `[Audited]` na classe:
````csharp
    [Audited]
    public class Proposta : Entity, ISoftDelete
````
Para visualizar os registros de auditoria de uma entidade, basta acessar o modal de detalhe do registro da entidade e acionar a aba "Histórico".
