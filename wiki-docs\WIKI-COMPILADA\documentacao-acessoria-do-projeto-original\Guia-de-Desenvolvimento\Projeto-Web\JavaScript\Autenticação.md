# UI para ASP.NET Core MVC / Razor Pages: API JavaScript de Autenticação

A API de Autenticação permite verificar permissões (policies) para o usuário atual no lado do cliente. Desta forma, você pode mostrar/ocultar condicionalmente partes da UI ou realizar sua lógica do lado do cliente com base nas permissões atuais.

> Este documento explica apenas a API JavaScript. Consulte o [documento de autorização](../../../Authorization.md) para entender o sistema de autorização e permissão do ABP.

## Uso Básico

A função `abp.auth.isGranted(...)` é usada para verificar se uma permissão/policy foi concedida ou não:

```js
if (abp.auth.isGranted('DeleteUsers')) {
  //TODO: Excluir o usuário
} else {
  alert("Você não tem permissão para excluir um usuário!");
}
```

## Outros Campos e Funções

*   `abp.auth.isAnyGranted(...)`: Obtém um ou mais nomes de permissão/policy e retorna `true` se pelo menos um deles foi concedido.
*   `abp.auth.areAllGranted(...)`: Obtém um ou mais nomes de permissão/policy e retorna `true` se todos eles foram concedidos.
*   `abp.auth.grantedPolicies`: Este é um objeto onde suas chaves são os nomes de permissão/policy. Você pode encontrar os nomes de permissão/policy concedidos aqui.
