# Razor Pages UI: Modais

Embora você possa continuar usando a forma padrão do [Bootstrap](https://getbootstrap.com/docs/4.5/components/modal/) para criar, abrir e gerenciar modais em suas aplicações, o ABP fornece uma maneira **flexível** de gerenciar modais, **automatizando tarefas comuns** para você.

**Exemplo: Um diálogo modal para criar uma nova entidade de role**

![modal-manager-example-modal](/ABP-Docs/images/modal-manager-example-modal.png =600x)

O ABP oferece os seguintes benefícios para um modal com um formulário dentro dele:

*   Faz **lazy load** do HTML do modal para a página e o **remove** do DOM assim que ele é fechado. Isso torna fácil o consumo de um diálogo modal reutilizável. Além disso, toda vez que você abre o modal, ele será um novo modal, então você não precisa lidar com a redefinição do conteúdo do modal.
*   **Foca automaticamente** no primeiro input do formulário assim que o modal é aberto. Você também pode especificar isso usando uma `function` ou um `jquery selector`.
*   Determina automaticamente o **formulário** dentro de um modal e envia o formulário via **AJAX** em vez de um post de página normal.
*   Verifica automaticamente se o formulário dentro do modal **foi alterado, mas não foi salvo**. Ele avisa o usuário neste caso.
*   **Desativa automaticamente os botões do modal** (salvar e cancelar) até que a operação AJAX seja concluída.
*   Facilita o registro de um **objeto JavaScript que é inicializado** assim que o modal é carregado.

Assim, ele faz você escrever menos código quando você lida com os modais, especialmente os modais com um formulário dentro.

## Uso Básico

### Criando um Modal como uma Razor Page

Para demonstrar o uso, estamos criando uma Razor Page simples, chamada `ProductInfoModal.cshtml`, na pasta `/Pages/Products`:

![modal-page-on-rider](/ABP-Docs/images/modal-page-on-rider.png =600x)

**Conteúdo de ProductInfoModal.cshtml:**

````html
@page
@model MyProject.Web.Pages.Products.ProductInfoModalModel
@{
    Layout = null;
}
<abp-modal>
    <abp-modal-header title="Product Information"></abp-modal-header>
    <abp-modal-body>
        <h3>@Model.ProductName</h3>
        <div>
            <img src="@Model.ProductImageUrl" />
        </div>
        <p>
            @Model.ProductDescription
        </p>
        <p>
            <small><i>Reference: https://acme.com/catalog/</i></small>
        </p>
    </abp-modal-body>
    <abp-modal-footer buttons="Close"></abp-modal-footer>
</abp-modal>
````

*   Esta página define o `Layout` como `null`, pois vamos mostrar isso como um modal. Portanto, não há necessidade de envolver com um layout.
*   Ele usa o [tag helper abp-modal](/Guia-de-Desenvolvimento/Projeto-Web/Modais/Modal-Tag-Helper) para simplificar a criação do código HTML do modal. Você pode usar o código modal padrão do Bootstrap, se preferir.

**Conteúdo de ProductInfoModalModel.cshtml.cs:**

```csharp
using Volo.Abp.AspNetCore.Mvc.UI.RazorPages;

namespace MyProject.Web.Pages.Products
{
    public class ProductInfoModalModel : AbpPageModel
    {
        public string ProductName { get; set; }

        public string ProductDescription { get; set; }

        public string ProductImageUrl { get; set; }

        public void OnGet()
        {
            ProductName = "Acme Indestructo Steel Ball";
            ProductDescription = "The ACME Indestructo Steel Ball is completely indestructible, there is nothing that can destroy it!";
            ProductImageUrl = "https://acme.com/catalog/acmeindestructo.jpg";
        }
    }
}
```

Você pode certamente obter as informações do produto de um banco de dados ou API. Estamos definindo as propriedades hard-coded por uma questão de simplicidade.

### Definindo o Modal Manager

Uma vez que você tem um modal, você pode abri-lo em qualquer página usando um código **JavaScript** simples.

Primeiro, crie um objeto `abp.ModalManager` definindo a `viewUrl`, no arquivo JavaScript da página que usará o modal:

````js
var productInfoModal = new abp.ModalManager({
    viewUrl: '/Products/ProductInfoModal'
});
````

> Se você só precisa especificar a `viewUrl`, você pode passá-la diretamente para o construtor do `ModalManager`, como um atalho. Exemplo: `new abp.ModalManager('/Products/ProductInfoModal');`

### Abrindo o Modal

Em seguida, abra o modal sempre que precisar:

````js
productInfoModal.open();
````

Normalmente, você deseja abrir o modal quando algo acontece; Por exemplo, quando o usuário clica em um botão:

````js
$('#OpenProductInfoModal').click(function(){
    productInfoModal.open();
});
````

O modal resultante será assim:

![modal-example-product-info](/ABP-Docs/images/modal-example-product-info.png)

#### Abrindo o Modal com Argumentos

Quando você chama o método `open()`, o `ModalManager` carrega o HTML do modal solicitando-o da `viewUrl`. Você pode passar alguns **parâmetros de string de consulta** para este URL quando você abrir o modal.

**Exemplo: Passar o id do produto enquanto abre o modal**

````js
productInfoModal.open({
    productId: 42
});
````

Você pode adicionar um parâmetro `productId` ao método get:

````csharp
using Volo.Abp.AspNetCore.Mvc.UI.RazorPages;

namespace MyProject.Web.Pages.Products
{
    public class ProductInfoModalModel : AbpPageModel
    {
        //...

        public async Task OnGetAsync(int productId) //Adicionar parâmetro productId
        {
            //TODO: Obter o produto do banco de dados com o productId fornecido
            //...
        }
    }
}
````

Dessa forma, você pode usar o `productId` para consultar o produto de uma fonte de dados.

## Modais com Formulários

`abp.ModalManager` lida com várias tarefas comuns (descritas na introdução) quando você deseja usar um formulário dentro do modal.

### Exemplo de Modal com um Formulário

Esta seção mostra um exemplo de formulário para criar um novo produto.

#### Criando a Razor Page

Para este exemplo, criar uma nova Razor Page, chamada `ProductCreateModal.cshtml`, na pasta `/Pages/Products`:

![product-create-modal-page-on-rider](/ABP-Docs/images/product-create-modal-page-on-rider.png)

**Conteúdo de ProductCreateModal.cshtml:**

````html
@page
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal
@model MyProject.Web.Pages.Products.ProductCreateModalModel
@{
    Layout = null;
}
<form method="post" action="@Url.Page("/Products/ProductCreateModal")">
    <abp-modal>
        <abp-modal-header title="Create New Product"></abp-modal-header>
        <abp-modal-body>
            <abp-input asp-for="Product.Name"/>
            <abp-input asp-for="Product.Description"/>
            <abp-input asp-for="Product.ReleaseDate"/>
        </abp-modal-body>
        <abp-modal-footer buttons="@AbpModalButtons.Save | @AbpModalButtons.Cancel"></abp-modal-footer>
    </abp-modal>
</form>
````

*   O `abp-modal` foi envolvido pelo `form`. Isso é necessário para colocar os botões `Save` e `Cancel` no formulário. Desta forma, o botão `Save` atua como o botão `submit` para o `form`.
*   Usei os [tag helpers abp-input](/Guia-de-Desenvolvimento/Projeto-Web/tag-helpers/form-elements.md) para simplificar a criação dos elementos do formulário. Caso contrário, você precisa escrever mais HTML.

**Conteúdo de ProductCreateModal.cshtml.cs:**

```csharp
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.UI.RazorPages;

namespace MyProject.Web.Pages.Products
{
    public class ProductCreateModalModel : AbpPageModel
    {
        [BindProperty]
        public PoductCreationDto Product { get; set; }

        public async Task OnGetAsync()
        {
            //TODO: Lógica Get, se disponível
        }

        public async Task<IActionResult> OnPostAsync()
        {
            //TODO: Salvar o Produto...

            return NoContent();
        }
    }
}
```

*   Esta é uma classe `PageModal` simples. O `[BindProperty]` faz o binding do formulário para o modelo quando você envia (submete) o formulário; O sistema ASP.NET Core padrão.
*   `OnPostAsync` retorna `NoContent` (este método é definido pela classe base `AbpPageModel`). Porque não precisamos de um valor de retorno no lado do cliente, após a operação de post do formulário.

**PoductCreationDto:**

`ProductCreateModalModel` usa uma classe `PoductCreationDto` definida como mostrado abaixo:

````csharp
using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;

namespace MyProject.Web.Pages.Products
{
    public class PoductCreationDto
    {
        [Required]
        [StringLength(128)]
        public string Name { get; set; }
        
        [TextArea(Rows = 4)]
        [StringLength(2000)]
        public string Description { get; set; }
        
        [DataType(DataType.Date)]
        public DateTime ReleaseDate { get; set; }
    }
}
````

*   O Tag Helper `abp-input` pode entender os atributos de anotação de dados e os usa para moldar e validar os elementos do formulário. Consulte o documento [tag helpers abp-input](tag-helpers/form-elements.md) para saber mais.

#### Definindo o Modal Manager

Novamente, crie um objeto `abp.ModalManager` definindo a `viewUrl`, no arquivo JavaScript da página que usará o modal:

````js
var productCreateModal = new abp.ModalManager({
    viewUrl: '/Products/ProductCreateModal'
});
````

#### Abrindo o Modal

Em seguida, abra o modal sempre que precisar:

````js
productCreateModal.open();
````

Normalmente, você deseja abrir o modal quando algo acontece; Por exemplo, quando o usuário clica em um botão:

````js
$('#OpenProductCreateModal').click(function(){
    productCreateModal.open();
});
````

Portanto, o código completo será algo assim (assumindo que você tenha um `button` com `id` igual a `OpenProductCreateModal` no lado da view):

```js
$(function () {

    var productCreateModal = new abp.ModalManager({
        viewUrl: '/Products/ProductCreateModal'
    });

    $('#OpenProductCreateModal').click(function () {
        productCreateModal.open();
    });

});
```

O modal resultante será assim:

![modal-example-product-create](/ABP-Docs/images/modal-example-product-create.png =600x)

#### Salvando o Modal

Quando você clica no botão `Save`, o formulário é enviado para o servidor. Se o servidor retornar uma **resposta de sucesso**, então o evento `onResult` é acionado com alguns argumentos, incluindo a resposta do servidor, e o modal é fechado automaticamente.

Um exemplo de callback que registra os argumentos passados para o método `onResult`:

````js
productCreateModal.onResult(function(){
   console.log(arguments);
});
````

Se o servidor retornar uma resposta de falha, ele mostra a mensagem de erro retornada do servidor e mantém o modal aberto.

> Consulte a seção *Modal Manager Reference* abaixo para outros eventos modais.

#### Cancelando o Modal

Se você clicar no botão Cancelar com algumas alterações feitas, mas não salvas, você receberá essa mensagem de aviso:

![modal-manager-cancel-warning](/ABP-Docs/images/modal-manager-cancel-warning.png =600x)

Se você não quiser essa verificação e mensagem, você pode adicionar o atributo `data-check-form-on-close="false"` ao seu elemento `form`. Exemplo:

````html
<form method="post"
      action="@Url.Page("/Products/ProductCreateModal")"
      data-check-form-on-close="false">
````

### Validação de Formulário

O `ModalManager` aciona automaticamente a validação do formulário quando você clica no botão `Save` ou pressiona a tecla `Enter` no formulário:

![modal-manager-validation](/ABP-Docs/images/modal-manager-validation.png =600x)

Consulte o documento [Forms & Validation](/Guia-de-Desenvolvimento/Projeto-Web/Validação/Formulários) para saber mais sobre a validação.

## Modais com Arquivos de Script

Você pode precisar executar alguma lógica para seu modal. Para fazer isso, crie um arquivo JavaScript como abaixo:

````js
abp.modals.ProductInfo = function () {

    function initModal(modalManager, args) {
        var $modal = modalManager.getModal();
        var $form = modalManager.getForm();

        $modal.find('h3').css('color', 'red');
        
        console.log('initialized the modal...');
    };

    return {
        initModal: initModal
    };
};
````

*   Este código simplesmente adiciona uma classe `ProductInfo` no namespace `abp.modals`. A classe `ProductInfo` expõe uma única função pública: `initModal`.
*   O método `initModal` é chamado pelo `ModalManager` assim que o HTML do modal é inserido no DOM e está pronto para a lógica de inicialização.
*   O parâmetro `modalManager` é o objeto `ModalManager` relacionado a esta instância modal. Portanto, você pode usar qualquer função nele em seu código. Consulte a seção *Modal Manager Reference*.

Em seguida, inclua este arquivo na página em que você usa o modal:

````html
<abp-script src="/Pages/Products/ProductInfoModal.js"/>
<abp-script src="/Pages/Products/Index.js"/>
````

*   Nós usamos o Tag Helper `abp-script` aqui. Consulte o documento [Bundling & Minification](bundling-minification.md) se você quiser entendê-lo. Você pode usar a tag `script` padrão. Não importa para este caso.

Finalmente, defina a opção `modalClass` ao criar a instância do `ModalManager`:

````js
var productInfoModal = new abp.ModalManager({
    viewUrl: '/Products/ProductInfoModal',
    modalClass: 'ProductInfo' //Corresponde ao abp.modals.ProductInfo
});
````

### Lazy Loading do Arquivo de Script

Em vez de adicionar o `ProductInfoModal.js` à página em que você usa o modal, você pode configurá-lo para carregar o arquivo de script preguiçosamente quando o modal for aberto pela primeira vez.

Exemplo:

````js
var productInfoModal = new abp.ModalManager({
    viewUrl: '/Products/ProductInfoModal',
    scriptUrl: '/Pages/Products/ProductInfoModal.js', //Lazy Load URL
    modalClass: 'ProductInfo'
});
````

*   `scriptUrl` é usado para definir o URL para carregar o arquivo de script do modal.
*   Nesse caso, você não precisa mais incluir o `ProductInfoModal.js` na página. Ele será carregado sob demanda.

#### Dica: Bundling & Minification

Embora o lazy loading pareça legal no início, ele requer uma chamada adicional para o servidor quando você abre o modal pela primeira vez.

Em vez disso, você pode usar o sistema [Bundling & Minification](/Guia-de-Desenvolvimento/Projeto-Web/bundling-minification.md) para criar um bundle (que é um arquivo único e minificado em produção) para todos os arquivos de script usados para uma página:

````html
<abp-script-bundle>
    <abp-script src="/Pages/Products/ProductInfoModal.js"/>
    <abp-script src="/Pages/Products/Index.js"/>
</abp-script-bundle>
````

Isso é eficiente se o arquivo de script não for grande e for aberto com frequência enquanto os usuários usam a página.

Alternativamente, você pode definir a classe `abp.modals.ProductInfo` no arquivo JavaScript principal da página se o modal for apenas e sempre usado na mesma página. Nesse caso, você não precisa de outro arquivo de script externo.

## Referência do ModalManager

### Opções

As opções podem ser passadas quando você cria um novo objeto `ModalManager`:

````js
var productInfoModal = new abp.ModalManager({
    viewUrl: '/Products/ProductInfoModal',
    //...outras opções
});
````

Aqui, a lista de todas as opções disponíveis:

*   `viewUrl` (obrigatório, `string`): O URL para carregar o HTML do modal.
*   `scriptUrl` (opcional, `string`): Um URL para carregar um arquivo JavaScript. Ele é carregado apenas uma vez, quando o modal é aberto pela primeira vez.
*   `modalClass` (opcional, `string`): Uma classe JavaScript definida no namespace `abp.modals` que pode ser usada para executar código relacionado ao modal.
*   `focusElement` (opcional, `function ou string`): Especifica o elemento que recebe o foco.

### Funções

Quando você cria um novo objeto `ModalManager`, você pode usar suas funções para executar operações no modal. Exemplo:

````js
var myModal = new abp.ModalManager({
    //...opções
});

//Abrir o modal
myModal.open();

//Fechar o modal
myModal.close();
````

Aqui, a lista de todas as funções disponíveis do objeto `ModalManager`;

*   `open([args])`: Abre o diálogo modal. Ele pode obter um objeto `args` que é convertido em string de consulta ao obter o `viewUrl` do servidor. Por exemplo, se `args` for `{ productId: 42 }`, então o `ModalManager` passa `?productId=42` para o final do `viewUrl` ao carregar a view do servidor.
*   `reopen()`: Abre o modal com os últimos `args` fornecidos para o método `open()`. Portanto, é um atalho se você quiser reabrir o modal com os mesmos `args`.
*   `close()`: Fecha o modal. O HTML do modal é automaticamente removido do DOM assim que ele é fechado.
*   `getModalId()`: Obtém o atributo `id` do contêiner que contém a view retornada do servidor. Este é um id único por modal e não muda depois que você cria o `ModalManager`.
*   `getModal()`: Retorna o elemento DOM do wrapper do modal (o elemento HTML com a classe CSS `modal`) como uma seleção JQuery, para que você possa executar qualquer método JQuery nele.
*   `getForm()`: Retorna o elemento HTML `form` como uma seleção JQuery, para que você possa executar qualquer método JQuery nele. Ele retorna `null` se o modal não tiver nenhum formulário dentro dele.
*   `getArgs()` Obtém o último objeto de argumentos fornecido ao abrir o modal.
*   `getOptions()`: Obtém o objeto de opções passado para o construtor `ModalManager`.
*   `setResult(...)`: Aciona o evento `onResult` com os argumentos fornecidos. Você pode passar zero ou mais argumentos que são passados diretamente para o evento `onResult`. Esta função é geralmente chamada pelo script modal para notificar a página que usa o modal.

### Eventos

Quando você cria um novo objeto `ModalManager`, você pode usar suas funções para se registrar nos eventos do modal. Exemplos:

````js
var myModal = new abp.ModalManager({
    //...opções
});

myModal.onOpen(function () {
    console.log('opened the modal...');
});

myModal.onClose(function () {
    console.log('closed the modal...');
});
````

Aqui, a lista de todas as funções disponíveis para se registrar nos eventos do objeto `ModalManager`;

*   `onOpen(callback)`: Registra uma função de callback para ser notificada assim que o modal é aberto. Ele é acionado quando o modal está completamente visível na UI.
*   `onClose(callback)`: Registra uma função de callback para ser notificada assim que o modal é fechado. Ele é acionado quando o modal está completamente invisível na UI.
*   `onResult(callback)`: Registra uma função de callback que é acionada quando o método `setResult(...)` é chamado. Todos os parâmetros enviados para o método `setResult` são passados para o callback.
