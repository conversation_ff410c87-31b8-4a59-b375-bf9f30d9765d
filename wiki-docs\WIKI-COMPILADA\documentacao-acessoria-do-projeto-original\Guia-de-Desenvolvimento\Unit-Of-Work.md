[[_TOC_]]
# Unit of Work

A implementação de Unit of Work (UOW/Unidade de Trabalho) do ABP fornece uma abstração e controle sobre o **escopo de conexão e transação de banco de dados** em uma aplicação.

Uma vez que uma nova UOW é iniciada, ela cria um **escopo ambiente** que é compartilhado por **todas as operações de banco de dados** realizadas dentro do escopo atual, sendo considerado como um **fronteira única de transação**. As operações são **confirmadas** (em caso de sucesso) ou **desfeitas** (em caso de exceção) todas juntas.

O sistema de UOW do ABP é:

* **Funciona de maneira convencional**, então, na maioria das vezes, você não lida com UOW diretamente.

* **Independente de Web**, o que significa que você pode criar escopos de unidade de trabalho em qualquer tipo de aplicação além de aplicações/web services (background jobs, background workers, console applications, etc...)

# Convenções
Uma UOW é iniciada automaticamente para esses métodos **exceto** se já houver uma **UOW ambiental (em andamento)**.:

* Métodos de **AppServices**.
* Métodos de **repositório**.
* **Page Handlers** no ASP.NET Core Razor.
* Ações de **Controller** no ASP.NET Core MVC.



Se você chamar um método de [repositório](https://abp.io/docs/latest/framework/architecture/domain-driven-design/repositories) e ainda não houver uma UOW iniciada, ela automaticamente **inicia uma nova UOW transacional** que envolve todas as operações realizadas no método do repositório e **confirma a transação** se o método do repositório **não lançar nenhuma exceção**. 

O método do repositório não sabe sobre a UOW ou transação. Ele apenas trabalha com objetos de banco de dados regulares (`DbContext` para EF Core, por exemplo) e a UOW é gerida pelo ABP.

Se você chamar um método de serviço de aplicação (AppServices), o mesmo sistema de UOW funciona 
como explicado acima. Se o método do serviço de aplicação (AppServices) usar algum repositório, os repositórios **não iniciam uma nova UOW**, mas **participam da UOW atual** iniciada pelo ABP para o método do serviço de aplicação (AppServices).

O mesmo vale para uma ação de controller do ASP.NET Core. Se a operação foi iniciada com uma ação de controller, então o **escopo de UOW é o corpo do método da ação do controller**.

Tudo isso é tratado automaticamente pelo ABP.

## Comportamento da Transação do Banco de Dados

Embora a seção acima explique a UOW como uma transação de banco de dados, na verdade, uma UOW não precisa ser transacional. Por padrão:

* Requisições **HTTP GET** não iniciam uma UOW transacional. Elas ainda iniciam uma UOW, mas **não criam uma transação de banco de dados**.
* Todos os outros tipos de requisições HTTP iniciam uma UOW com uma transação de banco de dados, se transações de nível de banco de dados forem suportadas pelo provedor de banco de dados subjacente.

Isso ocorre porque uma requisição HTTP GET não (e não deveria) fazer alterações no banco de dados. Você pode alterar esse comportamento utilizando as opções explicadas abaixo.

# Controlando a UoW Manualmente

Em alguns casos, você pode querer alterar o escopo convencional da transação, criar escopos internos ou controlar detalhadamente o comportamento da transação, como background jobs, ou outras partes do código em que escopos de Uow não são criados automaticamente.

## IUnitOfWorkManager

`IUnitOfWorkManager` é o serviço principal utilizado para controlar o sistema de unidade de trabalho. As seções seguintes explicam como trabalhar diretamente com este serviço.

### Iniciar uma Nova UoW

O método `IUnitOfWorkManager.Begin` é utilizado para criar um novo escopo de UOW.

**Exemplo: Criar um novo escopo UOW não transacional**

````csharp
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Uow;

namespace AbpDemo
{
    public class MyService : ITransientDependency
    {
        private readonly IUnitOfWorkManager _unitOfWorkManager;

        public MyService(IUnitOfWorkManager unitOfWorkManager)
        {
            _unitOfWorkManager = unitOfWorkManager;
        }
        
        public virtual async Task FooAsync()
        {
            using (var uow = _unitOfWorkManager.Begin(
                requiresNew: true, isTransactional: false
            ))
            {
                //...
                
                await uow.CompleteAsync();
            }
        }
    }
}
````

O método `Begin` recebe os seguintes parâmetros opcionais:

* `requiresNew` (`bool`): Defina como `true` para ignorar a UOW em andamento e iniciar uma nova UOW com as opções fornecidas. **O valor padrão é `false`. Se for `false` e houver uma UOW em andamento, o método `Begin` não iniciará uma nova UOW, mas participará silenciosamente da UOW existente.**
* `isTransactional` (`bool`). O valor padrão é `false`.
* `isolationLevel` (`IsolationLevel?`): Usado para definir o [nível de isolamento](https://docs.microsoft.com/en-us/dotnet/api/system.data.isolationlevel) da transação do banco de dados, se a UOW for transacional. Se não configurado, utiliza o valor configurado por padrão.
* `TimeOut` (`int?`): Usado para definir o valor do tempo limite para esta UOW. **O valor padrão é `null`** e recai sobre o valor configurado por padrão.

## Unit Of Work Atual (existente)

A UOW é ambiental, como explicado antes. Se você precisar acessar a unidade de trabalho atual, pode usar a propriedade `IUnitOfWorkManager.Current`.

Exemplo: Obter a UOW atual

````csharp
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Uow;

namespace AbpDemo
{
    public class MyProductService : ITransientDependency
    {
        private readonly IUnitOfWorkManager _unitOfWorkManager;

        public MyProductService(IUnitOfWorkManager unitOfWorkManager)
        {
            _unitOfWorkManager = unitOfWorkManager;
        }
        
        public async Task FooAsync()
        {
            var uow = _unitOfWorkManager.Current;
            //...
        }
    }
}
````

###A propriedade `Current` de um objeto `UnitOfWorkManager`.

> Caso a variável `uow` não esteja disponível no escopo (exemplo: um método privado), é possível acioná-lo pela propriedade `_unitOfWorkManager.Current`. 
Isto é útil para realizar alguma ação, como salvar mudanças em caso de uma interrupção de execução de um Job, por exemplo:
````csharp
if (_cancellationToken.IsCancellationRequested)
{
   await _unitOfWorkManager.Current!.SaveChangesAsync();
   _cancellationToken.ThrowIfCancellationRequested();
}
````

#### SaveChangesAsync

O método `IUnitOfWork.SaveChangesAsync()` pode ser necessário para salvar todas as alterações até o momento no banco de dados. Se você estiver usando o EF Core, o comportamento é exatamente o mesmo. 
Se a UOW atual for transacional, até mesmo as alterações salvas podem ser desfeitas em caso de erro (para os provedores de banco de dados que oferecem suporte).

**Exemplo: Salvar alterações após inserir uma entidade para obter seu id auto-incrementado**

````csharp
using System.Threading.Tasks;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace AbpDemo
{
    public class CategoryAppService : ApplicationService, ICategoryAppService
    {
        private readonly IRepository<Category, int> _categoryRepository;

        public CategoryAppService(IRepository<Category, int> categoryRepository)
        {
            _categoryRepository = categoryRepository;
        }

        public async Task<int> CreateAsync(string name)
        {
            var category = new Category {Name = name};
            await _categoryRepository.InsertAsync(category);
            
            //Salvar alterações para conseguir pegar o id auto-incrementado
            await UnitOfWorkManager.Current.SaveChangesAsync();
            
            return category.Id;
        }
    }
}
````

Este exemplo usa a chave primária `int` auto-incrementada para a entidade`Category`. 
Chaves primárias auto-incrementadas exigem salvar a entidade no banco de dados para obter o id da nova entidade.

Este exemplo é de um [serviço de aplicação (AppServices)](https://abp.io/docs/8.2/framework/architecture/domain-driven-design/application-services) derivado da classe base `ApplicationService`, que já tem o serviço `IUnitOfWorkManager` injetado como a propriedade `UnitOfWorkManager`. Portanto, não há necessidade de injetá-lo manualmente.

Como obter a UOW atual é bastante comum, também existe a propriedade `CurrentUnitOfWork` como um atalho para o `UnitOfWorkManager.Current`. Assim, o exemplo acima pode ser alterado para usá-la:

````csharp
await CurrentUnitOfWork.SaveChangesAsync();
````

### Alternativa ao SaveChanges()

Como salvar alterações após inserir, atualizar ou excluir uma entidade pode ser frequentemente necessário, métodos correspondentes de repositório possuem um parâmetro opcional `autoSave`. Portanto, o método `CreateAsync` acima poderia ser reescrito como mostrado abaixo:

````csharp
public async Task<int> CreateAsync(string name)
{
    var category = new Category {Name = name};
    await _categoryRepository.InsertAsync(category, autoSave: true);
    return category.Id;
}
````

Se sua intenção for apenas salvar as alterações após criar/atualizar/excluir uma entidade, é sugerido usar a opção `autoSave` em vez de usar manualmente o `CurrentUnitOfWork.SaveChangesAsync()`.

> **Nota**: Todas as alterações são automaticamente salvas quando uma unidade de trabalho termina sem erro. Então, não chame `SaveChangesAsync()` nem defina `autoSave` como `true`, a menos que realmente precise disso.

#### Outras Propriedades/Métodos do IUnitOfWork

* O método `OnCompleted` obtém uma ação de callback que é chamada quando a unidade de trabalho for concluída com sucesso (onde você pode ter certeza de que todas as alterações foram salvas).
* Os eventos `Failed` e `Disposed` podem ser usados para ser notificado se a UOW falhar ou quando for descartada.
* Os métodos `Complete` e `Rollback` são usados para concluir (commit) ou reverter a UOW atual, que normalmente são usados internamente pelo ABP, mas podem ser usados se você iniciar uma transação manualmente usando o método `IUnitOfWorkManager.Begin`.
* `Options` pode ser usado para obter as opções que foram usadas ao iniciar a UOW.
* O dicionário `Items` pode ser usado para armazenar e obter objetos arbitrários dentro da mesma unidade de trabalho, o que pode ser um ponto para implementar lógicas personalizadas.

## Integração com ASP.NET Core

O sistema de unidade de trabalho é totalmente integrado ao ASP.NET Core. Ele funciona corretamente quando você usa Controllers do ASP.NET Core MVC ou Razor Pages. Ele define filtros de ação e filtros de página para o sistema de UOW.

> Normalmente, você não precisa fazer nada para configurar a UOW quando usa o ASP.NET Core.

### Middleware de Unit Of Work

`AbpUnitOfWorkMiddleware` é um middleware que pode habilitar a UOW no pipeline de requisições do ASP.NET Core. Isso pode ser necessário se você precisar ampliar o escopo da UOW para cobrir outros middlewares.

**Exemplo:**

````csharp
app.UseUnitOfWork();
app.UseConfiguredEndpoints();
````

## Veja Também
* [Página da documentação oficial sobre UoW do ABP](https://abp.io/docs/8.2/framework/architecture/domain-driven-design/unit-of-work)
* [Tutorial em vídeo Código](https://abp.io/video-courses/essentials/unit-of-work-code)
* [Tutorial em vídeo Apresentação](https://abp.io/video-courses/essentials/unit-of-work-presentation)