# Widgets

ABP fornece um modelo e infraestrutura para criar **widgets reutilizáveis**. O sistema de widgets é uma extensão dos [ViewComponents do ASP.NET Core](https://docs.microsoft.com/en-us/aspnet/core/mvc/views/view-components). Widgets são especialmente úteis quando você deseja:

* Ter dependências de **scripts e estilos** para seu widget.
* Criar **dashboards** com widgets usados dentro deles.
* Definir widgets em **[módulos](/Guia-de-Desenvolvimento/Projeto-Web/Module-Development-Basics.md)** reutilizáveis.
* Cooperar widgets com sistemas de **[autorização](/Guia-de-Desenvolvimento/Projeto-Web/Authorization.md)** e **[bundling](/Guia-de-Desenvolvimento/Projeto-Web/Bundling-Minification.md)**.

## Definição Básica de Widget

### Criar um View Component

Como primeiro passo, crie um novo View Component regular do ASP.NET Core:

![widget-basic-files](/ABP-Docs/images/widget-basic-files.png)

**MySimpleWidgetViewComponent.cs**:

````csharp
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc;

namespace DashboardDemo.Web.Pages.Components.MySimpleWidget
{
    public class MySimpleWidgetViewComponent : AbpViewComponent
    {
        public IViewComponentResult Invoke()
        {
            //TODO: CONFIGURAÇÃO
            return View();
        }
    }
}
````

Herdar de `AbpViewComponent` não é obrigatório. Você pode herdar do `ViewComponent` padrão do ASP.NET Core. `AbpViewComponent` apenas define algumas propriedades úteis básicas.

Você pode injetar um serviço e usá-lo no método `Invoke` para obter alguns dados do serviço. Você pode precisar tornar o método Invoke assíncrono, como `public async Task<IViewComponentResult> InvokeAsync()`. Veja o documento [ViewComponents do ASP.NET Core](https://docs.microsoft.com/en-us/aspnet/core/mvc/views/view-components) para todos os diferentes usos.

**Default.cshtml**:

```xml
<div class="my-simple-widget">
    <h2>My Simple Widget</h2>
    <p>This is a simple widget!</p>
</div>
```

### Definir o Widget

Adicione um atributo `Widget` à classe `MySimpleWidgetViewComponent` para marcar este view component como um widget:

````csharp
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.UI.Widgets;

namespace DashboardDemo.Web.Pages.Components.MySimpleWidget
{
    [Widget]
    public class MySimpleWidgetViewComponent : AbpViewComponent
    {
        public IViewComponentResult Invoke()
        {
            //TODO: CONFIGURAÇÃO
            return View();
        }
    }
}
````

## Renderizando um Widget

Renderizar um widget é bastante padrão. Use o método `Component.InvokeAsync` em uma view/page razor como você faz para qualquer view component. Exemplos:

````xml
@await Component.InvokeAsync("MySimpleWidget")
@await Component.InvokeAsync(typeof(MySimpleWidgetViewComponent))
````

A primeira abordagem usa o nome do widget, enquanto a segunda abordagem usa o tipo do view component.

### Widgets com Argumentos

O sistema de view component do ASP.NET Core permite que você aceite argumentos para view components. O view component de exemplo abaixo aceita `startDate` e `endDate` e usa esses argumentos para recuperar dados de um serviço.

````csharp
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.UI.Widgets;

namespace DashboardDemo.Web.Pages.Shared.Components.CountersWidget
{
    [Widget]
    public class CountersWidgetViewComponent : AbpViewComponent
    {
        private readonly IDashboardAppService _dashboardAppService;

        public CountersWidgetViewComponent(IDashboardAppService dashboardAppService)
        {
            _dashboardAppService = dashboardAppService;
        }

        public async Task<IViewComponentResult> InvokeAsync(
            DateTime startDate, DateTime endDate)
        {
            var result = await _dashboardAppService.GetCountersWidgetAsync(
                new CountersWidgetInputDto
                {
                    StartDate = startDate,
                    EndDate = endDate
                }
            );

            return View(result);
        }
    }
}
````

Agora, você precisa passar um objeto anônimo para passar argumentos, como mostrado abaixo:

````xml
@await Component.InvokeAsync("CountersWidget", new
{
    startDate = DateTime.Now.Subtract(TimeSpan.FromDays(7)),
    endDate = DateTime.Now
})
````

## Nome do Widget

O nome padrão dos view components é calculado com base no nome do tipo do view component. Se o seu tipo de view component for `MySimpleWidgetViewComponent`, então o nome do widget será `MySimpleWidget` (remove o sufixo `ViewComponent`). É assim que o ASP.NET Core calcula o nome de um view component.

Para personalizar o nome do widget, basta usar o atributo padrão `ViewComponent` do ASP.NET Core:

```csharp
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.UI.Widgets;

namespace DashboardDemo.Web.Pages.Components.MySimpleWidget
{
    [Widget]
    [ViewComponent(Name = "MyCustomNamedWidget")]
    public class MySimpleWidgetViewComponent : AbpViewComponent
    {
        public IViewComponentResult Invoke()
        {
            //TODO: CONFIGURAÇÃO
            return View("~/Pages/Components/MySimpleWidget/Default.cshtml");
        }
    }
}
```

ABP respeitará o nome personalizado ao lidar com o widget.

> Se o nome do view component e o nome da pasta do view component não corresponderem, você pode precisar escrever manualmente o caminho da view, como feito neste exemplo.

### Nome de Exibição

Você também pode definir um nome de exibição legível por humanos e localizável para o widget. Este nome de exibição pode ser usado na interface do usuário quando necessário. O nome de exibição é opcional e pode ser definido usando as propriedades do atributo `Widget`:

````csharp
using DashboardDemo.Localization;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.UI.Widgets;

namespace DashboardDemo.Web.Pages.Components.MySimpleWidget
{
    [Widget(
        DisplayName = "MySimpleWidgetDisplayName", //Chave de localização
        DisplayNameResource = typeof(DashboardDemoResource) //recurso de localização
        )]
    public class MySimpleWidgetViewComponent : AbpViewComponent
    {
        public IViewComponentResult Invoke()
        {
            //TODO: CONFIGURAÇÃO
            return View();
        }
    }
}
````

Veja o documento [localização](/Guia-de-Desenvolvimento/Projeto-Web/Localization.md) para aprender sobre recursos e chaves de localização.

## Dependências de Estilo e Script

Existem alguns desafios quando seu widget possui arquivos de script e estilo;

* Qualquer página que usa o widget também deve incluir os **seus arquivos de script e estilos** na página.
* A página também deve se preocupar com **bibliotecas/arquivos dependentes** do widget.

O ABP resolve esses problemas quando você relaciona adequadamente os recursos com o widget. Você não se preocupa com as dependências do widget ao usá-lo.

### Definindo como Caminhos de Arquivo Simples

O widget de exemplo abaixo adiciona um arquivo de estilo e um arquivo de script:

````csharp
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.UI.Widgets;

namespace DashboardDemo.Web.Pages.Components.MySimpleWidget
{
    [Widget(
        StyleFiles = new[] { "/Pages/Components/MySimpleWidget/Default.css" },
        ScriptFiles = new[] { "/Pages/Components/MySimpleWidget/Default.js" }
        )]
    public class MySimpleWidgetViewComponent : AbpViewComponent
    {
        public IViewComponentResult Invoke()
        {
            //TODO: CONFIGURAÇÃO
            return View();
        }
    }
}
````

O ABP leva em conta essas dependências e as adiciona adequadamente à view/page quando você usa o widget. Os arquivos de estilo/script podem ser **físicos ou virtuais**. É completamente integrado ao [Sistema de Arquivos Virtuais](/Guia-de-Desenvolvimento/Projeto-Web/Virtual-File-System.md).

### Definindo Contribuidores de Bundle

Todos os recursos para widgets usados em uma página são adicionados como um **bundle** (agrupados e minificados em produção, se você não configurar de outra forma). Além de adicionar um arquivo simples, você pode aproveitar todo o poder dos contribuidores de bundle.

O código de exemplo abaixo faz o mesmo que o código acima, mas define e usa contribuidores de bundle:

````csharp
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.UI.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.Widgets;

namespace DashboardDemo.Web.Pages.Components.MySimpleWidget
{
    [Widget(
        StyleTypes = new []{ typeof(MySimpleWidgetStyleBundleContributor) },
        ScriptTypes = new[]{ typeof(MySimpleWidgetScriptBundleContributor) }
        )]
    public class MySimpleWidgetViewComponent : AbpViewComponent
    {
        public IViewComponentResult Invoke()
        {
            //TODO: CONFIGURAÇÃO
            return View();
        }
    }

    public class MySimpleWidgetStyleBundleContributor : BundleContributor
    {
        public override void ConfigureBundle(BundleConfigurationContext context)
        {
            context.Files
              .AddIfNotContains("/Pages/Components/MySimpleWidget/Default.css");
        }
    }

    public class MySimpleWidgetScriptBundleContributor : BundleContributor
    {
        public override void ConfigureBundle(BundleConfigurationContext context)
        {
            context.Files
              .AddIfNotContains("/Pages/Components/MySimpleWidget/Default.js");
        }
    }
}

````

O sistema de contribuição de bundle é muito poderoso. Se o seu widget usa uma biblioteca JavaScript para renderizar um gráfico, você pode declará-la como uma dependência, para que a biblioteca JavaScript seja automaticamente adicionada à página se não tiver sido adicionada antes. Dessa forma, a página que usa seu widget não se preocupa com as dependências.

Veja a documentação de [bundling e minificação](Bundling-Minification.md) para mais informações sobre esse sistema.

## RefreshUrl

Um widget pode projetar uma `RefreshUrl` que é usada sempre que o widget precisa ser atualizado. Se for definida, o widget é re-renderizado no lado do servidor em cada atualização (veja o método de atualização do `WidgetManager` abaixo).

````csharp
[Widget(RefreshUrl = "Widgets/Counters")]
public class CountersWidgetViewComponent : AbpViewComponent
{
    
}
````

Uma vez que você define uma `RefreshUrl` para seu widget, você precisa fornecer um endpoint para renderizá-lo e retorná-lo:

````csharp
[Route("Widgets")]
public class CountersWidgetController : AbpController
{
    [HttpGet]
    [Route("Counters")]
    public IActionResult Counters(DateTime startDate, DateTime endDate)
    {
        return ViewComponent("CountersWidget", new {startDate, endDate});
    }
}
````

A rota `Widgets/Counters` corresponde à `RefreshUrl` declarada anteriormente.

> Um widget deve ser atualizado de duas maneiras: Na primeira maneira, quando você usa uma `RefreshUrl`, ele é re-renderizado no servidor e substituído pelo HTML retornado do servidor. Na segunda maneira, o widget obtém dados (geralmente um objeto JSON) do servidor e se atualiza no lado do cliente (veja o método de atualização na seção API de JavaScript do Widget).

## AutoInitialize

`WidgetAttribute` tem uma propriedade `AutoInitialize` (`bool`) que pode ser configurada para inicializar automaticamente um widget quando a página estiver pronta e sempre que o widget for adicionado ao DOM. O valor padrão é `false`.

Se um widget for configurado para ser inicializado automaticamente, então um `WidgetManager` (veja abaixo) é automaticamente criado e inicializado para instâncias desse widget. Isso é útil quando as instâncias do widget não estão agrupadas e funcionam separadamente (não requerem inicialização ou atualização juntas).

Configurar o `AutoInitialize` como `true` é equivalente a escrever um código como este:

````js
$('.abp-widget-wrapper[data-widget-name="MySimpleWidget"]')
    .each(function () {
        var widgetManager = new abp.WidgetManager({
            wrapper: $(this),
        });

        widgetManager.init($(this));
    });
````

> `AutoInitialize` também suporta widgets carregados/atualizados via AJAX (adicionados ao DOM posteriormente) e/ou usados de forma aninhada (um widget dentro de outro widget). Se você não precisa agrupar vários widgets e controlá-los com um único `WidgetManager`, `AutoInitialize` é a abordagem recomendada.

## API de JavaScript

Um widget pode precisar ser renderizado e atualizado no lado do cliente. Nesses casos, você pode usar o `WidgetManager` do ABP e definir APIs para seus widgets.

### WidgetManager

`WidgetManager` é usado para inicializar e atualizar um ou mais widgets. Crie um novo `WidgetManager` como mostrado abaixo:

````js
$(function() {
    var myWidgetManager = new abp.WidgetManager('#MyDashboardWidgetsArea');    
})
````

`MyDashboardWidgetsArea` pode conter um ou mais widgets dentro.

> Usar o `WidgetManager` dentro do document.ready (como acima) é uma boa prática, pois suas funções usam o DOM e precisam que o DOM esteja pronto.

#### WidgetManager.init()

`init` simplesmente inicializa o `WidgetManager` e chama os métodos `init` dos widgets relacionados, se eles definirem (veja a seção API de JavaScript do Widget abaixo)

```js
myWidgetManager.init();
```

#### WidgetManager.refresh()

O método `refresh` atualiza todos os widgets relacionados a este `WidgetManager`:

````js
myWidgetManager.refresh();
````

#### Opções do WidgetManager

O WidgetManager tem algumas opções adicionais.

##### Formulário de Filtro

Se seus widgets exigirem parâmetros/filtros, você geralmente terá um formulário para filtrar os widgets. Nesses casos, você pode criar um formulário que tenha alguns elementos de formulário e uma área de dashboard com alguns widgets dentro. Exemplo:

````xml
<form method="get" id="MyDashboardFilterForm">
    ...elementos do formulário
</form>

<div id="MyDashboardWidgetsArea" data-widget-filter="#MyDashboardFilterForm">
   ...widgets
</div>
````

O atributo `data-widget-filter` relaciona o formulário com os widgets. Sempre que o formulário é enviado, todos os widgets são automaticamente atualizados com os campos do formulário como filtro.

Em vez do atributo `data-widget-filter`, você pode usar o parâmetro `filterForm` do construtor do `WidgetManager`. Exemplo:

````js
var myWidgetManager = new abp.WidgetManager({
    wrapper: '#MyDashboardWidgetsArea',
    filterForm: '#MyDashboardFilterForm'
});
````

##### Callback de Filtro

Você pode querer ter um controle melhor para fornecer filtros ao inicializar e atualizar os widgets. Nesse caso, você pode usar a opção `filterCallback`:

````js
var myWidgetManager = new abp.WidgetManager({
    wrapper: '#MyDashboardWidgetsArea',
    filterCallback: function() {
        return $('#MyDashboardFilterForm').serializeFormToObject();
    }
});
````

Este exemplo mostra a implementação padrão do `filterCallback`. Você pode retornar qualquer objeto JavaScript com campos. Exemplo:

````js
filterCallback: function() {
    return {
        'startDate': $('#StartDateInput').val(),
        'endDate': $('#EndDateInput').val()
    };
}
````

Os filtros retornados são passados para todos os widgets na `init` e `refresh`.

### API de JavaScript do Widget

Um widget pode definir uma API de JavaScript que é invocada pelo `WidgetManager` quando necessário. O exemplo de código abaixo pode ser usado para começar a definir uma API para um widget.

````js
(function () {
    abp.widgets.NewUserStatisticWidget = function ($wrapper) {

        var getFilters = function () {
            return {
                ...
            };
        }

        var refresh = function (filters) {
            ...
        };

        var init = function (filters) {
            ...
        };

        return {
            getFilters: getFilters,
            init: init,
            refresh: refresh
        };
    };
})();
````

`NewUserStatisticWidget` é o nome do widget aqui. Ele deve corresponder ao nome do widget definido no lado do servidor. Todas as funções são opcionais.

#### getFilters

Se o widget tiver filtros internos personalizados, esta função deve retornar o objeto de filtro. Exemplo:

````js
var getFilters = function() {
    return {
        frequency: $wrapper.find('.frequency-filter option:selected').val()
    };
}
````

Este método é usado pelo `WidgetManager` ao construir filtros.

#### init

Usado para inicializar o widget quando necessário. Ele tem um argumento de filtro que pode ser usado ao obter dados do servidor. O método `init` é usado quando a função `WidgetManager.init()` é chamada. Ele também é chamado se o seu widget exigir um recarregamento completo na atualização. Veja a opção `RefreshUrl` do widget.

#### refresh

Usado para atualizar o widget quando necessário. Ele tem um argumento de filtro que pode ser usado ao obter dados do servidor. O método `refresh` é usado sempre que a função `WidgetManager.refresh()` é chamada.

## Autorização

Alguns widgets podem precisar estar disponíveis apenas para usuários autenticados ou autorizados. Nesse caso, use as seguintes propriedades do atributo `Widget`:

* `RequiresAuthentication` (`bool`): Defina como verdadeiro para tornar este widget utilizável apenas para usuários autenticados (usuário que fez login no aplicativo).
* `RequiredPolicies` (`List<string>`): Uma lista de nomes de políticas para autorizar o usuário. Veja o documento de [autorização](../../Authorization.md) para mais informações sobre políticas.

Exemplo:

````csharp
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.UI.Widgets;

namespace DashboardDemo.Web.Pages.Components.MySimpleWidget
{
    [Widget(RequiredPolicies = new[] { "MyPolicyName" })]
    public class MySimpleWidgetViewComponent : AbpViewComponent
    {
        public IViewComponentResult Invoke()
        {
            //TODO: CONFIGURAÇÃO
            return View();
        }
    }
}
````

## WidgetOptions

Como alternativa ao atributo `Widget`, você pode usar o `AbpWidgetOptions` para configurar widgets:

```csharp
Configure<AbpWidgetOptions>(options =>
{
    options.Widgets.Add<MySimpleWidgetViewComponent>();
});
```

Escreva isso no método `ConfigureServices` do seu [módulo](../../Module-Development-Basics.md). Toda a configuração feita com o atributo `Widget` também é possível com o `AbpWidgetOptions`. Exemplo de configuração que adiciona um estilo para o widget:

````csharp
Configure<AbpWidgetOptions>(options =>
{
    options.Widgets
        .Add<MySimpleWidgetViewComponent>()
        .WithStyles("/Pages/Components/MySimpleWidget/Default.css");
});
````

> Dica: `AbpWidgetOptions` também pode ser usado para obter um widget existente e alterar sua configuração. Isso é especialmente útil se você quiser modificar a configuração de um widget dentro de um módulo usado pelo seu aplicativo. Use `options.Widgets.Find` para obter um `WidgetDefinition` existente.

## Veja Também

* [Projeto de exemplo (código-fonte)](https://github.com/abpframework/abp-samples/tree/master/DashboardDemo).