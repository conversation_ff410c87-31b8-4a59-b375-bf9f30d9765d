# ASP.NET Core MVC / Razor Pages UI: Menu de Navegação no SISPREC

O SISPREC utiliza a infraestrutura de menus do ABP Framework para organizar sua navegação. Este guia explica como os menus são implementados e como adicionar novos itens.

## Estrutura de Menus no SISPREC

O SISPREC organiza seus menus em duas classes principais:

1. `SISPRECMenus.cs`: Define as constantes que identificam cada item de menu
2. `SISPRECMenuContributor.cs`: Implementa a lógica de construção dos menus

### Definindo Identificadores de Menu

No SISPREC, todos os identificadores de menu são definidos como constantes na classe `SISPRECMenus`:

```csharp
public static class SISPRECMenus
{
    private const string Prefix = "SISPREC";
    public const string Requisitorios = Prefix + ".Requisitorios";
    public const string TransmissaoCJF = Prefix + ".TransmissaoCJF";
    // ...
}
```

Esta abordagem garante:
- Consistência nos nomes dos menus
- Facilidade de manutenção
- Evita erros de digitação
- Permite refatoração segura

### Implementando o Menu Contributor

O `SISPRECMenuContributor` implementa `IMenuContributor` e organiza os menus em métodos específicos:

```csharp
public class SISPRECMenuContributor : IMenuContributor
{
    public async Task ConfigureMenuAsync(MenuConfigurationContext context)
    {
        if (context.Menu.Name == StandardMenus.Main)
        {
            await ConfigureMainMenuAsync(context);
        }
    }

    private Task ConfigureMainMenuAsync(MenuConfigurationContext context)
    {
        // Verifica autenticação
        if (currentUser.IsAuthenticated)
        {
            ConfigurarMenuRequisitorios(context);
            ConfigurarMenuTransmissaoCjf(context);
            ConfigurarMenuConfiguracoesAplicacao(context);
        }
        return Task.CompletedTask;
    }
}
```

## Exemplos Práticos do SISPREC

### 1. Adicionando Menu Principal com Submenu

```csharp
context.Menu.Items.Insert(0,
    new ApplicationMenuItem(
        SISPRECMenus.Requisitorios,
        "Requisitórios",
        null,
        icon: "fa-solid fa-file-contract",
        order: 1,
        requiredPermissionName: SISPRECPermissoes.RequisicaoProtocolo.Visualizar
    ));

// Adiciona submenu
var requisitoriosMenu = context.Menu.GetMenuItem(SISPRECMenus.Requisitorios);
requisitoriosMenu.AddItem(
    new ApplicationMenuItem(
        SISPRECMenus.Requisicoes,
        "Requisições",
        "/ViewRequisicoes",
        icon: "fa-solid fa-file-alt"
    ));
```

### 2. Organizando Menus em Grupos

O SISPREC organiza menus relacionados em grupos lógicos. Por exemplo, o menu de Cadastros Básicos:

```csharp
var cadastrosMenu = new ApplicationMenuItem(
    SISPRECMenus.CadastrosBasicosCJF,
    "Cadastros",
    null,
    icon: "fa-solid fa-table-list"
);

// Adiciona itens ordenados alfabeticamente
var itensMenu = new List<ApplicationMenuItem>
{
    new("Menu.Assuntos", "Assunto", "/Assuntos"),
    new("Menu.Bancos", "Banco", "/Bancos"),
    // ...
}.OrderBy(x => x.DisplayName);

foreach (var item in itensMenu)
{
    cadastrosMenu.AddItem(item);
}
```

### 3. Implementando Autorização

O SISPREC utiliza permissões para controlar o acesso aos menus:

```csharp
new ApplicationMenuItem(
    SISPRECMenus.Requisitorios,
    "Requisitórios",
    null,
    icon: "fa-solid fa-file-contract",
    requiredPermissionName: SISPRECPermissoes.RequisicaoProtocolo.Visualizar
)
```

## Boas Práticas no SISPREC

1. **Organização de Constantes**
   - Use prefixos para agrupar menus relacionados
   - Mantenha as constantes em uma classe estática
   - Use nomes descritivos e consistentes

2. **Estrutura de Menus**
   - Agrupe itens relacionados
   - Ordene itens alfabeticamente quando apropriado
   - Use ícones para melhor usabilidade
   - Mantenha a profundidade do menu controlada

3. **Autorização**
   - Sempre defina permissões para menus sensíveis
   - Use constantes do `SISPRECPermissoes` para as permissões
   - Verifique autenticação antes de construir menus

4. **Manutenção**
   - Organize o código em métodos específicos por área
   - Use comentários para documentar seções complexas
   - Mantenha a consistência visual com os ícones

## Propriedades do ApplicationMenuItem

- `name`: Identificador único do menu (use constantes do `SISPRECMenus`)
- `displayName`: Nome exibido na interface
- `url`: URL da página (null para menus com submenus)
- `icon`: Classe do ícone (FontAwesome)
- `order`: Ordem de exibição
- `requiredPermissionName`: Permissão necessária para visualização

## Configuração no Módulo

Para registrar o menu contributor:

```csharp
Configure<AbpNavigationOptions>(options =>
{
    options.MenuContributors.Add(new SISPRECMenuContributor());
});
```

## Considerações Finais

- Mantenha a consistência com o padrão existente
- Use as constantes definidas em `SISPRECMenus`
- Implemente autorização adequada
- Organize os menus de forma lógica e intuitiva
- Mantenha a documentação atualizada ao adicionar novos menus
