# ASP.NET Core MVC / Razor Pages: Testando

> Você pode seguir a [documentação de Testes de Integração do ASP.NET Core](https://docs.microsoft.com/en-us/aspnet/core/test/integration-tests) para aprender detalhes dos testes de integração do ASP.NET Core. Este documento explica a infraestrutura de teste adicional fornecida pelo ABP.

## O Template de Inicialização da Aplicação

O Template de Inicialização da Aplicação contém o projeto `.Web` que contém as views/páginas/componentes da UI da aplicação e um projeto `.Web.Tests` para testá-los.

![aspnetcore-web-tests-in-solution](/ABP-Docs/images/aspnetcore-web-tests-in-solution.png)

## Testando as Razor Pages

Assuma que você criou uma Razor Page, nomeada `Issues.cshtml` com o seguinte conteúdo:

**Issues.cshtml.cs**

````csharp
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc.RazorPages;
using MyProject.Issues;

namespace MyProject.Web.Pages
{
    public class IssuesModel : PageModel
    {
        public List<IssueDto> Issues { get; set; }

        private readonly IIssueAppService _issueAppService;

        public IssuesModel(IIssueAppService issueAppService)
        {
            _issueAppService = issueAppService;
        }

        public async Task OnGetAsync()
        {
            Issues = await _issueAppService.GetListAsync();
        }
    }
}
````

**Issues.cshtml**

````html
@page
@model MyProject.Web.Pages.IssuesModel
<h2>Issue List</h2>
<table id="IssueTable" class="table">
    <thead>
        <tr>
            <th>Issue</th>
            <th>Closed?</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var issue in Model.Issues)
        {
            <tr>
                <td>@issue.Title</td>
                <td>
                    @if (issue.IsClosed)
                    {
                        <span>Closed</span>
                    }
                    else
                    {
                        <span>Open</span>
                    }
                </td>
            </tr>
        }
    </tbody>
</table>
````

Essa página simplesmente cria uma tabela com as issues:

![issue-list](/ABP-Docs/images/issue-list.png)

Você pode escrever uma classe de teste dentro do projeto `.Web.Tests` como no exemplo abaixo:

````csharp
using System.Threading.Tasks;
using HtmlAgilityPack;
using Shouldly;
using Xunit;

namespace MyProject.Pages
{
    public class Issues_Tests : MyProjectWebTestBase
    {
        [Fact]
        public async Task Should_Get_Table_Of_Issues()
        {
            // Act

            var response = await GetResponseAsStringAsync("/Issues");

            //Assert

            var htmlDocument = new HtmlDocument();
            htmlDocument.LoadHtml(response);

            var tableElement = htmlDocument.GetElementbyId("IssueTable");
            tableElement.ShouldNotBeNull();

            var trNodes = tableElement.SelectNodes("//tbody/tr");
            trNodes.Count.ShouldBeGreaterThan(0);
        }
    }
}
````

`GetResponseAsStringAsync` é um método atalho que vem da classe base que realiza uma requisição HTTP GET, verifica se o Status HTTP resultante é `200` e retorna a resposta como uma `string`.

> Você pode usar o objeto `Client` base (do tipo `HttpClient`) para realizar qualquer tipo de requisição ao servidor e ler a resposta você mesmo. `GetResponseAsStringAsync` é apenas um método atalho.

Este exemplo usa a biblioteca [HtmlAgilityPack](https://html-agility-pack.net/) para analisar o HTML recebido e testar se ele contém a tabela de issues.

> Este exemplo assume que existem algumas issues iniciais no banco de dados. Veja a seção *The Data Seed* do [documento de Testes](../../../testing/overall.md) para aprender como alimentar dados de teste, para que seus testes possam assumir que alguns dados iniciais estão disponíveis no banco de dados.

## Testando os Controllers

Testar um controller não é diferente. Apenas faça uma requisição para o servidor com a URL apropriada, obtenha a resposta e faça suas asserções.

### View Result

Se o controller retorna uma View, você pode usar um código similar para testar o HTML retornado. Veja o exemplo das Razor Pages acima.

### Object Result

Se o controller retorna um resultado do tipo object, você pode usar o método base `GetResponseAsObjectAsync`.

Assuma que você tem um controller como definido abaixo:

````csharp
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MyProject.Issues;
using Volo.Abp.AspNetCore.Mvc;

namespace MyProject.Web.Controllers
{
    [Route("api/issues")]
    public class IssueController : AbpController
    {
        private readonly IIssueAppService _issueAppService;

        public IssueController(IIssueAppService issueAppService)
        {
            _issueAppService = issueAppService;
        }

        [HttpGet]
        public async Task<List<IssueDto>> GetAsync()
        {
            return await _issueAppService.GetListAsync();
        }
    }
}
````

Você pode escrever um código de teste para executar a API e obter o resultado:

````csharp
using System.Collections.Generic;
using System.Threading.Tasks;
using MyProject.Issues;
using Shouldly;
using Xunit;

namespace MyProject.Pages
{
    public class Issues_Tests : MyProjectWebTestBase
    {
        [Fact]
        public async Task Should_Get_Issues_From_Api()
        {
            var issues = await GetResponseAsObjectAsync<List<IssueDto>>("/api/issues");
            
            issues.ShouldNotBeNull();
            issues.Count.ShouldBeGreaterThan(0);
        }
    }
}
````

## Testando o Código JavaScript

O ABP não fornece nenhuma infraestrutura para testar seu código JavaScript. Você pode usar qualquer framework de teste e ferramentas para testar seu código JavaScript.

## A Infraestrutura de Teste

O pacote [Volo.Abp.AspNetCore.TestBase](https://www.nuget.org/packages/Volo.Abp.AspNetCore.TestBase) fornece a infraestrutura de teste que é integrada ao ABP e ASP.NET Core.

> O pacote Volo.Abp.AspNetCore.TestBase já está instalado no projeto `.Web.Tests`.

Este pacote fornece o `AbpWebApplicationFactoryIntegratedTest` como a classe base fundamental para derivar as classes de teste. É herdada da classe [WebApplicationFactory](https://learn.microsoft.com/en-us/aspnet/core/test/integration-tests) fornecida pelo ASP.NET Core.

A classe base `MyProjectWebTestBase` usada acima herda de `AbpWebApplicationFactoryIntegratedTest`, então indiretamente herdamos `AbpWebApplicationFactoryIntegratedTest`.

Veja Também
* [Testes de integração em ASP.NET Core](https://learn.microsoft.com/en-us/aspnet/core/test/integration-tests)
* [Visão Geral / Testes do Lado do Servidor](../../../testing/overall.md)
