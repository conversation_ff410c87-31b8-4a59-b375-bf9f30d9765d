[[_TOC_]]

# Testes de Interface Web

Este guia demonstra como implementar testes automatizados para a camada web do SISPREC, focando nos testes de Razor Pages.
Os testes de UI verificam o se as páginas são carregadas corretamente.
Atualmente não há infraestrutura para testar submissão de formulários.

## Testes das telas de Livro

### Teste da Página de Índice
Este parágrafo destaca algumas questões-chave, como o status HTTP esperado (200 OK), a análise do conteúdo da página e a garantia de que o carregamento e a rota estejam funcionando corretamente, antes de prosseguir com as validações de elementos HTML.

```csharp
using Shouldly;

namespace TRF3.SISPREC.Pages.Tests.Livros;

/// <summary>
/// Classe de testes para a página principal de listagem de livros.
/// Demonstra como testar páginas Razor que não dependem de dados específicos.
/// Namespace: TRF3.SISPREC.Pages.Tests.Livros
/// Diretório: .\test\TRF3.SISPREC.Web.Tests\Pages\Livros
/// </summary>
/// <remarks>
/// Esta classe herda de SISPRECWebTestBase que fornece:
/// - Ambiente web simulado para testes
/// - Métodos auxiliares para requisições HTTP
/// - Configuração do ABP Framework para testes
/// 
/// Características dos testes:
/// - Verifica o carregamento básico da página
/// - Valida o status HTTP da resposta
/// - Confirma a presença do conteúdo HTML
/// 
/// Obs: Testes mais específicos como validação do DataTable
/// são feitos via testes de JavaScript, pois a tabela é
/// carregada dinamicamente pelo cliente.
/// </remarks>
public class Index_Tests : SISPRECWebTestBase
{
    /// <summary>
    /// Testa se a página principal de livros carrega corretamente.
    /// Verifica:
    /// 1. Se a requisição retorna status 200 (OK)
    /// 2. Se o HTML da página está presente
    /// 
    /// Este é um teste básico de smoke test que garante
    /// que a rota está configurada e a página está acessível.
    /// </summary>
    [Fact]
    public async Task Index_Page_Test()
    {
        // Arrange
        // Define a URL da página principal de livros
        string url = "/Livros";

        // Act
        // Faz duas requisições: uma para o response e outra para o conteúdo
        var response = await GetResponseAsync(url);
        var responseString = await GetResponseAsStringAsync(url);

        //Assert
        // Verifica se as respostas não são nulas
        response.ShouldNotBeNull();
        responseString.ShouldNotBeNull();
        // Verifica se o status da resposta é 200 OK
        response.StatusCode.ShouldBe(System.Net.HttpStatusCode.OK);
    }
}

```

### Teste do Modal de Criação
Este parágrafo ressalta o quão importante é verificar a estrutura do modal, incluindo rodapé, botões de ação (Cancelar e Salvar) e status HTTP. Também chama a atenção para o uso do HtmlAgilityPack, garantindo que o HTML esteja adequado para a funcionalidade desejada.

```csharp
using HtmlAgilityPack;
using Shouldly;

namespace TRF3.SISPREC.Pages.Tests.Livros;

/// <summary>
/// Classe de testes para a modal de criação de livros.
/// Demonstra como testar páginas Razor e elementos HTML usando HtmlAgilityPack.
/// Namespace: TRF3.SISPREC.Pages.Tests.Livros
/// Diretório: .\test\TRF3.SISPREC.Web.Tests\Pages\Livros
/// </summary>
/// <remarks>
/// Esta classe herda de SISPRECWebTestBase que fornece:
/// - Ambiente web simulado para testes
/// - Métodos auxiliares para requisições HTTP
/// - Configuração do ABP Framework para testes
/// 
/// Características dos testes:
/// - Verificam a estrutura HTML da modal
/// - Validam elementos específicos como botões
/// - Confirmam o status HTTP das respostas
/// - Usam HtmlAgilityPack para parsing do HTML
/// </remarks>
public class CreateModalTests : SISPRECWebTestBase
{
    /// <summary>
    /// Testa se a modal de criação é carregada corretamente.
    /// Verifica:
    /// 1. Se a requisição retorna status 200 (OK)
    /// 2. Se o HTML da modal está presente
    /// 3. Se os botões de ação existem com os textos corretos
    /// </summary>
    [Fact]
    public async Task Create_Modal_Teste()
    {
        // Arrange
        // Define a URL da modal de criação
        var url = "/Livros/CreateModal";

        // Act
        // Faz duas requisições: uma para o conteúdo em string e outra para o response completo
        var responseAsString = await GetResponseAsStringAsync(url);
        var response = await GetResponseAsync(url);

        // Assert
        // Verifica se a resposta não é nula
        responseAsString.ShouldNotBeNull();
        var htmlDocument = new HtmlDocument();
        // Verifica se o status da resposta é 200 OK
        response.StatusCode.ShouldBe(System.Net.HttpStatusCode.OK);

        // Carrega o HTML para análise
        htmlDocument.LoadHtml(responseAsString);

        // Verifica existência do rodapé do modal usando XPath
        var footer = htmlDocument.DocumentNode.SelectSingleNode("//*[contains(@class, 'modal-footer')]");
        footer.ShouldNotBeNull();

        // Verifica se existem exatamente 2 botões (Cancelar e Salvar)
        var buttons = footer.SelectNodes(".//button");
        buttons.Count.ShouldBe(2);
        // Verifica os textos dos botões
        buttons[0].InnerText.Trim().ShouldBe("Cancelar");
        buttons[1].InnerText.Trim().ShouldBe("Salvar");
    }
}

```

### Teste do Modal de Edição
Este parágrafo destaca a necessidade de preparar dados usando Bogus e persistência com Unit of Work, validando campos e valores dentro do modal. Também enfatiza a verificação dos botões de ação e o correto preenchimento dos inputs, garantindo a consistência no front-end e no banco.

```csharp
using HtmlAgilityPack;
using Shouldly;
using TRF3.SISPREC.Autores;
using TRF3.SISPREC.Livros;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.Pages.Tests.Livros;

/// <summary>
/// Classe de testes para a modal de edição de livros.
/// Demonstra como testar páginas Razor que dependem de dados do banco
/// e validar o preenchimento correto dos campos do formulário.
/// Namespace: TRF3.SISPREC.Pages.Tests.Livros
/// Diretório: .\test\TRF3.SISPREC.Web.Tests\Pages\Livros
/// </summary>
/// <remarks>
/// Esta classe herda de SISPRECWebTestBase que fornece:
/// - Ambiente web simulado para testes
/// - Métodos auxiliares para requisições HTTP
/// - Configuração do ABP Framework para testes
/// - Acesso aos serviços via injeção de dependência
/// 
/// Características dos testes:
/// - Usa Bogus para gerar dados de teste
/// - Trabalha com Unit of Work para persistência
/// - Cria dados relacionados (Autor e Livro)
/// - Verifica respostas HTTP e elementos HTML específicos
/// </remarks>
public class EditModalTests : SISPRECWebTestBase
{
    /// <summary>
    /// Livro de teste criado no construtor e usado nos métodos de teste
    /// </summary>
    private readonly Livro livroObj;

    /// <summary>
    /// Construtor que prepara o ambiente de teste:
    /// 1. Obtém os gerenciadores necessários via injeção de dependência
    /// 2. Configura os fakers para gerar dados consistentes
    /// 3. Cria um autor e um livro no banco de teste
    /// </summary>
    public EditModalTests()
    {
        var livroManager = GetRequiredService<ILivroManager>();
        var autorManager = GetRequiredService<IAutorManager>();

        // Configurando o faker para autores com dados válidos
        var autorFaker = new Bogus.Faker<Autor>()
                    .RuleFor(a => a.Nome, a => a.Random.Hash(10))
                    .RuleFor(a => a.Sobrenome, a => a.Random.Hash(10))
                    .RuleFor(a => a.MunicipioId, SISPRECTestConsts.MunicipioId);

        // Configurando o faker para livros com dados válidos
        livroObj = new Bogus.Faker<Livro>()
            .RuleFor(p => p.Titulo, p => p.Random.Hash())
            .RuleFor(p => p.Categoria, p => ECategoriaLivro.INFANTIL)
            .RuleFor(p => p.DataPublicacao, p => p.Date.Past())
            .RuleFor(p => p.Preco, p => p.Random.Decimal(1, 999.99M))
            .RuleFor(p => p.Descricao, p => p.Random.Hash())
            .RuleFor(p => p.Quantidade, p => p.Random.Int(1, 100))
            .RuleFor(p => p.Disponivel, p => true)
            .Generate();

        /**
         * Geralmente não é necessário usar UnitOfWork, porém como são necessários dois inserts, 
         * é necessário garantir a instância do DbContext esteja disponível para ambos.
         */
        using (var uow = GetRequiredService<IUnitOfWorkManager>().Begin())
        {
            // Inserindo o autor e obtendo seu ID
            var autorId = autorManager.InserirAsync(autorFaker.Generate(), true).Result.AutorId;
            // Inserindo o livro e vinculando ao autor
            livroManager.InserirAsync(livroObj, new[] { autorId }, true).Wait();
            uow.CompleteAsync().Wait();
        }
    }

    /// <summary>
    /// Testa se a modal de edição carrega corretamente com os dados do livro.
    /// Verifica:
    /// 1. Se a requisição retorna status 200 (OK)
    /// 2. Se o ID do livro está no campo hidden
    /// 3. Se os campos estão preenchidos com os valores corretos
    /// 4. Se os botões de ação existem com os textos corretos
    /// </summary>
    [Fact]
    public async Task Edit_Modal_Teste()
    {
        // Arrange
        // Monta a URL com o ID do livro criado no construtor
        var url = "/Livros/EditModal?id=" + livroObj.LivroId;

        // Act
        // Faz duas requisições: uma para o conteúdo em string e outra para o response completo
        var responseAsString = await GetResponseAsStringAsync(url);
        var response = await GetResponseAsync(url);

        // Assert
        // Verifica se a resposta não é nula
        responseAsString.ShouldNotBeNull();
        var htmlDocument = new HtmlDocument();
        // Verifica se o status da resposta é 200 OK
        response.StatusCode.ShouldBe(System.Net.HttpStatusCode.OK);

        htmlDocument.LoadHtml(responseAsString);

        // Verifica se o ID do livro está no campo hidden
        var hiddenIdInput = htmlDocument.GetElementbyId("Id");
        hiddenIdInput.ShouldNotBeNull();

        // Verifica se o título está preenchido corretamente
        var TituloInput = htmlDocument.GetElementbyId("ViewModel_Titulo");
        TituloInput.ShouldNotBeNull();
        TituloInput.Attributes["value"].Value.ShouldBe(livroObj.Titulo);

        // Verifica existência do rodapé do modal
        var footer = htmlDocument.DocumentNode.SelectSingleNode("//*[contains(@class, 'modal-footer')]");
        footer.ShouldNotBeNull();

        // Verifica se existem exatamente 2 botões (Cancelar e Salvar)
        var buttons = footer.SelectNodes(".//button");
        buttons.Count.ShouldBe(2);
        buttons[0].InnerText.Trim().ShouldBe("Cancelar");
        buttons[1].InnerText.Trim().ShouldBe("Salvar");
    }
}

```

### Teste do Modal de Detalhes
Este parágrafo enfatiza a checagem da resposta HTTP e do HTML para verificar se as informações do livro são exibidas corretamente no modal. Isso confirma a integridade do teste, assegurando que os dados carregados pelo backend estejam corretos.

```csharp
using HtmlAgilityPack;
using Shouldly;
using TRF3.SISPREC.Autores;
using TRF3.SISPREC.Livros;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.Pages.Tests.Livros;

/// <summary>
/// Classe de testes para a modal de detalhes de livros.
/// Demonstra como testar páginas Razor que dependem de dados do banco.
/// Namespace: TRF3.SISPREC.Pages.Tests.Livros
/// Diretório: .\test\TRF3.SISPREC.Web.Tests\Pages\Livros
/// </summary>
/// <remarks>
/// Esta classe herda de SISPRECWebTestBase que fornece:
/// - Ambiente web simulado para testes
/// - Métodos auxiliares para requisições HTTP
/// - Configuração do ABP Framework para testes
/// - Acesso aos serviços via injeção de dependência
/// 
/// Características dos testes:
/// - Usa Bogus para gerar dados de teste
/// - Trabalha com Unit of Work para persistência
/// - Cria dados relacionados (Autor e Livro)
/// - Verifica respostas HTTP e conteúdo HTML
/// </remarks>
public class DetalheModalTests : SISPRECWebTestBase
{
    /// <summary>
    /// Livro de teste criado no construtor e usado nos métodos de teste
    /// </summary>
    private readonly Livro livroObj;

    /// <summary>
    /// Construtor que prepara o ambiente de teste:
    /// 1. Obtém os gerenciadores necessários via injeção de dependência
    /// 2. Configura os fakers para gerar dados consistentes
    /// 3. Cria um autor e um livro no banco de teste
    /// </summary>
    public DetalheModalTests()
    {
        var livroManager = GetRequiredService<ILivroManager>();
        var autorManager = GetRequiredService<IAutorManager>();

        // Configurando o faker para autores com dados válidos
        var autorFaker = new Bogus.Faker<Autor>()
                    .RuleFor(a => a.Nome, a => a.Random.Hash(10))
                    .RuleFor(a => a.Sobrenome, a => a.Random.Hash(10))
                    .RuleFor(a => a.MunicipioId, SISPRECTestConsts.MunicipioId);

        // Configurando o faker para livros com dados válidos
        livroObj = new Bogus.Faker<Livro>()
            .RuleFor(p => p.Titulo, p => p.Random.Hash())
            .RuleFor(p => p.Categoria, p => ECategoriaLivro.INFANTIL)
            .RuleFor(p => p.DataPublicacao, p => p.Date.Past())
            .RuleFor(p => p.Preco, p => p.Random.Decimal(1, 999.99M))
            .RuleFor(p => p.Descricao, p => p.Random.Hash())
            .RuleFor(p => p.Quantidade, p => p.Random.Int(1, 100))
            .RuleFor(p => p.Disponivel, p => true)
            .Generate();

        // Cria os registros dentro de uma transação
        using (var uow = GetRequiredService<IUnitOfWorkManager>().Begin())
        {
            // Inserindo o autor e obtendo seu ID
            var autorId = autorManager.InserirAsync(autorFaker.Generate(), true).Result.AutorId;
            // Inserindo o livro e vinculando ao autor
            livroManager.InserirAsync(livroObj, new[] { autorId }, true).Wait();
            uow.CompleteAsync().Wait();
        }
    }

    /// <summary>
    /// Testa se a modal de detalhes carrega corretamente os dados do livro.
    /// Verifica:
    /// 1. Se a requisição retorna status 200 (OK)
    /// 2. Se o HTML da modal está presente
    /// 3. Se os dados do livro são exibidos corretamente
    /// </summary>
    [Fact]
    public async Task Detalhe_Modal_Teste()
    {
        // Arrange
        // Monta a URL com o ID do livro criado no construtor
        string url = "/Livros/DetalheModal?id=" + livroObj.LivroId;

        // Act
        // Faz duas requisições: uma para o response e outra para o conteúdo
        var response = await GetResponseAsync(url);
        var responseString = await GetResponseAsStringAsync(url);

        //Assert
        // Verifica se as respostas não são nulas
        response.ShouldNotBeNull();
        responseString.ShouldNotBeNull();

        // Verifica se o status da resposta é 200 OK
        response.StatusCode.ShouldBe(System.Net.HttpStatusCode.OK);

        var htmlDocument = new HtmlDocument();
        htmlDocument.LoadHtml(responseString);

        // Verifica se os elementos principais existem
        var tituloElement = htmlDocument.DocumentNode.SelectSingleNode("//input[@id='ViewModel_Titulo']");
        tituloElement.ShouldNotBeNull();
        tituloElement.Attributes["value"].Value.ShouldBeEquivalentTo(livroObj.Titulo);
    }
}

```

## Como Usar Dados Pré-carregados nos Testes Web

### Benefícios de Usar a Carga Básica
- Evita criação desnecessária de registros
- Garante consistência entre testes
- Reduz tempo de execução dos testes
- Previne problemas com chaves duplicadas

### Exemplo Prático de Uso

```csharp
// Teste usando dados pré-carregados
public class RequisicaoModalTests : SISPRECWebTestBase
{
    [Fact]
    public async Task Detalhe_Modal_Teste()
    {
        // Arrange
        // Usa constante da classe SISPRECTestConsts em vez de valor literal
        var url = $"/Requisicao/DetalheModal?id={SISPRECTestConsts.NumProtocRequisicao}";

        // Act
        var response = await GetResponseAsync(url);
        var responseString = await GetResponseAsStringAsync(url);

        // Assert
        response.ShouldNotBeNull();
        responseString.ShouldNotBeNull();
        response.StatusCode.ShouldBe(System.Net.HttpStatusCode.OK);

        var htmlDocument = new HtmlDocument();
        htmlDocument.LoadHtml(responseString);

        // Valida dados conhecidos da requisição usando a constante
        var protocoloElement = htmlDocument.DocumentNode
            .SelectSingleNode("//input[@id='ViewModel_NumProtocRequisicao']");
        protocoloElement.ShouldNotBeNull();
        protocoloElement.Attributes["value"].Value.ShouldBe(SISPRECTestConsts.NumProtocRequisicao);
    }
}
```

### Diretrizes para Uso da Carga

1. **Identificação de Dados Disponíveis**
   - Consulte a classe `SISPRECTestConsts` para IDs disponíveis
   - Verifique o arquivo `CARGA_BASICA_SQLITE.sql` para detalhes dos registros

2. **Escolha do Tipo de Teste**
   - Use dados da carga para testes de leitura/consulta
   - Crie registros novos para testes de inserção/alteração
   - Para testes de exclusão, prefira criar registros específicos

3. **Manutenção dos Testes**
   - Se precisar alterar um registro da carga, atualize o arquivo SQL
   - Documente alterações nos dados da carga
   - Evite depender de dados calculados ou gerados

4. **Exemplo de Constantes Disponíveis**
   ```csharp
   // Em SISPRECTestConsts
   public const string NumProtocRequisicao = "20240086838";
   public const int MunicipioId = 2;
   // Outros IDs conforme necessário
   ```

### Tabelas com Dados Pré-carregados Úteis para Testes Web

1. **Requisições e Protocolos**
   - `REQ_REQUISICAO_PROTOCOLO`: Protocolo "20240086838"
   - `REQ_REQUISICAO_PARTE`: IDs 1, 2, 3
   - `REQ_REQUISICAO_PROPOSTA`: Proposta 3

2. **Dados de Apoio**
   - `TRF_MUNICIPIO`: Vários municípios (IDs 2 a 107)
   - `TRF_UF`: Todas as UFs do Brasil
   - `CJF_Unidade`: Unidade 1050
   - `CJF_Unidade_Judici`: ID 16312

3. **Situações e Tipos**
   - `REQ_SITUACAO_REQUISICAO_PROTOCOLO`: IDs 1 a 20
   - `CJF_Valor_Tipo`: IDs 1 a 28
   - `REQ_Tipo_Procedimento`: Tipos 'PRC' e 'RPV'


## Boas Práticas para Testes Web

1. **Preparação de Dados**
   - Sempre use `Wait()` e `autoSave: true` ao inserir registros no construtor para garantir que:
     - os dados estejam disponíveis antes dos testes
     - se acontecer erro na inserção, seja exibido no resultado do teste
   - Exemplo:
     ```csharp
     _repository.InsertAsync(autorObj, autoSave: true).Wait(); // Garante sincronização
     ```

2. **Validações Essenciais**
   - Verifique pelo menos um campo preenchido para garantir que os dados foram carregados
   - Exemplo de validação mínima:
     ```csharp
        var nomeInput = htmlDocument.DocumentNode.SelectSingleNode("//input[@id='ViewModel_Nome']");
        nomeInput.ShouldNotBeNull();
        nomeInput.Attributes["value"].Value.ShouldContain(autorObj.Nome);
     ```
   - **Importante**: use SelectSingleNode buscando pelo input com o ID específico, como no exemplo acima.

3. **Rastreabilidade de Dados**
   - Use o mesmo objeto inserido no construtor para fazer as consultas
   - Mantenha referência ao objeto de teste como campo privado da classe

4. **Manutenibilidade**
   - Organize testes por funcionalidade
   - Use constantes para dados comuns
   - Documente cenários complexos
