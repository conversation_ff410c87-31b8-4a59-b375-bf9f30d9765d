[[_TOC_]]
- (Em contrução)

O uso de views deverá ser feito com critério, isto é, quando for evidentemente necessário e o uso de LINQ queries no respositórios não for viável.
- Isto se deve ao fato de que as mudanças na estrutura do banco precisam passar pela área responsável, que incorre em um passo a mais para a entrega da funcionalidade, podendo haver gargalos.

Para mapear uma View, crie uma migration a partir de um SQL puro.
- Será necessário criar uma configuration usando o método ToView()
- O EFCore existe também, nessa configuration, que seja definida uma chave primária para a view.