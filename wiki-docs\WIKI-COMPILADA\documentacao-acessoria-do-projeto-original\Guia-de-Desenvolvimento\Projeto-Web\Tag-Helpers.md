# ABP Tag Helpers

O ABP define um conjunto de **componentes tag helper** para simplificar o desenvolvimento da interface do usuário para aplicações ASP.NET Core (MVC / Razor Pages).

## Wrappers de Componentes Bootstrap

A maioria dos tag helpers são wrappers do [Bootstrap](https://getbootstrap.com/) (v5+). Codificar bootstrap não é tão fácil, não é tão type-safe e contém muitas tags HTML repetitivas. ABP Tag Helpers torna isso **mais fácil** e **type safe**.

Não temos como objetivo fazer o wrapper de 100% dos componentes do bootstrap. Escrever código **nativo no estilo bootstrap** ainda é possível (na verdade, tag helpers geram código bootstrap nativo no final), mas sugerimos usar os tag helpers sempre que possível.

O ABP também adiciona alguns **recursos úteis** aos componentes bootstrap padrão.

Aqui está a lista de componentes que são encapsulados pelo ABP:

*   [Alerts](/Guia-de-Desenvolvimento/Projeto-Web/Tag-Helpers/Alerts.md)
*   [Badges](/Guia-de-Desenvolvimento/Projeto-Web/Tag-Helpers/Badges.md)
*   [Blockquote](/Guia-de-Desenvolvimento/Projeto-Web/Tag-Helpers/Blockquote.md)
*   [Borders](/Guia-de-Desenvolvimento/Projeto-Web/Tag-Helpers/Borders.md)
*   [Breadcrumb](/Guia-de-Desenvolvimento/Projeto-Web/Tag-Helpers/Breadcrumbs.md)
*   [Buttons](/Guia-de-Desenvolvimento/Projeto-Web/Tag-Helpers/Buttons.md)
*   [Cards](/Guia-de-Desenvolvimento/Projeto-Web/Tag-Helpers/Cards.md)
*   [Carousel](/Guia-de-Desenvolvimento/Projeto-Web/Tag-Helpers/Carousel.md)
*   [Collapse](/Guia-de-Desenvolvimento/Projeto-Web/Tag-Helpers/Collapse.md)
*   [Dropdowns](/Guia-de-Desenvolvimento/Projeto-Web/Tag-Helpers/Dropdowns.md)
*   [Figures](/Guia-de-Desenvolvimento/Projeto-Web/Tag-Helpers/Figure.md)
*   [Grids](/Guia-de-Desenvolvimento/Projeto-Web/Tag-Helpers/Grids.md)
*   [List Groups](/Guia-de-Desenvolvimento/Projeto-Web/Tag-Helpers/List-Groups.md)
*   [Modals](/Guia-de-Desenvolvimento/Projeto-Web/Tag-Helpers/Modals.md)
*   [Navigation](/Guia-de-Desenvolvimento/Projeto-Web/Tag-Helpers/Navs.md)
*   [Paginator](/Guia-de-Desenvolvimento/Projeto-Web/Tag-Helpers/Paginator.md)
*   [Popovers](/Guia-de-Desenvolvimento/Projeto-Web/Tag-Helpers/Popovers.md)
*   [Progress Bars](/Guia-de-Desenvolvimento/Projeto-Web/Tag-Helpers/Progress-Bars.md)
*   [Tables](/Guia-de-Desenvolvimento/Projeto-Web/Tag-Helpers/Tables.md)
*   [Tabs](/Guia-de-Desenvolvimento/Projeto-Web/Tag-Helpers/Tabs.md)
*   [Tooltips](/Guia-de-Desenvolvimento/Projeto-Web/Tag-Helpers/Tooltips.md)

> Até que todos os tag helpers sejam documentados, você pode visitar https://bootstrap-taghelpers.abp.io/ para vê-los com exemplos ao vivo.

## Elementos de Formulário

**Abp Tag Helpers** adiciona novos recursos aos **Tag Helpers de input & select Asp.Net Core MVC** padrões e os envolve com controles de formulário **Bootstrap**. Veja a [documentação de Elementos de Formulário](/Guia-de-Desenvolvimento/Projeto-Web/Tag-Helpers/Form-Elements.md).

## Formulários Dinâmicos

**Abp Tag helpers** oferecem uma maneira fácil de construir **formulários Bootstrap** completos. Veja a [documentação de Formulários Dinâmicos](/Guia-de-Desenvolvimento/Projeto-Web/Tag-Helpers/Dynamic-Forms.md).
