# Classes e Interfaces Base da Camada de Domínio - SISPREC

## Visão Geral

Este documento lista e descreve todas as classes e interfaces base disponíveis na camada de domínio do SISPREC, localizadas em `TRF3.SISPREC.Domain`.

## Classes Base de Entidades

### 1. BaseAtivaDesativaEntity

**Localização:** `TRF3.SISPREC.Domain\BaseAtivaDesativaEntity.cs`

**Descrição:** Classe base abstrata que herda de `Entity` (ABP Framework) e fornece funcionalidade básica de ativação/desativação.

**Propriedades:**
- `Ativo` (bool): Propriedade virtual que indica se a entidade está ativa (padrão: true)

**Uso:** Base para entidades que precisam de controle de ativação/desativação simples.

### 2. BaseEntidadeDominio

**Localização:** `TRF3.SISPREC.Domain\BaseEntidadeDominio.cs`

**Descrição:** Classe base abstrata que herda de `BaseAtivaDesativaEntity` e implementa `ISincronizavelCjfDataFim`. Fornece funcionalidade avançada de controle de ativação com data de fim de utilização e sincronização com CJF.

**Propriedades:**
- `DataUtilizacaoFim` (DateTime?): Data de fim de utilização da entidade
- `Ativo` (bool): Sobrescreve a propriedade base com lógica integrada à DataUtilizacaoFim
- `FoiSincronizadoCjf` (bool): Indica se a entidade foi sincronizada com CJF (padrão: true)

**Lógica de Negócio:**
- Quando `DataUtilizacaoFim` é preenchida e `Ativo` é true, define `Ativo` como false
- Quando `DataUtilizacaoFim` é nula e `Ativo` é false, define `DataUtilizacaoFim` como DateTime.Now
- Quando `Ativo` é true e `DataUtilizacaoFim` está preenchida, define `DataUtilizacaoFim` como null
- Quando `Ativo` é false e `DataUtilizacaoFim` está nula, define `DataUtilizacaoFim` como DateTime.Now

**Uso:** Base principal para entidades de domínio que precisam de sincronização com CJF e controle avançado de ativação.

## Interfaces de Entidades

### 1. IEntidadeSincronizavelCjf

**Localização:** `TRF3.SISPREC.Domain\InterfacesDominio\IEntidadeSincronizavelCjf.cs`

**Descrição:** Interface que herda de `IEntity` (ABP Framework) e define o contrato para entidades sincronizáveis com CJF.

**Propriedades:**
- `FoiSincronizadoCjf` (bool): Indica se a entidade foi sincronizada com CJF

### 2. ISincronizavelCjfDataFim

**Localização:** `TRF3.SISPREC.Domain\InterfacesDominio\ISincronizavelCjfDataFim.cs`

**Descrição:** Interface que herda de `IEntidadeSincronizavelCjf` e adiciona controle de data de fim de utilização.

**Propriedades:**
- `DataUtilizacaoFim` (DateTime?): Data de fim de utilização da entidade

## Classes Base de Serviços de Domínio

### 1. BaseDomainManager<TEntity>

**Localização:** `TRF3.SISPREC.Domain\BaseDomainManager.cs`

**Descrição:** Classe base para gerenciadores de domínio que herda de `DomainService` (ABP Framework) e implementa `IBaseDomainManager<TEntity>`.

**Constraint:** `TEntity : class, IEntity`

**Dependências:**
- `IRepository<TEntity> _repository`: Repositório injetado via construtor

**Métodos Principais:**
- `InserirAsync(TEntity entidade, bool autoSave = false, CancellationToken cancellationToken = default)`
- `InserirMuitosAsync(IEnumerable<TEntity> entidades, bool autoSave = false, CancellationToken cancellationToken = default)`
- `AlterarAsync(TEntity entidade, bool autoSave = false, CancellationToken cancellationToken = default)`
- `AlterarMuitosAsync(IEnumerable<TEntity> entidades, bool autoSave = false, CancellationToken cancellationToken = default)`
- `ExcluirAsync(TEntity entidade, bool autoSave = false, CancellationToken cancellationToken = default)`
- `ExcluirAsync(Expression<Func<TEntity, bool>> predicate, bool autoSave = false, CancellationToken cancellationToken = default)`
- `ExcluirMuitosAsync(IEnumerable<TEntity> entidades, bool autoSave = false, CancellationToken cancellationToken = default)`
- `ExcluirDiretoAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default)`

### 2. BaseSincronizavelManager<BaseEntidadeDominio>

**Localização:** `TRF3.SISPREC.Domain\BaseSincronizavelManager.cs`

**Descrição:** Classe base abstrata para gerenciadores de entidades sincronizáveis que herda de `BaseDomainManager<BaseEntidadeDominio>` e implementa `ISincronizavelManager<BaseEntidadeDominio>`.

**Constraint:** `BaseEntidadeDominio : class, IEntidadeSincronizavelCjf`

**Métodos Abstratos:**
- `RegistroFoiSincronizadoCjf(BaseEntidadeDominio entidade)`: Deve ser implementado pelas classes filhas para verificar se o registro foi sincronizado

## Interfaces de Serviços de Domínio

### 1. IBaseDomainManager<TEntity>

**Localização:** `TRF3.SISPREC.Domain\IBaseDomainManager.cs`

**Descrição:** Interface que herda de `IDomainService` (ABP Framework) e define o contrato para gerenciadores de domínio básicos.

**Constraint:** `TEntity : class, IEntity`

**Métodos:**
- `InserirAsync(TEntity entidade, bool autoSave = false, CancellationToken cancellationToken = default)`
- `InserirMuitosAsync(IEnumerable<TEntity> entidades, bool autoSave = false, CancellationToken cancellationToken = default)`
- `AlterarAsync(TEntity entidade, bool autoSave = false, CancellationToken cancellationToken = default)`
- `AlterarMuitosAsync(IEnumerable<TEntity> entidades, bool autoSave = false, CancellationToken cancellationToken = default)`
- `ExcluirAsync(TEntity entidade, bool autoSave = false, CancellationToken cancellationToken = default)`
- `ExcluirAsync(Expression<Func<TEntity, bool>> predicate, bool autoSave = false, CancellationToken cancellationToken = default)`
- `ExcluirMuitosAsync(IEnumerable<TEntity> entidades, bool autoSave = false, CancellationToken cancellationToken = default)`
- `ExcluirDiretoAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default)`

### 2. ISincronizavelManager<IEntidadeSincronizavelCjf>

**Localização:** `TRF3.SISPREC.Domain\ISincronizavelManager.cs`

**Descrição:** Interface que herda de `IBaseDomainManager<IEntidadeSincronizavelCjf>` e adiciona funcionalidade específica para entidades sincronizáveis.

**Constraint:** `IEntidadeSincronizavelCjf : class, IEntity`

**Métodos:**
- `RegistroFoiSincronizadoCjf(IEntidadeSincronizavelCjf entidade)`: Verifica se o registro foi sincronizado com CJF

### 3. IDbContext

**Localização:** `TRF3.SISPREC.Domain\IDbContext.cs`

**Descrição:** Interface para contexto de banco de dados customizado.

**Métodos:**
- `ExecuteNonQueryAsync(string sql)`: Executa comando SQL sem retorno
- `SaveChangesAsync()`: Salva mudanças no contexto

**Propriedades:**
- `Database` (DatabaseFacade): Acesso ao facade do banco de dados

## Exemplos de Implementação

### Managers Específicos que Implementam as Interfaces Base:

- `IFaseManager : ISincronizavelManager<Fase>`
- `IUnidadeJudicialManager : ISincronizavelManager<UnidadeJudicial>`
- `IPlanoManager : ISincronizavelManager<Plano>`
- `IAssuntoManager : ISincronizavelManager<Assunto>`
- `IUnidadeOrcamentariaManager : ISincronizavelManager<UnidadeOrcamentaria>`

## Diretrizes de Uso

### Para Entidades:

1. **Entidades Simples com Ativação/Desativação:** Herdar de `BaseAtivaDesativaEntity`
2. **Entidades de Domínio Completas:** Herdar de `BaseEntidadeDominio` (recomendado para a maioria dos casos)
3. **Entidades Customizadas:** Implementar `IEntidadeSincronizavelCjf` ou `ISincronizavelCjfDataFim` conforme necessário

### Para Serviços de Domínio:

1. **Managers Básicos:** Herdar de `BaseDomainManager<TEntity>`
2. **Managers para Entidades Sincronizáveis:** Herdar de `BaseSincronizavelManager<TEntity>`
3. **Interfaces Customizadas:** Implementar `IBaseDomainManager<TEntity>` ou `ISincronizavelManager<TEntity>`

### Padrões de Nomenclatura:

- **Entidades:** `[Nome]Entity` ou apenas `[Nome]`
- **Managers:** `[Nome]Manager`
- **Interfaces de Managers:** `I[Nome]Manager`
- **Interfaces de Entidades:** `I[Nome]` ou `I[Característica][Nome]`

## Considerações Arquiteturais

1. **Separação de Responsabilidades:** Entidades focam em estado e regras de negócio, Managers focam em operações e coordenação
2. **Injeção de Dependência:** Todos os managers são registrados automaticamente no container DI do ABP
3. **Transações:** Operações de repositório respeitam o padrão UnitOfWork do ABP
4. **Sincronização:** Entidades que herdam de `BaseEntidadeDominio` são preparadas para sincronização com sistemas externos (CJF)
5. **Auditoria:** Todas as entidades base são compatíveis com o sistema de auditoria do ABP

---

**Última Atualização:** $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
**Versão:** 1.0
**Responsável:** SISPREC BMAD Agent
