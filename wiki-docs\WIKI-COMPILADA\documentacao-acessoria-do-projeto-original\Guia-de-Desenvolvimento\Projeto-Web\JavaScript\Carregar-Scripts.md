# API JavaScript: Carregamento de Recursos/Scripts

## Tag Helper abp-script

O ABP Framework fornece o tag helper `abp-script` para carregar scripts de forma otimizada, com suporte a bundling e minification automáticos.

### Uso Básico

```html
<abp-script src="/scripts/meu-script.js"/>
```

Este método é recomendado para carregar scripts individuais. O ABP irá:
- Adicionar o script ao bundle automaticamente
- Gerar um nome de bundle baseado no caminho do arquivo
- Adicionar uma string de versão para cache-busting

### Uso em Bundles

Para agrupar múltiplos scripts em um bundle:

```html
<abp-script-bundle name="MeuBundle">
    <abp-script src="/scripts/script1.js" />
    <abp-script src="/scripts/script2.js" />
</abp-script-bundle>
```

### Uso de Bundles Globais

Você pode referenciar bundles globais definidos no projeto, como no exemplo abaixo:

```html
<abp-script-bundle name="@LeptonXLiteThemeBundles.Scripts.Global" />
```

Este exemplo:
- Referencia um bundle global de scripts definido no tema LeptonXLite
- Permite que outros módulos contribuam para o bundle
- Mantém a consistência dos scripts em toda a aplicação

### Uso em Modais

Ao usar scripts em modais, você pode incluí-los diretamente na página do modal:

```html
@page
@model MeuProjeto.Web.Pages.MeuModalModel
@{
    Layout = null;
}
<abp-modal>
    <abp-modal-header title="Meu Modal"></abp-modal-header>
    <abp-modal-body>
        <!-- Conteúdo do modal -->
    </abp-modal-body>
    <abp-modal-footer buttons="Close"></abp-modal-footer>
</abp-modal>

<abp-script src="/Pages/MeuModal.js"/>
```

O ABP irá:
- Carregar o script apenas quando o modal for aberto
- Remover o script quando o modal for fechado
- Garantir que o script seja executado apenas uma vez, mesmo se o modal for aberto múltiplas vezes

### Lazy Loading de Scripts

Para carregar scripts sob demanda:

```js
var meuModal = new abp.ModalManager({
    viewUrl: '/MinhaPagina/MeuModal',
    scriptUrl: '/Pages/MeuModal.js',
    modalClass: 'MeuModal'
});
```

O `scriptUrl` será carregado apenas quando o modal for aberto pela primeira vez.

## API ResourceLoader

`abp.ResourceLoader` é um serviço que pode carregar um arquivo JavaScript ou CSS sob demanda. Ele garante carregar o arquivo apenas uma vez, mesmo que você o solicite várias vezes.

### Carregando Arquivos de Script

A função `abp.ResourceLoader.loadScript(...)` **carrega** um arquivo JavaScript do servidor e **executa** ele.

**Exemplo: Carregar um arquivo JavaScript**

```js
abp.ResourceLoader.loadScript('/Pages/my-script.js');
```

#### Parâmetros

A função `loadScript` pode receber três parâmetros:

* `url` (obrigatório, `string`): A URL do arquivo de script a ser carregado.
* `loadCallback` (opcional, `function`): Uma função de callback que é chamada uma vez que o script é carregado e executado. Neste callback, você pode usar com segurança o código no arquivo de script. Este callback é chamado mesmo se o arquivo já foi carregado antes.
* `failCallback` (opcional, `function`): Uma função de callback que é chamada se o carregamento do script falhar.

**Exemplo: Fornecer o argumento `loadCallback`**

```js
abp.ResourceLoader.loadScript('/Pages/my-script.js', function() {
  console.log('carregado com sucesso :)');
});
```

### Carregando Arquivos de Estilo

A função `abp.ResourceLoader.loadStyle(...)` adiciona um elemento `link` ao `head` do documento para a URL fornecida, assim o arquivo CSS é carregado automaticamente pelo navegador.

**Exemplo: Carregar um arquivo CSS**

```js
abp.ResourceLoader.loadStyle('/Pages/my-styles.css');
