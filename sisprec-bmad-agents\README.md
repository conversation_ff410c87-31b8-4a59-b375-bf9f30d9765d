# Agente SISPREC-BMAD - Modos Personalizados para TRF3.SISPREC

## Padrões de Codificação e Convenções

**Todos os Modos Personalizados e atividades de desenvolvimento devem seguir estritamente os padrões de codificação e convenções definidos no arquivo `data/sisprec-coding-standards.md`.** Este documento é a fonte central de verdade para as boas práticas de desenvolvimento no projeto TRF3.SISPREC.

## Visão Geral

Esta é uma adaptação especializada do Método BMAD (Breakthrough Method of Agile AI-driven Development) para o projeto TRF3.SISPREC, um sistema de gestão de precatórios desenvolvido com ABP Framework 8 e arquitetura DDD. O SISPREC-BMAD implementa **modos personalizados específicos do projeto** no RooCode, proporcionando agentes especializados com conhecimento profundo do domínio judiciário brasileiro.

## Arquitetura do Sistema

### Sistema de Modos Personalizados Específicos do Projeto

O SISPREC-BMAD utiliza modos personalizados em formato JSON específicos do workspace:

```mermaid
graph TD
    RooCode["RooCode"] --> ProjectModes["Modos Personalizados (.roomodes)"]
    ProjectModes --> RulesSystem["Sistema de Regras (.roo/rules-{slug}/)"]

    subgraph "Modos Personalizados SISPREC"
        ProjectModes --> Orchestrator["🎯 Orquestrador SISPREC"]
        ProjectModes --> Arquiteto["🏗️ Arquiteto SISPREC"]
        ProjectModes --> DevBackend["⚙️ Dev-Backend SISPREC"]
        ProjectModes --> DevFrontend["🎨 Dev-Frontend SISPREC"]
        ProjectModes --> Tester["🧪 Testador SISPREC"]
    end

    subgraph "Sistema de Recursos"
        RulesSystem --> Templates["📋 Templates"]
        RulesSystem --> Tasks["📝 Tarefas"]
        RulesSystem --> Checklists["✅ Checklists"]
        RulesSystem --> Data["📚 Base de Conhecimento"]
    end
```

### Estrutura do Projeto

```
TRF3.SISPREC/                          # Raiz do projeto
├── src/                              # Código-fonte do projeto
└── sisprec-bmad-agent/                # Recursos e documentação
    ├── README.md                      # Este arquivo
    ├── SETUP-ROOCODE.md               # Instruções de configuração
    ├── CUSTOM-MODES-SISPREC.md        # Documentação dos modos personalizados
    ├── personas/                      # Definições de personas especializadas
    │   ├── sisprec-orchestrator.md    # Orquestrador principal
    │   ├── arquiteto.md               # Especialista em arquitetura ABP/DDD
    │   ├── dev-backend.md             # Desenvolvedor backend (todas as camadas)
    │   ├── dev-frontend.md            # Desenvolvedor frontend (Web/Razor Pages)
    │   └── tester.md                  # Especialista em testes automatizados
    ├── tasks/                         # Tarefas específicas do SISPREC
    │   ├── create-crud-complete.md    # CRUD completo seguindo padrões SISPREC
    │   └── validate-patterns-task.md  # Validação de padrões arquiteturais
    ├── templates/                     # Templates adaptados para SISPREC
    │   ├── entity-template.md         # Template para entidades DDD
    │   ├── razor-page-template.md     # Template para páginas Razor
    │   ├── appservice-template.md     # Template para Application Services
    │   └── test-template.md           # Template para testes automatizados
    ├── checklists/                    # Checklists de qualidade específicos
    │   ├── sisprec-quality-checklist.md # Checklist geral de qualidade
    │   └── architecture-checklist.md # Validação arquitetural
    ├── data/                          # Base de conhecimento SISPREC
    │   ├── sisprec-kb.md              # Conhecimento base do sistema
    │   └── abp-patterns.md            # Padrões ABP Framework
    ├── config/                        # Configurações dos agentes
    │   ├── orchestrator-config.md     # Configuração do orquestrador
    │   └── agent-mappings.md          # Mapeamento de recursos por agente
    └── roocode/                       # Arquivos para configurar modos personalizados no Roo Code
        ├── .roomodes                          # Modos personalizados em formato JSON
        ├── .roo/                              # Diretório de regras do RooCode
        │   ├── rules-sisprec-orchestrator/    # Regras do orquestrador
        │   ├── rules-sisprec-arquiteto/       # Regras do arquiteto
        │   ├── rules-sisprec-backend/         # Regras do dev backend
        │   ├── rules-sisprec-frontend/        # Regras do dev frontend
        │   └── rules-sisprec-tester/          # Regras do testador
```

## Modos Personalizados Especializados

### Conceito de Modos Personalizados Específicos do Projeto

O SISPREC-BMAD utiliza **modos personalizados específicos do projeto** no RooCode, proporcionando:

-   **Especialização**: Cada modo otimizado para tarefas específicas do SISPREC
-   **Contexto Integrado**: Conhecimento profundo do domínio judiciário brasileiro
-   **Isolamento**: Configurações específicas do projeto, sem impacto global
-   **Eficiência**: Comandos e fluxos específicos para o domínio

### Modos Personalizados Disponíveis

#### 1. 🎯 Orquestrador SISPREC (`sisprec-orchestrator`)

-   **Função**: Coordenação de equipe especializada para desenvolvimento SISPREC
-   **Especialização**:
    -   Conhecimento profundo da arquitetura ABP Framework e padrões DDD
    -   Gestão de fluxos de trabalho complexos do domínio judiciário
    -   Coordenação entre diferentes especialistas
    -   Tomada de decisões arquiteturais estratégicas
-   **Acesso**: Todas as ferramentas (read, edit, command, browser)
-   **Comandos Principais**: `/create-crud`, `/analyze-entity`, `/run-quality-check`

#### 2. 🏗️ Arquiteto SISPREC (`sisprec-arquiteto`)

-   **Função**: Especialista em arquitetura ABP Framework 8, Clean Architecture, DDD
-   **Especialização**:
    -   Arquitetura ABP Framework 8 com padrões avançados
    -   Domain-Driven Design (DDD) para domínio judiciário
    -   Clean Architecture com camadas bem definidas
    -   Integrações complexas (CJF, SEI, MinIO, sistemas legados)
    -   Performance, segurança e escalabilidade
-   **Acesso**: Arquivos C#, JSON e Markdown + command + browser
-   **Comandos Principais**: `/review-architecture`, `/validate-patterns`, `/design-module`

#### 3. ⚙️ Dev Backend SISPREC (`sisprec-backend`)

-   **Função**: Desenvolvimento backend (Domain, Application, Infrastructure, API)
-   **Especialização**:
    -   **Camada de Domínio (Domain Layer)**: Entidades ricas (Entity/BaseEntidadeDominio), serviços de domínio (NUNCA Aggregates/Value Objects)
    -   **Camada de Aplicação (Application Layer)**: Application services, DTOs, AutoMapper, validações
    -   **Camada de Infraestrutura (Infrastructure Layer)**: Repositórios EF Core, integrações externas
    -   **Camada de API (API Layer)**: Controllers REST, documentação Swagger, validações
-   **Acesso**: Arquivos C# e JSON + command
-   **Comandos Principais**: `/implement-entity`, `/create-appservice`, `/implement-repository`

#### 4. 🎨 Dev Frontend SISPREC (`sisprec-frontend`)

-   **Função**: Desenvolvimento frontend (Web, Razor Pages, Bootstrap)
-   **Especialização**:
    -   Razor Pages com padrões SISPREC específicos
    -   Bootstrap 5 + ABP Tag Helpers (sem CSS customizado)
    -   JavaScript/jQuery modular e reutilizável
    -   UX/UI otimizada para operadores judiciários
    -   Responsividade e acessibilidade
-   **Acesso**: Arquivos web (Razor, C#, JS, CSS, JSON) + browser
-   **Comandos Principais**: `/create-page`, `/implement-form`, `/create-listing`

#### 5. 🧪 Testador SISPREC (`sisprec-tester`)

-   **Função**: Testes automatizados em todas as camadas
-   **Especialização**:
    -   Testes unitários (xUnit, NSubstitute, Shouldly)
    -   Testes de integração (TestContainers, ABP Test Framework)
    -   Testes end-to-end (Selenium WebDriver)
    -   Cobertura de código e métricas de qualidade
    -   Mocks e fixtures específicos do domínio judiciário
-   **Acesso**: Arquivos C# e JSON de teste + command
-   **Comandos Principais**: `/create-unit-tests`, `/create-integration-tests`, `/generate-coverage-report`

## Características Específicas do SISPREC

### Arquitetura ABP Framework

-   Design modular com ABP Modules
-   Injeção de Dependência (Dependency Injection) nativa
-   Auditoria automática
-   Sistema de permissões
-   Background Jobs com Quartz

### Domínio de Negócio

-   **Entidades Principais**: RequisicaoProtocolo, Processo, Beneficiario, Proposta (usando Entity/BaseEntidadeDominio)
-   **Gestão Complexa**: Gestão de precatórios e RPVs sem Aggregates
-   **Integrações**: CJF, SEI, MinIO, sistemas legados
-   **Workflows**: Processamento de precatórios com fases bem definidas

### Padrões Técnicos

-   Clean Architecture com 4 camadas bem definidas
-   Padrão Repositório (Repository Pattern) com EF Core
-   CQRS implícito via Application Services
-   Eventos de Domínio (Domain Events) para comunicação entre entidades
-   **NUNCA usar Value Objects** - usar tipos primitivos ou classes simples

## Configuração dos Modos Personalizados

### Arquivos de Configuração

#### `.roomodes` (Formato JSON)

Arquivo principal na raiz do projeto contendo as definições dos 5 modos personalizados:

```json
{
    "customModes": [
        {
            "slug": "sisprec-orchestrator",
            "name": "🎯 Orquestrador SISPREC",
            "roleDefinition": "Coordenador especializado...",
            "whenToUse": "Para coordenação geral...",
            "customInstructions": "Contexto SISPREC...",
            "groups": ["read", "edit", "command", "browser"]
        }
        // ... outros modos
    ]
}
```

#### Diretórios de Regras (`.roo/rules-{slug}/`)

Cada modo personalizado possui um diretório com regras específicas:

-   `.roo/rules-bmad-orchestrator/01-orchestrator-rules.md`
-   `.roo/rules-arquiteto-sisprec/01-arquiteto-rules.md`
-   `.roo/rules-dev-backend-sisprec/01-dev-backend-rules.md`
-   `.roo/rules-dev-frontend-sisprec/01-dev-frontend-rules.md`
-   `.roo/rules-tester-sisprec/01-tester-rules.md`

## Sistema de Qualidade Integrado

### Checklists de Validação

O sistema implementa validação sistemática através de checklists especializados:

```mermaid
graph LR
    Documento["Documento/Código"] --> MotorChecklist["checklist-run-task"]
    MotorChecklist --> ModoValidacao{Modo de Validação}

    ModoValidacao -->|Interativo| PassoAPasso["Validação Passo-a-Passo"]
    ModoValidacao -->|YOLO| ValidacaoLote["Validação em Lote"]

    PassoAPasso --> FeedbackUsuario["Feedback do Usuário"]
    ValidacaoLote --> RelatorioAbrangente["Relatório Abrangente"]

    FeedbackUsuario --> RelatorioFinal["Relatório Final"]
    RelatorioAbrangente --> RelatorioFinal

    RelatorioFinal --> StatusQualidade["Status: ✅ APROVADO / ❌ REPROVADO / ⚠️ PARCIAL / N/A"]
```

### Gates de Qualidade

-   **Gate de Arquitetura**: Validação de padrões ABP/DDD, performance, segurança
-   **Gate de Backend**: Validação de modelos de domínio, application services, repositórios
-   **Gate de Frontend**: Validação de UX/UI, responsividade, acessibilidade
-   **Gate de Testes**: Cobertura de código, qualidade dos testes, automação

## Como Usar os Modos Personalizados

### 1. Ativação Automática

Os modos personalizados são ativados automaticamente quando você abre o projeto TRF3.SISPREC no RooCode:

1. Abra o projeto no RooCode
2. Os modos personalizados aparecerão na interface
3. Selecione o modo apropriado para sua tarefa

### 2. Fluxos de Trabalho Recomendados

#### Fluxo: Nova Entidade Completa (Coordenado)

1. **Modo**: Orquestrador BMAD
2. **Comando**: `/create-crud {entidade}` ou "Criar CRUD completo para entidade {Nome}"
3. **Sequência Coordenada**:
    - Orquestrador analisa requisitos e cria plano
    - Coordena com Arquiteto para design
    - Coordena com Dev-Backend para implementação
    - Coordena com Dev-Frontend para interface
    - Coordena com Testador para validação

#### Fluxo: Desenvolvimento Especializado (Direto)

1. **Backend**: Modo Dev Backend SISPREC → "Implementar {funcionalidade}"
2. **Frontend**: Modo Dev Frontend SISPREC → "Criar interface para {funcionalidade}"
3. **Arquitetura**: Modo Arquiteto SISPREC → "Analisar arquitetura do módulo {Nome}"
4. **Testes**: Modo Testador SISPREC → "Criar testes para {componente}"

### 3. Comandos Disponíveis

#### Comandos Gerais (Todos os Modos)

-   `/help` - Lista todos os comandos disponíveis
-   `/analyze-entity {nome}` - Análise completa de entidade
-   `/create-crud {entidade}` - CRUD completo seguindo padrões SISPREC
-   `/check-patterns` - Verificar aderência aos padrões ABP/DDD
-   `/validate-business-rules` - Verificar implementação de regras de negócio

#### Comandos Específicos por Modo

-   **Orquestrador BMAD**: `/run-quality-check`, `/optimize-performance`
-   **Arquiteto SISPREC**: `/review-architecture`, `/design-module`
-   **Dev Backend SISPREC**: `/implement-entity`, `/create-appservice`
-   **Dev Frontend SISPREC**: `/create-page`, `/implement-form`
-   **Testador SISPREC**: `/create-unit-tests`, `/generate-coverage-report`

## Vantagens dos Modos Personalizados SISPREC

### Especialização

-   Cada modo otimizado para tarefas específicas do SISPREC
-   Conhecimento profundo do domínio judiciário brasileiro
-   Padrões técnicos específicos do ABP Framework 8

### Isolamento

-   Configurações específicas do projeto
-   Não interferem com configurações globais do RooCode
-   Mantêm contexto do projeto SISPREC

### Eficiência

-   Comandos específicos para tarefas comuns
-   Fluxos de trabalho otimizados
-   Coordenação automática entre modos

### Qualidade

-   Validações automáticas de padrões
-   Verificações de regras de negócio específicas
-   Testes abrangentes em todas as camadas

## Integração com SISPREC

Este sistema foi especificamente adaptado para:

-   Respeitar a arquitetura existente do SISPREC
-   Seguir os padrões de código estabelecidos (ABP Framework 8 + DDD)
-   Integrar com o sistema de build e deploy
-   Manter compatibilidade com as integrações existentes (CJF, SEI, MinIO)
-   Seguir as regras de negócio específicas do domínio judiciário

## Documentação Adicional

-   **`SETUP-ROOCODE.md`**: Instruções detalhadas de configuração
-   **`CUSTOM-MODES-SISPREC.md`**: Documentação completa dos modos personalizados
-   **`.roo/rules-{slug}/`**: Regras específicas de cada modo
-   **`personas/`**: Definições detalhadas de cada especialista
-   **`templates/`**: Templates para padronização de código

## Próximos Passos

1. **Verificar configuração** dos modos personalizados no RooCode
2. **Testar fluxos básicos** com tarefas simples
3. **Expandir templates** conforme necessidades do projeto
4. **Treinar equipe** nos novos fluxos de trabalho
5. **Coletar feedback** e refinar configurações

---

**Nota**: Esta implementação utiliza modos personalizados específicos do projeto TRF3.SISPREC em formato JSON, proporcionando agentes especializados sem interferir com configurações globais do RooCode.
