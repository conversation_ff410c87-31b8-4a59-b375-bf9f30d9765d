---
description: Guia para fluxos de trabalho de desenvolvimento e melhores práticas
globs: **/*
alwaysApply: true
---

# Fluxo de Trabalho de Desenvolvimento (Development Workflow)

Este guia descreve o processo típico para gerenciar projetos de desenvolvimento de software de forma eficaz.

## Visão Geral do Processo de Desenvolvimento (Development Process Overview)

O desenvolvimento eficaz requer abordagens estruturadas para gerenciamento de tarefas, organização de código e garantia de qualidade.

## Processo Padrão de Fluxo de Trabalho de Desenvolvimento (Standard Development Workflow Process)

-   Iniciar novos projetos configurando a estrutura de projeto adequada e a configuração inicial
-   Começar as sessões de codificação revisando as tarefas atuais, status e prioridades
-   Determinar a próxima tarefa a ser trabalhada com base em dependências e prioridade
-   Analisar a complexidade da tarefa antes de dividir tarefas grandes em componentes menores
-   Revisar os requisitos e especificações minuciosamente
-   Selecionar tarefas com base em dependências, nível de prioridade e ordem lógica
-   Esclarecer requisitos verificando a documentação ou solicitando a entrada do usuário
-   Visualizar detalhes específicos da tarefa para entender os requisitos de implementação
-   Dividir tarefas complexas em subtarefas gerenciáveis
-   Remover quaisquer bloqueadores ou dependências antes de iniciar a implementação
-   Implementar código seguindo os detalhes da tarefa, dependências e padrões do projeto
-   Verificar a implementação de acordo com as estratégias de teste antes de marcar como concluída
-   Marcar as tarefas concluídas apropriadamente em seu sistema de rastreamento
-   Atualizar tarefas dependentes quando a implementação diferir do plano original
-   Adicionar novas tarefas descobertas durante a implementação
-   Adicionar novas subtarefas conforme necessário para melhor organização
-   Documentar notas e detalhes de implementação para referência futura
-   Gerar documentação após a conclusão de recursos importantes
-   Manter uma estrutura de dependência e relacionamentos de tarefas válidos
-   Respeitar as cadeias de dependência e as prioridades das tarefas ao selecionar o trabalho
-   Relatar o progresso regularmente às partes interessadas

## Análise de Complexidade da Tarefa (Task Complexity Analysis)

-   Executar análise de complexidade do projeto para uma compreensão abrangente
-   Revisar o relatório de complexidade para uma versão formatada e legível da análise.
-   Focar em tarefas com as maiores pontuações de complexidade (8-10) para detalhamento
-   Usar os resultados da análise para determinar a alocação apropriada de subtarefas
-   Observar que os relatórios são usados automaticamente pela ferramenta/comando `expand_task`

## Processo de Detalhamento de Tarefas (Task Breakdown Process)

-   Usar a ferramenta `expand_task`. Ela usa automaticamente o relatório de complexidade, se encontrado, caso contrário, gera um número padrão de subtarefas.
-   Usar `--num=<number>` para especificar um número explícito de subtarefas, substituindo os padrões ou as recomendações do relatório de complexidade.
-   Adicionar a flag `--research` para aproveitar a IA Perplexity para expansão baseada em pesquisa.
-   Adicionar a flag `--force` para limpar as subtarefas existentes antes de gerar novas (o padrão é anexar).
-   Usar `--prompt="<context>"` para fornecer contexto adicional quando necessário.
-   Revisar e ajustar as subtarefas geradas conforme necessário.
-   Usar a ferramenta `expand_all` para expandir várias tarefas pendentes de uma vez, respeitando flags como `--force` e `--research`.
-   Se as subtarefas precisarem de substituição completa (independentemente da flag `--force` em `expand`), limpe-as primeiro com a ferramenta `clear_subtasks`.

## Tratamento de Desvio de Implementação (Implementation Drift Handling)

-   Quando a implementação difere significativamente da abordagem planejada
-   Quando tarefas futuras precisam de modificação devido às escolhas de implementação atuais
-   Quando novas dependências ou requisitos surgem
-   Usar a ferramenta `update` para atualizar várias tarefas futuras.
-   Usar a ferramenta `update_task` para atualizar uma única tarefa específica.

## Gerenciamento de Status da Tarefa (Task Status Management)

-   Usar 'pending' para tarefas prontas para serem trabalhadas
-   Usar 'done' para tarefas concluídas e verificadas
-   Usar 'deferred' para tarefas adiadas
-   Adicionar valores de status personalizados conforme necessário para fluxos de trabalho específicos do projeto

## Campos da Estrutura da Tarefa (Task Structure Fields)

-   **id**: Identificador único para a tarefa (Exemplo: `1`, `1.1`)
-   **title**: Título breve e descritivo (Exemplo: `"Initialize Repo"`)
-   **description**: Resumo conciso do que a tarefa envolve (Exemplo: `"Create a new repository, set up initial structure."`)
-   **status**: Estado atual da tarefa (Exemplo: `"pending"`, `"done"`, `"deferred"`)
-   **dependencies**: IDs de tarefas pré-requisito (Exemplo: `[1, 2.1]`)
    -   As dependências são exibidas com indicadores de status (✅ para concluídas, ⏱️ para pendentes)
    -   Isso ajuda a identificar rapidamente quais tarefas pré-requisito estão bloqueando o trabalho
-   **priority**: Nível de importância (Exemplo: `"high"`, `"medium"`, `"low"`)
-   **details**: Instruções de implementação detalhadas (Exemplo: `"Use GitHub client ID/secret, handle callback, set session token."`)
-   **testStrategy**: Abordagem de verificação (Exemplo: `"Deploy and call endpoint to confirm 'Hello World' response."`)
-   **subtasks**: Lista de tarefas menores e mais específicas (Exemplo: `[{"id": 1, "title": "Configure OAuth", ...}]`)
-   Consultar detalhes da estrutura da tarefa (anteriormente vinculado a `tasks.md`).

## Gerenciamento de Configuração (Configuration Management) (Atualizado)

A configuração do projeto é gerenciada por meio de arquivos de configuração:

1.  **Arquivo de Configuração (Principal):**

    -   Localizado no diretório raiz do projeto.
    -   Armazena a maioria das configurações: seleções de modelo de IA (principal, pesquisa, fallback), parâmetros (max tokens, temperatura), nível de log, subtarefas/prioridade padrão, nome do projeto, etc.
    -   **Gerenciado por meio de ferramentas de configuração.** Não edite manualmente, a menos que saiba o que está fazendo.
    -   **Visualizar/Definir modelos específicos via ferramenta MCP `models`.**
    -   Criado automaticamente quando você executa a configuração pela primeira vez.

2.  **Variáveis de Ambiente (`.env` / `mcp.json`):**
    -   Usado **apenas** para chaves de API sensíveis e URLs de endpoint específicas.
    -   Coloque as chaves de API (uma por provedor) em um arquivo `.env` no diretório raiz do projeto para uso na CLI.
    -   Para integração MCP/Roo Code, configure essas chaves na seção `env` de `.roo/mcp.json`.
    -   Chaves/variáveis disponíveis: Consulte `assets/env.example` ou a seção de Configuração na documentação do projeto.

**Importante:** As configurações que não são chaves de API (como seleções de modelo, `MAX_TOKENS`, `LOG_LEVEL`) **não são mais configuradas via variáveis de ambiente**. Use a ferramenta MCP `models` para gerenciamento de configuração.
**Se os comandos de IA FALHAREM no MCP**, verifique se a chave de API para o provedor selecionado está presente na seção `env` de `.roo/mcp.json`.
**Se os comandos de IA FALHAREM na CLI**, verifique se a chave de API para o provedor selecionado está presente no arquivo `.env` na raiz do projeto.

## Determinando a Próxima Tarefa (Determining the Next Task)

-   Executar a ferramenta `next_task` para mostrar a próxima tarefa a ser trabalhada.
-   O comando identifica tarefas com todas as dependências satisfeitas
-   As tarefas são priorizadas por nível de prioridade, contagem de dependências e ID
-   O comando mostra informações abrangentes da tarefa, incluindo:
    -   Detalhes básicos da tarefa e descrição
    -   Detalhes de implementação
    -   Subtarefas (se existirem)
    -   Ações sugeridas contextuais
-   Recomendado antes de iniciar qualquer novo trabalho de desenvolvimento
-   Respeita a estrutura de dependência do seu projeto
-   Garante que as tarefas sejam concluídas na sequência apropriada
-   Fornece comandos prontos para uso para ações comuns de tarefas

## Visualizando Detalhes Específicos da Tarefa (Viewing Specific Task Details)

-   Executar a ferramenta `get_task` para visualizar uma tarefa específica.
-   Usar a notação de ponto para subtarefas: mostrar a subtarefa 2 da tarefa 1 como `1.2`
-   Exibe informações abrangentes semelhantes ao comando `next`, mas para uma tarefa específica
-   Para tarefas pai, mostra todas as subtarefas e seu status atual
-   Para subtarefas, mostra informações e relacionamento da tarefa pai
-   Fornece ações sugeridas contextuais apropriadas para a tarefa específica
-   Útil para examinar os detalhes da tarefa antes da implementação ou verificar o status

## Gerenciando Dependências de Tarefas (Managing Task Dependencies)

-   Usar a ferramenta `add_dependency` para adicionar uma dependência.
-   Usar a ferramenta `remove_dependency` para remover uma dependência.
-   O sistema impede dependências circulares e entradas de dependência duplicadas
-   As dependências são verificadas quanto à existência antes de serem adicionadas ou removidas
-   Os arquivos de tarefa são regenerados automaticamente após alterações de dependência
-   As dependências são visualizadas com indicadores de status nas listagens e arquivos de tarefas

## Implementação Iterativa de Subtarefas (Iterative Subtask Implementation)

Uma vez que uma tarefa tenha sido dividida em subtarefas usando `expand_task` ou métodos semelhantes, siga este processo iterativo para implementação:

1.  **Entender o Objetivo (Preparação):**

    -   Revisar os detalhes da subtarefa minuciosamente para entender os objetivos e requisitos específicos de cada subtarefa.

2.  **Exploração e Planejamento Inicial (Iteração 1):**

    -   Esta é a primeira tentativa de criar um plano de implementação concreto.
    -   Explorar a base de código para identificar os arquivos, funções e até mesmo linhas de código específicas que precisarão de modificação.
    -   Determinar as alterações de código pretendidas (diffs) e seus locais.
    -   Coletar _todos_ os detalhes relevantes desta fase de exploração.

3.  **Registrar o Plano (Log the Plan):**

    -   Executar a ferramenta `update_subtask` com o ID da subtarefa e o plano detalhado.
    -   Fornecer as descobertas _completas e detalhadas_ da fase de exploração no prompt. Incluir caminhos de arquivo, números de linha, diffs propostos, raciocínio e quaisquer desafios potenciais identificados. Não omitir detalhes. O objetivo é criar um log rico e com carimbo de data/hora dentro dos `details` da subtarefa.

4.  **Verificar o Plano (Verify the Plan):**

    -   Executar a ferramenta `get_task` novamente para confirmar que o plano de implementação detalhado foi anexado com sucesso aos detalhes da subtarefa.

5.  **Iniciar a Implementação (Begin Implementation):**

    -   Definir o status da subtarefa usando a ferramenta `set_task_status` com o status 'in-progress'.
    -   Começar a codificar com base no plano registrado.

6.  **Refinar e Registrar o Progresso (Refine and Log Progress) (Iteração 2+):**

    -   À medida que a implementação avança, você encontrará desafios, descobrirá nuances ou confirmará abordagens bem-sucedidas.
    -   **Antes de anexar novas informações**: Revisar brevemente os detalhes _existentes_ registrados na subtarefa (usando `get_task` ou lembrando do contexto) para garantir que a atualização adicione novos insights e evite redundância.
    -   **Regularmente** usar a ferramenta `update_subtask` para anexar novas descobertas.
    -   **Crucialmente, registrar:**
        -   O que funcionou ("verdades fundamentais" descobertas).
        -   O que não funcionou e por quê (para evitar repetir erros).
        -   Trechos de código ou configurações específicas que foram bem-sucedidas.
        -   Decisões tomadas, especialmente se confirmadas com a entrada do usuário.
        -   Quaisquer desvios do plano inicial e o raciocínio.
    -   O objetivo é enriquecer continuamente os detalhes da subtarefa, criando um log da jornada de implementação que ajuda a IA (e os desenvolvedores humanos) a aprender, adaptar e evitar repetir erros.

7.  **Revisar e Atualizar Regras (Review & Update Rules) (Pós-Implementação):**

    -   Uma vez que a implementação para a subtarefa esteja funcionalmente completa, revisar todas as alterações de código e o histórico de chat relevante.
    -   Identificar quaisquer padrões de código, convenções ou melhores práticas novas ou modificadas estabelecidas durante a implementação.
    -   Criar novas ou atualizar regras existentes seguindo as diretrizes internas (anteriormente vinculadas a `cursor_rules.md` e `self_improve.md`).

8.  **Marcar Tarefa Concluída (Mark Task Complete):**

    -   Após verificar a implementação e atualizar quaisquer regras necessárias, marcar a subtarefa como concluída: ferramenta `set_task_status` com o status 'done'.

9.  **Commitar Alterações (Commit Changes) (Se estiver usando Git):**

    -   Preparar as alterações de código relevantes e quaisquer arquivos de regra atualizados/novos (`git add .`).
    -   Criar uma mensagem de commit Git abrangente resumindo o trabalho feito para a subtarefa, incluindo a implementação do código e quaisquer ajustes de regra.
    -   Executar o comando de commit diretamente no terminal (ex: `git commit -m 'feat(module): Implement feature X for subtask <subtaskId>\n\n- Details about changes...\n- Updated rule Y for pattern Z'`).
    -   Considerar se um Changeset é necessário de acordo com as diretrizes internas de versionamento (anteriormente vinculado a `changeset.md`). Se sim, executar `npm run changeset`, preparar o arquivo gerado e emendar o commit ou criar um novo.

10. **Prosseguir para a Próxima Subtarefa (Proceed to Next Subtask):**
    -   Identificar a próxima subtarefa (ex: usando a ferramenta `next_task`).

## Técnicas de Análise e Refatoração de Código (Code Analysis & Refactoring Techniques)

-   **Busca de Função de Nível Superior (Top-Level Function Search)**:
    -   Útil para entender a estrutura do módulo ou planejar refatorações.
    -   Usar grep/ripgrep para encontrar funções/constantes exportadas:
        `rg "export (async function|function|const) \w+"` ou padrões semelhantes.
    -   Pode ajudar a comparar funções entre arquivos durante migrações ou identificar potenciais conflitos de nomenclatura.

---

_Este fluxo de trabalho fornece uma diretriz geral. Adapte-o com base nas necessidades específicas do seu projeto e nas práticas da equipe._
