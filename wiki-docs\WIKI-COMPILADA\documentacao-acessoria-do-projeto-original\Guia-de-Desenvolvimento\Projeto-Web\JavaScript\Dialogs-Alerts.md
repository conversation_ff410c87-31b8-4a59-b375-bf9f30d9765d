# Dialogs/Alerts

A API de Mensagens é usada para mostrar mensagens agradáveis ao usuário como um Dialog/Alert. A API de Mensagens é uma abstração fornecida pelo ABP Framework e implementada usando a biblioteca [SweetAlert](https://sweetalert.js.org/) por padrão.

## Exemplo Rápido

Use a função `abp.message.success(...)` para mostrar uma mensagem de sucesso:

```js
abp.message.success('Suas alterações foram salvas com sucesso!', 'Parabéns');
```

Isso mostrará um diálogo na UI:

![js-message-success](/ABP-Docs/images/js-message-success.png)

## Mensagens Informativas

Existem quatro tipos de funções de mensagens informativas:

* `abp.message.info(...)`
* `abp.message.success(...)`
* `abp.message.warn(...)`
* `abp.message.error(...)`

Todos esses métodos recebem dois parâmetros:

* `message`: A mensagem (`string`) a ser exibida.
* `title`: Um título opcional (`string`).

**Exemplo: Mostrar uma mensagem de erro**

```js
abp.message.error('Seu número de cartão de crédito não é válido!');
```

![js-message-error](/ABP-Docs/images/js-message-error.png)

## Mensagem de Confirmação

A função `abp.message.confirm(...)` pode ser usada para obter uma confirmação do usuário.

**Exemplo**

Use o seguinte código para obter um resultado de confirmação do usuário:

```js
abp.message.confirm('Você tem certeza de que deseja excluir o papel "admin"?')
.then(function(confirmed){
  if(confirmed){
    console.log('TODO: excluindo o papel...');
  }
});
```

A UI resultante será como mostrado abaixo:

![js-message-confirm](/ABP-Docs/images/js-message-confirm.png)

Se o usuário clicar no botão `Sim`, o argumento `confirmed` na função de callback `then` será `true`.

> "*Você tem certeza?*" é o título padrão (localizado com base no idioma atual) e você pode substituí-lo.

### O Valor de Retorno

O valor de retorno da função `abp.message.confirm(...)` é uma promise, então você pode encadear um callback `then` como mostrado acima.

### Parâmetros

A função `abp.message.confirm(...)` tem os seguintes parâmetros:

* `message`: Uma mensagem (string) a ser mostrada ao usuário.
* `titleOrCallback` (opcional): Um título ou uma função de callback. Se você fornecer uma string, ela será exibida como o título. Se você fornecer uma função de callback (que recebe um parâmetro `bool`), ela será chamada com o resultado.
* `callback` (opcional): Se você passou um título para o segundo parâmetro, pode passar sua função de callback como o terceiro parâmetro.

Passar uma função de callback é uma alternativa ao callback `then` mostrado acima.

**Exemplo: Fornecendo todos os parâmetros e obtendo o resultado com a função de callback**

```js
abp.message.confirm(
  'Você tem certeza de que deseja excluir o papel "admin"?',
  'Tenha cuidado!',
  function(confirmed){
    if(confirmed){
      console.log('TODO: excluindo o papel...');
    }
  });
```

## Configuração do SweetAlert

A API de Mensagens é implementada usando a biblioteca [SweetAlert](https://sweetalert.js.org/) por padrão. Se você quiser alterar sua configuração, pode definir as opções no objeto `abp.libs.sweetAlert.config`. O objeto de configuração padrão é mostrado abaixo:

```js
{
    'default': {
    },
    info: {
        icon: 'info'
    },
    success: {
        icon: 'success'
    },
    warn: {
        icon: 'warning'
    },
    error: {
        icon: 'error'
    },
    confirm: {
        icon: 'warning',
        title: 'Você tem certeza?',
        buttons: ['Cancelar', 'Sim']
    }
}
```

> Os textos "Você tem certeza?", "Cancelar" e "Sim" são automaticamente localizados com base no idioma atual.

Portanto, se você quiser definir o ícone `warn`, pode configurá-lo assim:

```js
abp.libs.sweetAlert.config.warn.icon = 'error';
```

Consulte o documento [SweetAlert](https://sweetalert.js.org/) para todas as opções de configuração.
