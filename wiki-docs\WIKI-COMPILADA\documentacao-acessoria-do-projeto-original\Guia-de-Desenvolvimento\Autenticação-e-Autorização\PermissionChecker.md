[[_TOC_]]
# SISPRECPermissionChecker

A classe SISPRECPermissionChecker fornece verificação de permissões flexível, permitindo que claims customizadas sejam consideradas junto com as validações padrão do ABP Framework.
Assim, perfis privilegiados (por exemplo, AdminTI) recebem permissão global, e as claims personalizadas atuam como complemento às permissões nativas do AbpPermissionChecker.

## Casos de Uso

- Garantir que usuários com papel “AdminTI” tenham acesso total ao sistema.
- Integrar claims customizadas em cenários específicos, sem quebrar a lógica de permissões padrão.
- Tornar o gerenciamento de permissões mais coeso e consistente em todo o sistema.

```csharp
// ...existing code...
[ExposeServices(typeof(IPermissionChecker))]
[Dependency(ReplaceServices = true)]
[ExcludeFromCodeCoverage]
public class SISPRECPermissionChecker : <PERSON>er<PERSON><PERSON><PERSON><PERSON>, ITransientDependency
{
    protected IPermissionChecker AbpPermissionChecker { get; }
    protected ICurrentPrincipalAccessor PrincipalAccessor { get; }

    public SISPRECPermissionChecker(
        ICurrentPrincipalAccessor principalAccessor,
        IPermissionDefinitionManager permissionDefinitionManager,
        ICurrentTenant currentTenant,
        IPermissionValueProviderManager permissionValueProviderManager,
        ISimpleStateCheckerManager<PermissionDefinition> stateCheckerManager)
    {
        PrincipalAccessor = principalAccessor;
        // Foi necessário usar composição, pois a herança com override não resolveu o problema,
        // visto que não é possível sobrescrever os métodos que recebem arrays de permissões
        AbpPermissionChecker = new PermissionChecker(principalAccessor, permissionDefinitionManager, currentTenant, permissionValueProviderManager, stateCheckerManager);
    }

    public async Task<bool> IsGrantedAsync(string name)
    {
        var isGranted = PrincipalAccessor.Principal.IsInRole(SISPRECPermissoes.Perfil.AdminTI);

        if (isGranted)
            return true;

        isGranted = await AbpPermissionChecker.IsGrantedAsync(PrincipalAccessor.Principal, name);

        if (!isGranted)
            // Verifica as claims customizadas, se a permissão não foi concedida pelo AbpPermissionChecker
            isGranted = PrincipalAccessor.Principal.HasClaim(SISPRECPermissoes.NomeClaimTypePermissoes, name);

        return isGranted;
    }

    public async Task<MultiplePermissionGrantResult> IsGrantedAsync(string[] names)
    {
        var isGranted = PrincipalAccessor.Principal.IsInRole(SISPRECPermissoes.Perfil.AdminTI);

        if (isGranted)
            return new MultiplePermissionGrantResult(names, PermissionGrantResult.Granted);

        // Composição: Utiliza o método IsGrantedAsync do AbpPermissionChecker para múltiplas permissões
        var multiplePermissionGrantResult = await AbpPermissionChecker.IsGrantedAsync(PrincipalAccessor.Principal, names);

        if (multiplePermissionGrantResult.AllGranted)
            return multiplePermissionGrantResult;

        var claimsPermissionGrantResult = new MultiplePermissionGrantResult();

        foreach (var name in names)
        {
            // Composição: Verifica as claims customizadas para cada permissão
            var grantResult = PrincipalAccessor.Principal.HasClaim(SISPRECPermissoes.NomeClaimTypePermissoes, name) ? PermissionGrantResult.Granted : PermissionGrantResult.Prohibited;
            claimsPermissionGrantResult.Result.Add(name, grantResult);
        }

        return claimsPermissionGrantResult;
    }

    public async Task<bool> IsGrantedAsync(ClaimsPrincipal? claimsPrincipal, string name)
    {
        // Composição: Utiliza o método IsGrantedAsync do AbpPermissionChecker com ClaimsPrincipal
        var isGranted = await AbpPermissionChecker.IsGrantedAsync(claimsPrincipal, name);

        if (!isGranted)
            //Reutiliza o método IsGrantedAsync com o nome da permissão
            isGranted = await this.IsGrantedAsync(name);

        return isGranted;
    }

    public async Task<MultiplePermissionGrantResult> IsGrantedAsync(ClaimsPrincipal? claimsPrincipal, string[] names)
    {
        // Composição: Utiliza o método IsGrantedAsync do AbpPermissionChecker para múltiplas permissões com ClaimsPrincipal
        var multiplePermissionGrantResult = await AbpPermissionChecker.IsGrantedAsync(claimsPrincipal, names);

        if (multiplePermissionGrantResult.AllGranted)
            return multiplePermissionGrantResult;

        //Reutiliza o método IsGrantedAsync com o array de nomes de permissões
        return await this.IsGrantedAsync(names);
    }
}
```