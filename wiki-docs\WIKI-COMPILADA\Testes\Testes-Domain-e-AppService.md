[[_TOC_]]

# Testes de Domain e Application Services

Este guia demonstra como implementar testes automatizados para as camadas de domínio e aplicação no SISPREC.

## Estrutura de Testes

O SISPREC utiliza as seguintes bibliotecas para testes:
- **xUnit**: Framework de testes
- **Shouldly**: Asserções mais legíveis
- **NSubstitute**: Criação de mocks
- **Bogus**: Geração de dados fictícios

### Organização dos Projetos de Teste

```
test/
├── TRF3.SISPREC.Domain.Tests/      # Testes de domínio
├── TRF3.SISPREC.Application.Tests/  # Testes de aplicação
└── TRF3.SISPREC.TestBase/          # Classes base e utilitários
```

## Testes de Domain Services

AutorManagerTests.cs:

```csharp
using Bogus;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using Shouldly;
using TRF3.SISPREC.Autores;
using TRF3.SISPREC.Livros;
using Volo.Abp;
using TRF3.SISPREC.Enums;

namespace TRF3.SISPREC.Tests.Autores;

/// <summary>
/// Classe de testes para o AutorManager que demonstra:
/// 1. Como estruturar testes de um gerenciador de domínio
/// 2. Como usar mocks com NSubstitute
/// 3. Como realizar asserções com Shouldly
/// 4. Como testar regras específicas de negócio
/// </summary>
public class AutorManagerTests : SISPRECDomainTestBase<SISPRECTestBaseModule>
{
    /// <summary>
    /// Mock do repositório de autores. Simula o comportamento do banco de dados
    /// sem precisar de uma conexão real. Usado para verificar se os métodos
    /// do repositório são chamados corretamente.
    /// </summary>
    private readonly IAutorRepository _repository;

    /// <summary>
    /// Instância do gerenciador que será testado (SUT - System Under Test).
    /// É criada com os mocks necessários no construtor.
    /// </summary>
    private readonly AutorManager _manager;

    /// <summary>
    /// Mock do repositório de livros. Necessário para testar a validação
    /// de livros associados ao autor.
    /// </summary>
    private readonly IValidarExcluirAutorService _validarExcluirAutorService;

    /// <summary>
    /// Construtor que inicializa os mocks e o gerenciador para cada teste.
    /// Demonstra como configurar o ambiente de teste usando NSubstitute.
    /// </summary>
    public AutorManagerTests()
    {
        _repository = Substitute.For<IAutorRepository>();
        _validarExcluirAutorService = Substitute.For<IValidarExcluirAutorService>();
        _manager = new AutorManager(_repository, _validarExcluirAutorService);
    }

    /// <summary>
    /// Método auxiliar que cria um autor com dados faker válidos.
    /// Demonstra como usar a biblioteca Bogus para gerar dados de teste
    /// de forma consistente e controlada.
    /// </summary>
    private Autor InicializarAutorComValoresValidos()
    {
        return new Faker<Autor>()
            .RuleFor(p => p.AutorId, p => p.Random.Int(min: 1))
            .RuleFor(p => p.Nome, p => p.Random.Hash())
            .RuleFor(p => p.Email, p => p.Internet.Email())
            .Generate();
    }

    /// <summary>
    /// Testa se o AutorManager valida corretamente se um autor pode ser excluído
    /// verificando se ele não possui livros associados.
    /// Demonstra como testar regras de negócio específicas do domínio.
    /// </summary>
    [Fact]
    public async Task Validar_Se_Autor_Pode_Ser_Excluido()
    {
        // Arrange
        var autor = InicializarAutorComValoresValidos();
        var livros = new List<Livro>();

        _validarExcluirAutorService.Validar(Arg.Any<Autor>()).Returns(Task.CompletedTask);

        // Act
        await _manager.ExcluirAsync(autor);

        // Assert - Se não lançar exceção, o teste passa
        true.ShouldBe(true);
    }

    /// <summary>
    /// Testa se o AutorManager impede a exclusão de um autor que possui livros.
    /// Demonstra como testar exceções esperadas e usar o Shouldly para verificações.
    /// </summary>
    [Fact]
    public async Task Impedir_Exclusao_De_Autor_Com_Livros()
    {
        // Arrange
        var autor = InicializarAutorComValoresValidos();
        var livros = new List<Livro> { new() { LivroId = 1, Titulo = "Teste" } };

        _validarExcluirAutorService.Validar(Arg.Any<Autor>()).ThrowsAsync(new UserFriendlyException("Não é possível excluir Autor com Livro vinculado."));

        // Act & Assert
        var exception = await Should.ThrowAsync<UserFriendlyException>(async () =>
        {
            await _manager.ExcluirAsync(autor);
        });

        exception.Message.ShouldContain("Não é possível excluir Autor com Livro vinculado.");
    }
}

```

ValidarExcluirAutorServiceTests.cs:

```csharp
using NSubstitute;
using Shouldly;
using TRF3.SISPREC.Autores;
using TRF3.SISPREC.Livros;
using Volo.Abp;
using TRF3.SISPREC.Enums;

namespace TRF3.SISPREC.Domain.Tests.Autores;

/// <summary>
/// Classe de testes para o ValidarExcluirAutorService que demonstra:
/// 1. Como estruturar testes de um serviço de validação
/// 2. Como usar mocks com NSubstitute
/// 3. Como realizar asserções com Shouldly
/// 4. Como testar regras específicas de negócio
/// </summary>
public class ValidarExcluirAutorServiceTests : SISPRECDomainTestBase<SISPRECTestBaseModule>
{
    /// <summary>
    /// Mock do repositório de livros. Simula o comportamento do banco de dados
    /// sem precisar de uma conexão real. Usado para verificar se os métodos
    /// do repositório são chamados corretamente.
    /// </summary>
    private readonly ILivroRepository _livroRepository;

    /// <summary>
    /// Instância do serviço que será testado (SUT - System Under Test).
    /// É criada com os mocks necessários no construtor.
    /// </summary>
    private readonly ValidarExcluirAutorService _validarExcluirAutorService;

    /// <summary>
    /// Construtor que inicializa os mocks e o serviço para cada teste.
    /// Demonstra como configurar o ambiente de teste usando NSubstitute.
    /// </summary>
    public ValidarExcluirAutorServiceTests()
    {
        _livroRepository = Substitute.For<ILivroRepository>();
        _validarExcluirAutorService = new ValidarExcluirAutorService(_livroRepository);
    }

    /// <summary>
    /// Testa se o ValidarExcluirAutorService valida corretamente a exclusão
    /// de um autor sem livros vinculados.
    /// Demonstra como testar regras de negócio específicas do domínio.
    /// </summary>
    [Fact]
    public async Task Deve_Validar_Exclusao_De_Autor_Com_Sucesso()
    {
        // Arrange
        var autorId = 1;
        var autor = new Autor { AutorId = autorId };
        var repositoryMock = Substitute.For<IAutorRepository>();
        var autoresQueryable = new List<Autor> { autor }.AsQueryable();
        repositoryMock.GetQueryableAsync().Returns(Task.FromResult(autoresQueryable));
        var validarExcluirAutorService = _validarExcluirAutorService;

        // Act
        await validarExcluirAutorService.Validar(autor);

        // Assert
        Should.NotThrow(async () => await validarExcluirAutorService.Validar(autor));
    }

    /// <summary>
    /// Testa se o ValidarExcluirAutorService impede a exclusão de um autor
    /// que possui livros vinculados.
    /// Demonstra como testar exceções esperadas e usar o Shouldly para verificações.
    /// </summary>
    [Fact]
    public async Task Deve_Nao_Validar_Exclusao_De_Autor_Com_Livros_Vinculados()
    {
        // Arrange
        var autorId = 1;
        var autor = new Autor { AutorId = autorId };
        var livroRepositoryMock = Substitute.For<ILivroRepository>();
        livroRepositoryMock.GetQueryableAsync().Returns(Task.FromResult(new List<Livro>
        {
            new Livro { Autores = new List<Autor> { autor } }
        }.AsQueryable()));

        var validarExcluirAutorService = new ValidarExcluirAutorService(livroRepositoryMock);

        // Act
        Func<Task> act = async () => await validarExcluirAutorService.Validar(autor);

        // Assert
        var exception = await act.ShouldThrowAsync<UserFriendlyException>();
        exception.Message.ShouldBe("Não é possível excluir Autor com Livro vinculado.");
    }

    /// <summary>
    /// Testa se o ValidarExcluirAutorService valida corretamente a exclusão
    /// de um autor com dados inválidos.
    /// Demonstra como testar exceções esperadas e usar o Shouldly para verificações.
    /// </summary>
    [Fact]
    public async Task Deve_Validar_Exclusao_De_Autor_Com_Dados_Invalidos()
    {
        // Arrange
        var validarExcluirAutorService = _validarExcluirAutorService;

        // Act
        Func<Task> act = async () => await validarExcluirAutorService.Validar(null);

        // Assert
        await act.ShouldThrowAsync<ArgumentNullException>();
    }
}
```

LivroManagerTests.cs:

```csharp
using Bogus;
using NSubstitute;
using Shouldly;
using TRF3.SISPREC.Autores;
using TRF3.SISPREC.Livros;
using Volo.Abp;
using TRF3.SISPREC.Enums;

namespace TRF3.SISPREC.Tests.Livros;

/// <summary>
/// Classe de testes específica para os métodos customizados do LivroManager.
/// Demonstra:
/// 1. Como estruturar testes para operações CRUD complexas
/// 2. Como validar regras de negócio relacionadas a entidades associadas
/// 3. Como trabalhar com múltiplos repositórios mockados
/// </summary>
public class LivroManagerTests : SISPRECDomainTestBase<SISPRECTestBaseModule>
{
    /// <summary>
    /// Mock do repositório de livros. Simula o comportamento do banco de dados
    /// para operações de CRUD de livros. Importante para validar se as operações
    /// de persistência estão sendo chamadas corretamente.
    /// </summary>
    private readonly ILivroRepository _repository;

    /// <summary>
    /// Instância do gerenciador que será testado (SUT - System Under Test).
    /// Responsável por coordenar as operações de negócio relacionadas a livros,
    /// incluindo validações e associações com autores.
    /// </summary>
    private readonly LivroManager _manager;

    /// <summary>
    /// Mock do repositório de autores. Fundamental para testar as validações
    /// de existência de autores e as operações de associação entre livros e autores.
    /// Demonstra como trabalhar com relacionamentos entre entidades nos testes.
    /// </summary>
    private readonly IAutorRepository _autorRepository;

    /// <summary>
    /// Construtor que configura o ambiente de teste inicializando os mocks necessários
    /// e injetando-os no gerenciador. Demonstra o padrão de injeção de dependências
    /// e como preparar o cenário base para todos os testes da classe.
    /// </summary>
    public LivroManagerTests()
    {
        _repository = Substitute.For<ILivroRepository>();
        _autorRepository = Substitute.For<IAutorRepository>();
        _manager = new LivroManager(_repository, _autorRepository);
    }

    /// <summary>
    /// Método auxiliar que cria um livro com dados faker válidos.
    /// Demonstra:
    /// 1. Como usar a biblioteca Bogus para gerar dados consistentes
    /// 2. Como definir regras específicas para cada propriedade
    /// 3. Como garantir que os dados gerados atendam às regras de negócio
    /// </summary>
    private Livro InicializarLivroComValoresValidos()
    {
        return new Faker<Livro>()
            .RuleFor(p => p.LivroId, p => p.Random.Int(min: 1))
            .RuleFor(p => p.Titulo, p => p.Random.Hash())
            .RuleFor(p => p.Categoria, p => p.Random.Enum<ECategoriaLivro>())
            .RuleFor(p => p.DataPublicacao, p => p.Date.Past())
            .RuleFor(p => p.Preco, p => p.Random.Decimal())
            .RuleFor(p => p.Descricao, p => p.Random.Hash())
            .Generate();
    }

    /// <summary>
    /// Verifica se o LivroManager valida corretamente a existência de todos os autores
    /// antes de permitir a inserção de um livro.
    /// </summary>
    [Fact]
    public async Task Validar_Existencia_Autores_Na_Insercao()
    {
        // Arrange
        var livro = InicializarLivroComValoresValidos();
        var autoresIds = new List<int> { 1, 2 };
        var autores = new List<Autor> { new Autor { AutorId = 1 } }; // Apenas 1 autor existe

        _autorRepository.GetQueryableAsync().Returns(Task.FromResult(autores.AsQueryable()));

        // Act & Assert
        await Should.ThrowAsync<UserFriendlyException>(async () =>
        {
            await _manager.InserirAsync(livro, autoresIds, true);
        });
    }

    /// <summary>
    /// Verifica se o LivroManager associa corretamente os autores ao livro
    /// quando todos os autores informados existem.
    /// </summary>
    [Fact]
    public async Task Associar_Autores_Ao_Livro_Na_Insercao()
    {
        // Arrange
        var livro = InicializarLivroComValoresValidos();
        var autoresIds = new List<int> { 1, 2 };
        var autores = autoresIds.Select(id => new Autor { AutorId = id }).ToList();

        _autorRepository.GetQueryableAsync().Returns(Task.FromResult(autores.AsQueryable()));

        // Act
        await _manager.InserirAsync(livro, autoresIds, true);

        // Assert
        livro.Autores.Count.ShouldBe(autores.Count);
        livro.Autores.Select(a => a.AutorId).ShouldBe(autoresIds);
    }

    /// <summary>
    /// Verifica se o LivroManager valida corretamente a existência de todos os autores
    /// antes de permitir a alteração de um livro.
    /// </summary>
    [Fact]
    public async Task Validar_Existencia_Autores_Na_Alteracao()
    {
        // Arrange
        var livro = InicializarLivroComValoresValidos();
        var autoresIds = new List<int> { 1, 2 };
        var autores = new List<Autor> { new Autor { AutorId = 1 } }; // Apenas 1 autor existe

        _autorRepository.GetQueryableAsync().Returns(Task.FromResult(autores.AsQueryable()));

        // Act & Assert
        await Should.ThrowAsync<UserFriendlyException>(async () =>
        {
            await _manager.AlterarAsync(livro, autoresIds, true);
        });
    }

    /// <summary>
    /// Verifica se o LivroManager atualiza corretamente a lista de autores do livro
    /// quando todos os autores informados existem.
    /// </summary>
    [Fact]
    public async Task Atualizar_Autores_Do_Livro_Na_Alteracao()
    {
        // Arrange
        var livro = InicializarLivroComValoresValidos();
        var autoresIds = new List<int> { 1, 2 };
        var autores = autoresIds.Select(id => new Autor { AutorId = id }).ToList();

        _autorRepository.GetQueryableAsync().Returns(Task.FromResult(autores.AsQueryable()));

        // Act
        await _manager.AlterarAsync(livro, autoresIds, true);

        // Assert
        livro.Autores.Count.ShouldBe(autores.Count);
        livro.Autores.Select(a => a.AutorId).ShouldBe(autoresIds);
    }
}

```


## Testes de Application Services

Principais aspectos dos testes de serviços de aplicação:

- **Objetivo**: Validar operações CRUD e regras de negócio na camada de aplicação

- **Características**:
    - Usam banco de dados em memória (SQLite)
    - Validam integração com repositórios
    - Testam fluxos completos das operações
    - Verificam transformações DTO <-> Entidade

- **Estrutura**:
    - Uma classe de teste por serviço de aplicação 
    - Herdam de `BaseAppServiceTests`
    - Organização em Arrange-Act-Assert
    - Uso de dados fictícios com Bogus

- **Práticas importantes**:
    - Usar `WithUnitOfWorkAsync` para operações de banco
    - Criar dados de teste no construtor da classe
    - Verificar estado do banco após operações
    - Testar cenários de erro e validações

Para mais detalhes sobre implementação de testes, consulte [Guia de Testes](/Guia-de-Testes.md).

AutorAppServiceTests.cs:

```csharp
using Bogus.Extensions.Brazil;
using Shouldly;
using TRF3.SISPREC.Autores.Dtos;
using TRF3.SISPREC.EntityFrameworkCore;
using TRF3.SISPREC.Enums;

namespace TRF3.SISPREC.Autores;

/// <summary>
/// Classe de testes para o serviço de aplicação de Autores.
/// Demonstra como testar operações CRUD usando banco de dados em memória.
/// Namespace: TRF3.SISPREC.Autores
/// Diretório: .\test\TRF3.SISPREC.EntityFrameworkCore.Tests\EntityFrameworkCore\Applications
/// </summary>
/// <remarks>
/// Esta classe herda de BaseAppServiceTests que fornece:
/// - Banco de dados SQLite em memória
/// - Injeção de dependência configurada
/// - Unit of Work para transações
/// 
/// Características dos testes:
/// - Usa Bogus para gerar dados de teste
/// - Valida todas as operações CRUD
/// - Verifica integridade dos dados após operações
/// - Testa paginação e ordenação
/// </remarks>
public class AutorAppServiceTests : BaseAppServiceTests<SISPRECEntityFrameworkCoreTestModule>
{
    /// <summary>
    /// Serviço de aplicação sendo testado
    /// </summary>
    private readonly IAutorAppService _appService;

    /// <summary>
    /// Repositório para verificações diretas no banco
    /// </summary>
    private readonly IAutorRepository _repository;

    /// <summary>
    /// Gerenciador para operações de domínio
    /// </summary>
    private readonly IAutorManager _autorManager;

    /// <summary>
    /// Autores de teste criados no construtor
    /// </summary>
    private Autor autorObj1;
    private Autor autorObj2;

    /// <summary>
    /// Construtor que prepara o ambiente de teste:
    /// 1. Obtém os serviços necessários via injeção de dependência
    /// 2. Configura o faker para gerar dados válidos
    /// 3. Cria dois autores no banco para os testes
    /// </summary>
    public AutorAppServiceTests()
    {
        _appService = GetRequiredService<IAutorAppService>();
        _repository = GetRequiredService<IAutorRepository>();
        _autorManager = GetRequiredService<IAutorManager>();

        WithUnitOfWorkAsync(async () =>
        {
            // Configurando o faker para autores com dados válidos
            var autorFaker = new Bogus.Faker<Autor>()
                .RuleFor(a => a.Nome, a => a.Random.Hash())
                .RuleFor(a => a.Sobrenome, a => a.Random.Hash())
                .RuleFor(a => a.GeneroBiologico, a => a.PickRandom<EGeneroBiologico>())
                .RuleFor(a => a.MunicipioId, SISPRECTestConsts.MunicipioId);

            // Gerando dois autores para usar nos testes
            autorObj1 = autorFaker.Generate();
            autorObj2 = autorFaker.Generate();

            // Inserindo os autores usando o manager
            await _autorManager.InserirAsync(autorObj1, true);
            await _autorManager.InserirAsync(autorObj2, true);
        })
        .Wait(); // Importante para lançar exceção se houver erro
    }

    /// <summary>
    /// Testa a criação de um novo autor.
    /// Verifica:
    /// 1. Se o autor é criado com sucesso
    /// 2. Se o ID é gerado corretamente
    /// 3. Se os dados são persistidos conforme o DTO
    /// </summary>
    [Fact]
    public async Task Criar_Autor_Deve_Passar()
    {
        // Arrange
        // Gera DTO com dados válidos usando Bogus
        var createUpdateAutorDto = new Bogus.Faker<CreateUpdateAutorDto>()
            .RuleFor(p => p.Nome, p => p.Random.Hash())
            .RuleFor(p => p.Sobrenome, p => p.Random.Hash())
            .RuleFor(p => p.GeneroBiologico, p => p.PickRandom<EGeneroBiologico>())
            .RuleFor(p => p.Biografia, p => p.Random.Hash())
            .RuleFor(p => p.Email, p => p.Internet.Email())
            .RuleFor(p => p.Cpf, p => p.Person.Cpf())
            .RuleFor(p => p.MunicipioId, SISPRECTestConsts.MunicipioId)
            .Generate();

        // Act
        var result = await _appService.CreateAsync(createUpdateAutorDto);

        // Assert
        result.ShouldNotBeNull();
        result.AutorId.ShouldBeGreaterThan(0);
        result.Nome.ShouldBe(createUpdateAutorDto.Nome);
        result.Sobrenome.ShouldBe(createUpdateAutorDto.Sobrenome);
        result.GeneroBiologico.ShouldBe(createUpdateAutorDto.GeneroBiologico.ToString());
        result.MunicipioId.ShouldBe((int)createUpdateAutorDto.MunicipioId);
    }

    /// <summary>
    /// Testa a atualização de um autor existente.
    /// Verifica:
    /// 1. Se o autor é atualizado com sucesso
    /// 2. Se todos os campos são atualizados corretamente
    /// 3. Se as relações são mantidas (Município)
    /// </summary>
    [Fact]
    public async Task Atualizar_Autor_Deve_Passar()
    {
        // Arrange
        // Gera DTO com novos dados usando Bogus
        var createUpdateAutorDto = new Bogus.Faker<CreateUpdateAutorDto>()
            .RuleFor(p => p.Nome, p => p.Random.Hash())
            .RuleFor(p => p.Sobrenome, p => p.Random.Hash())
            .RuleFor(p => p.GeneroBiologico, p => p.PickRandom<EGeneroBiologico>())
            .RuleFor(p => p.Biografia, p => p.Random.Hash())
            .RuleFor(p => p.MunicipioId, SISPRECTestConsts.MunicipioId)
            .Generate();

        // Act
        var result = await _appService.UpdateAsync(autorObj2.AutorId, createUpdateAutorDto);

        // Assert
        result.ShouldNotBeNull();
        result.AutorId.ShouldBe(autorObj2.AutorId);
        result.Nome.ShouldBe(createUpdateAutorDto.Nome);
        result.Sobrenome.ShouldBe(createUpdateAutorDto.Sobrenome);
        result.GeneroBiologico.ShouldBe(createUpdateAutorDto.GeneroBiologico.ToString());
        result.Biografia.ShouldBe(createUpdateAutorDto.Biografia);
        result.Email.ShouldBe(createUpdateAutorDto.Email);
        result.Telefone.ShouldBe(createUpdateAutorDto.Telefone);
        result.Website.ShouldBe(createUpdateAutorDto.Website);
        result.Municipio.MunicipioId.ShouldBe((int)createUpdateAutorDto.MunicipioId);
    }

    /// <summary>
    /// Testa a exclusão de um autor.
    /// Verifica:
    /// 1. Se o autor é excluído com sucesso
    /// 2. Se não é mais possível encontrá-lo no banco
    /// </summary>
    [Fact]
    public async Task Excluir_Autor_Deve_Passar()
    {
        // Arrange
        var autor = autorObj1;

        // Act
        await _appService.DeleteAsync(autor.AutorId);

        // Assert
        var autorDeletado = await _repository.FindAsync(a => a.AutorId == autor.AutorId);
        autorDeletado.ShouldBeNull();
    }

    /// <summary>
    /// Testa a busca de um autor por ID.
    /// Verifica:
    /// 1. Se o autor é encontrado
    /// 2. Se os dados retornados estão corretos
    /// </summary>
    [Fact]
    public async Task Obter_Autor_Por_Id_Deve_Passar()
    {
        // Arrange
        var autor = autorObj2;

        // Act
        var result = await _appService.GetAsync(autor.AutorId);

        // Assert
        result.ShouldNotBeNull();
        result.AutorId.ShouldBe(autor.AutorId);
    }

    /// <summary>
    /// Testa a listagem paginada de autores.
    /// Verifica:
    /// 1. Se a lista não está vazia
    /// 2. Se a paginação está funcionando
    /// 3. Se a ordenação está sendo aplicada
    /// </summary>
    [Fact]
    public async Task Obter_Lista_Autores_Deve_Passar()
    {
        // Arrange
        // Configura input com paginação e ordenação
        var input = new AutorGetListInput
        {
            MaxResultCount = 10,
            SkipCount = 0,
            Sorting = "AutorId"
        };

        // Act
        var result = await _appService.GetListAsync(input);

        // Assert
        result.Items.ShouldNotBeEmpty();
    }
}
```

LivroAppServiceTests.cs:

```csharp
using Shouldly;
using TRF3.SISPREC.Autores;
using TRF3.SISPREC.EntityFrameworkCore;
using TRF3.SISPREC.Livros.Dtos;
using TRF3.SISPREC.Enums;

namespace TRF3.SISPREC.Livros;

/// <summary>
/// Classe de testes para o serviço de aplicação de Livros (LivroAppService).
/// </summary>
public class LivroAppServiceTests : BaseAppServiceTests<SISPRECEntityFrameworkCoreTestModule>
{
    private readonly ILivroAppService _appService;
    private readonly ILivroRepository _repository;
    private readonly ILivroManager _livroManager;
    private Livro livroObj1;
    private Livro livroObj2;
    private int autorId1; // ID do primeiro autor
    private int autorId2; // ID do segundo autor

    /// <summary>
    /// Construtor da classe de testes.
    /// Configura os serviços necessários e cria dados de teste.
    /// </summary>
    public LivroAppServiceTests()
    {
        // Arrange: Configura os serviços necessários para os testes.
        _appService = GetRequiredService<ILivroAppService>();
        _repository = GetRequiredService<ILivroRepository>();
        _livroManager = GetRequiredService<ILivroManager>();

        // Arrange: Cria autores para serem usados nos testes de livros.
        var autorManager = GetRequiredService<IAutorManager>();

        var autorFaker = new Bogus.Faker<Autor>()
            .RuleFor(a => a.Nome, a => a.Random.Hash(10))
            .RuleFor(a => a.Sobrenome, a => a.Random.Hash(10))
            .RuleFor(a => a.MunicipioId, SISPRECTestConsts.MunicipioId);

        WithUnitOfWorkAsync(async () =>
        {
            // Arrange: Cria dois autores e salva seus IDs.
            autorId1 = autorManager.InserirAsync(autorFaker.Generate(), true).Result.AutorId;
            autorId2 = autorManager.InserirAsync(autorFaker.Generate(), true).Result.AutorId;

            // Arrange: Configura o faker para livros.
            var livroFaker = new Bogus.Faker<Livro>()
                .RuleFor(p => p.Titulo, p => p.Random.Hash())
                .RuleFor(p => p.Categoria, p => ECategoriaLivro.INFANTIL)
                .RuleFor(p => p.DataPublicacao, p => p.Date.Past())
                .RuleFor(p => p.Preco, p => p.Random.Decimal(1, 999.99M))
                .RuleFor(p => p.Descricao, p => p.Random.Hash())
                .RuleFor(p => p.Quantidade, p => p.Random.Int(1, 100))
                .RuleFor(p => p.Disponivel, p => true);

            // Arrange: Gera dois livros.
            livroObj1 = livroFaker.Generate();
            livroObj2 = livroFaker.Generate();

            // Arrange: Insere os livros usando o manager e associa autores.
            _livroManager.InserirAsync(livroObj1, new[] { autorId1 }, true).Wait();
            _livroManager.InserirAsync(livroObj2, new[] { autorId1, autorId2 }, true).Wait();
        })
        //Importante para lançar exceção se houver erro
        .Wait();
    }

    /// <summary>
    /// Teste para verificar se a criação de um livro é bem-sucedida.
    /// </summary>
    [Fact]
    public async Task Criar_Livro_Deve_Passar()
    {
        // Arrange: Cria um objeto CreateUpdateLivroDto com dados válidos.
        var input = new Bogus.Faker<CreateUpdateLivroDto>()
            .RuleFor(p => p.Titulo, p => p.Random.Hash(10))
            .RuleFor(p => p.Categoria, p => p.PickRandom<ECategoriaLivro>())
            .RuleFor(p => p.DataPublicacao, p => p.Date.Past()) // Garante data passada
            .RuleFor(p => p.Preco, p => Math.Min(p.Random.Decimal(1, 999.99M), 999.99M))
            .RuleFor(p => p.Descricao, p => p.Random.Hash(10))
            .RuleFor(p => p.Quantidade, p => p.Random.Int(1, 100))
            .RuleFor(p => p.Disponivel, p => true)
            .Generate();

        // Act: Chama o método CreateAsync do serviço de aplicação.
        var result = await _appService.CreateAsync(input);

        // Assert: Verifica se o resultado não é nulo e se as propriedades foram salvas corretamente.
        result.ShouldNotBeNull();
        result.LivroId.ShouldBeGreaterThan(0);
        result.Titulo.ShouldBe(input.Titulo);
        result.Categoria.ShouldBe(input.Categoria.ToString());
        result.DataPublicacao.ShouldBe(input.DataPublicacao);
        result.Preco.ShouldBe(input.Preco);
        result.Descricao.ShouldBe(input.Descricao);
        result.Quantidade.ShouldBe(input.Quantidade);
        result.Disponivel.ShouldBe(input.Disponivel);
    }

    /// <summary>
    /// Teste para verificar se a atualização de um livro é bem-sucedida.
    /// </summary>
    [Fact]
    public async Task Atualizar_Livro_Deve_Passar()
    {
        // Arrange: Obtém um livro existente e cria um objeto CreateUpdateLivroDto com dados válidos.
        var objetoOriginal = await _repository.GetAsync(l => l.LivroId == livroObj1.LivroId);
        var input = new Bogus.Faker<CreateUpdateLivroDto>()
            .RuleFor(p => p.Titulo, p => p.Random.Hash(10)) // Garante título não vazio e dentro do limite
            .RuleFor(p => p.Categoria, p => ECategoriaLivro.INFANTIL) // Categoria válida
            .RuleFor(p => p.DataPublicacao, p => DateTime.Today.AddDays(-1)) // Data no passado
            .RuleFor(p => p.Preco, p => Math.Round(p.Random.Decimal(0.01M, 999.99M), 2)) // Preço positivo
            .RuleFor(p => p.Descricao, p => p.Random.Hash(50)) // Descrição dentro do limite
            .RuleFor(p => p.Quantidade, p => p.Random.Int(0, 100)) // Quantidade positiva
            .RuleFor(p => p.Disponivel, p => true) // Valor booleano válido
            .RuleFor(p => p.AutoresIds, p => new List<int> { autorId2 }) // Lista não vazia de autores
            .Generate();

        // Act: Chama o método UpdateAsync do serviço de aplicação.
        var result = await _appService.UpdateAsync(objetoOriginal.LivroId, input);

        // Assert: Verifica se o resultado não é nulo e se as propriedades foram atualizadas corretamente.
        result.ShouldNotBeNull();
        result.LivroId.ShouldBeGreaterThan(0);
        result.Titulo.ShouldBe(input.Titulo);
        result.Categoria.ShouldBe(input.Categoria.ToString());
        result.DataPublicacao.ShouldBe(input.DataPublicacao);
        result.Preco.ShouldBe(input.Preco);
        result.Descricao.ShouldBe(input.Descricao);
        result.Quantidade.ShouldBe(input.Quantidade);
        result.Disponivel.ShouldBe(input.Disponivel);
        result.Autores.Select(a => a.AutorId).ShouldContain(autorId2);
    }

    /// <summary>
    /// Teste para verificar se a exclusão de um livro é bem-sucedida.
    /// </summary>
    [Fact]
    public async Task Excluir_Livro_Deve_Passar()
    {
        // Arrange: Cria um objeto Livro para ser excluído e o insere no repositório.
        var objetoParaExcluir = new Bogus.Faker<Livro>()
            .RuleFor(p => p.LivroId, p => p.Random.Int(min: 1))
            .RuleFor(p => p.Titulo, p => p.Random.Hash())
            .RuleFor(p => p.Categoria, p => p.PickRandom<ECategoriaLivro>())
            .RuleFor(p => p.DataPublicacao, p => p.Date.Past())
            .RuleFor(p => p.Preco, p => p.Finance.Amount())
            .RuleFor(p => p.Descricao, p => p.Random.Hash())
            .RuleFor(p => p.Quantidade, p => p.Random.Int())
            .RuleFor(p => p.Disponivel, p => true)
            .Generate();

        objetoParaExcluir = await _repository.InsertAsync(objetoParaExcluir, autoSave: true); // Usando o repositório

        // Act: Chama o método DeleteAsync do serviço de aplicação.
        await _appService.DeleteAsync(objetoParaExcluir.LivroId);

        // Assert: Verifica se o livro foi excluído do repositório.
        var objetoDeletado = await _repository.FindAsync(a => a.LivroId == objetoParaExcluir.LivroId); // Usando o repositório
        objetoDeletado.ShouldBeNull();
    }

    /// <summary>
    /// Teste para verificar se a obtenção de um livro por ID é bem-sucedida.
    /// </summary>
    [Fact]
    public async Task Obter_Livro_Por_Id_Deve_Passar()
    {
        // Arrange: Obtém um livro existente do repositório.
        var objetoExistente = await _repository.FindAsync(a => a.LivroId == livroObj1.LivroId); // Usando o repositório

        // Act: Chama o método GetAsync do serviço de aplicação.
        var result = await _appService.GetAsync(objetoExistente.LivroId);

        // Assert: Verifica se o resultado não é nulo e se o ID do livro é o mesmo do objeto existente.
        result.ShouldNotBeNull();
        result.LivroId.ShouldBe(objetoExistente.LivroId);
    }

    /// <summary>
    /// Teste para verificar se a obtenção de uma lista de livros com ordenação padrão é bem-sucedida.
    /// </summary>
    [Fact]
    public async Task Obter_Livro_Com_Ordenacao_Padrao_Deve_Passar()
    {
        // Arrange: Cria um objeto LivroGetListInput sem filtros específicos.
        var input = new LivroGetListInput();  // Sem filtros específicos para testar a ordenação padrão.

        // Act: Chama o método GetListAsync do serviço de aplicação.
        var result = await _appService.GetListAsync(input);

        // Assert: Verifica se a lista de livros não está vazia e se está ordenada por ID.
        result.Items.ShouldNotBeEmpty();
        result.Items.OrderBy(l => l.LivroId).SequenceEqual(result.Items).ShouldBeTrue();
    }

    /// <summary>
    /// Teste para verificar se a criação de um livro com título vazio falha.
    /// </summary>
    [Fact]
    public async Task Criar_Livro_Com_Titulo_Vazio_Deve_Falhar()
    {
        // Arrange: Cria um objeto CreateUpdateLivroDto com título vazio.
        var input = new CreateUpdateLivroDto
        {
            Titulo = string.Empty,
            Categoria = ECategoriaLivro.INFANTIL,
            DataPublicacao = DateTime.UtcNow.AddDays(-2),
            Preco = 10m,
            Descricao = "Livro sem título",
            Quantidade = 1,
            Disponivel = true
        };

        // Act & Assert: Verifica se uma exceção de validação é lançada ao tentar criar o livro.
        await Should.ThrowAsync<Volo.Abp.Validation.AbpValidationException>(() => _appService.CreateAsync(input));
    }

    /// <summary>
    /// Teste para verificar se a criação de um livro com preço negativo falha.
    /// </summary>
    [Fact]
    public async Task Criar_Livro_Com_Preco_Negativo_Deve_Falhar()
    {
        // Arrange: Cria um objeto CreateUpdateLivroDto com preço negativo.
        var input = new CreateUpdateLivroDto
        {
            Titulo = "Teste Preço Negativo",
            Categoria = ECategoriaLivro.INFANTIL,
            DataPublicacao = DateTime.UtcNow.AddDays(-5),
            Preco = -1m,
            Descricao = "Preço < 0",
            Quantidade = 5,
            Disponivel = true
        };

        // Act & Assert: Verifica se uma exceção de validação é lançada ao tentar criar o livro.
        await Should.ThrowAsync<Volo.Abp.Validation.AbpValidationException>(() => _appService.CreateAsync(input));
    }

    /// <summary>
    /// Teste para verificar se a criação de um livro com data de publicação futura falha.
    /// </summary>
    [Fact]
    public async Task Criar_Livro_Com_Data_Publicacao_Futura_Deve_Falhar()
    {
        // Arrange: Cria um objeto CreateUpdateLivroDto com data de publicação futura.
        var input = new CreateUpdateLivroDto
        {
            Titulo = "Teste Data Futura",
            Categoria = ECategoriaLivro.INFANTIL,
            DataPublicacao = DateTime.UtcNow.AddDays(2),
            Preco = 99m,
            Descricao = "Data acima de hoje",
            Quantidade = 10,
            Disponivel = true
        };

        // Act & Assert: Verifica se uma exceção de validação é lançada ao tentar criar o livro.
        await Should.ThrowAsync<Volo.Abp.Validation.AbpValidationException>(() => _appService.CreateAsync(input));
    }

    /// <summary>
    /// Teste para verificar se a criação de um livro com dados válidos é bem-sucedida.
    /// </summary>
    [Fact]
    public async Task Criar_Livro_Com_Dados_Validos_Deve_Passar()
    {
        // Arrange: Cria um objeto CreateUpdateLivroDto com dados válidos.
        var input = new CreateUpdateLivroDto
        {
            Titulo = "Livro de Teste",
            Categoria = ECategoriaLivro.INFANTIL,
            DataPublicacao = DateTime.Today.AddDays(-1),
            Preco = 49.90m,
            Descricao = "Descrição de teste",
            Quantidade = 10,
            Disponivel = true,
            AutoresIds = new List<int> { autorId1 }
        };

        // Act: Chama o método CreateAsync do serviço de aplicação.
        var result = await _appService.CreateAsync(input);

        // Assert: Verifica se o resultado não é nulo, se o ID do livro é maior que zero e se as propriedades foram salvas corretamente.
        result.ShouldNotBeNull();
        result.LivroId.ShouldBeGreaterThan(0);
        result.Titulo.ShouldBe(input.Titulo);
        result.Categoria.ShouldBe(input.Categoria.ToString());
        result.Autores.Count.ShouldBe(1);
    }

    /// <summary>
    /// Teste para verificar se a atualização de um livro com dados válidos é bem-sucedida.
    /// </summary>
    [Fact]
    public async Task Atualizar_Livro_Com_Dados_Validos_Deve_Passar()
    {
        // Arrange: Obtém um livro existente e cria um objeto CreateUpdateLivroDto com dados válidos.
        var livroExistente = await _repository.GetListAsync();
        var livroParaAtualizar = livroExistente.First();

        var input = new CreateUpdateLivroDto
        {
            Titulo = "Título Atualizado",
            Categoria = ECategoriaLivro.TECNOLOGIA,
            DataPublicacao = DateTime.Today.AddDays(-5),
            Preco = 89.90m,
            Descricao = "Nova descrição",
            Quantidade = 20,
            Disponivel = true,
            AutoresIds = new List<int> { autorId1, autorId2 }
        };

        // Act: Chama o método UpdateAsync do serviço de aplicação.
        var result = await _appService.UpdateAsync(livroParaAtualizar.LivroId, input);

        // Assert: Verifica se o resultado não é nulo e se as propriedades foram atualizadas corretamente.
        result.ShouldNotBeNull();
        result.Titulo.ShouldBe(input.Titulo);
        result.Categoria.ShouldBe(input.Categoria.ToString());
        result.Autores.Count.ShouldBe(2);
    }

    /// <summary>
    /// Teste para verificar se a criação de um livro com título inválido falha.
    /// </summary>
    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public async Task Criar_Livro_Com_Titulo_Invalido_Deve_Falhar(string tituloInvalido)
    {
        // Arrange: Cria um objeto CreateUpdateLivroDto com título inválido.
        var input = new CreateUpdateLivroDto
        {
            Titulo = tituloInvalido,
            Categoria = ECategoriaLivro.INFANTIL,
            DataPublicacao = DateTime.Today.AddDays(-1),
            Preco = 29.90m,
            Quantidade = 1,
            Disponivel = true
        };

        // Act & Assert: Verifica se uma exceção de validação é lançada ao tentar criar o livro.
        var exception = await Should.ThrowAsync<Volo.Abp.Validation.AbpValidationException>(
            async () => await _appService.CreateAsync(input)
        );
    }

    /// <summary>
    /// Teste para verificar se a criação de um livro com preço inválido falha.
    /// </summary>
    [Theory]
    [InlineData(-0.01)]
    [InlineData(-50.00)]
    public async Task Criar_Livro_Com_Preco_Invalido_Deve_Falhar(decimal precoInvalido)
    {
        // Arrange: Cria um objeto CreateUpdateLivroDto com preço inválido.
        var input = new CreateUpdateLivroDto
        {
            Titulo = "Livro Teste",
            Categoria = ECategoriaLivro.INFANTIL,
            DataPublicacao = DateTime.Today.AddDays(-1),
            Preco = precoInvalido,
            Quantidade = 1,
            Disponivel = true
        };

        // Act & Assert: Verifica se uma exceção de validação é lançada ao tentar criar o livro.
        var exception = await Should.ThrowAsync<Volo.Abp.Validation.AbpValidationException>(
            async () => await _appService.CreateAsync(input)
        );
    }

    /// <summary>
    /// Teste para verificar se a criação de um livro com data futura falha.
    /// </summary>
    [Fact]
    public async Task Criar_Livro_Com_Data_Futura_Deve_Falhar()
    {
        // Arrange: Cria um objeto CreateUpdateLivroDto com data futura.
        var input = new CreateUpdateLivroDto
        {
            Titulo = "Livro Futuro",
            Categoria = ECategoriaLivro.INFANTIL,
            DataPublicacao = DateTime.Today.AddDays(1),
            Preco = 29.90m,
            Quantidade = 1,
            Disponivel = true
        };

        // Act & Assert: Verifica se uma exceção de validação é lançada ao tentar criar o livro.
        var exception = await Should.ThrowAsync<Volo.Abp.Validation.AbpValidationException>(
            async () => await _appService.CreateAsync(input)
        );
    }

    /// <summary>
    /// Teste para verificar se a exclusão de um livro existente é bem-sucedida.
    /// </summary>
    [Fact]
    public async Task Excluir_Livro_Existente_Deve_Passar()
    {
        // Arrange: Obtém um livro existente do repositório.
        var livroExistente = await _repository.GetListAsync();
        var livroParaExcluir = livroExistente.First();

        // Act: Chama o método DeleteAsync do serviço de aplicação.
        await _appService.DeleteAsync(livroParaExcluir.LivroId);

        // Assert: Verifica se o livro foi excluído do repositório.
        var livroExcluido = await _repository.FindAsync(l => l.LivroId == livroParaExcluir.LivroId);
        livroExcluido.ShouldBeNull();
    }

    /// <summary>
    /// Teste para verificar se a busca de livros por filtros retorna resultados corretos.
    /// </summary>
    [Fact]
    public async Task Buscar_Livro_Por_Filtros_Deve_Retornar_Resultados_Corretos()
    {
        // Arrange: Cria um objeto LivroGetListInput com filtros específicos.
        var input = new LivroGetListInput
        {
            Categoria = ECategoriaLivro.INFANTIL,
            Disponivel = true
        };

        // Act: Chama o método GetListAsync do serviço de aplicação.
        var result = await _appService.GetListAsync(input);

        // Assert: Verifica se a lista de livros não está vazia e se todos os livros correspondem aos filtros.
        result.Items.ShouldNotBeEmpty();
        result.Items.ShouldAllBe(l =>
            l.Categoria == ECategoriaLivro.INFANTIL.ToString() &&
            l.Disponivel
        );
    }
}

```


## Boas Práticas

1. **Nomenclatura**
   - Use nomes descritivos que indicam o cenário testado
   - Siga o padrão `Deve_Fazer_Algo_Quando_Algo`
   - Use termos em português para manter consistência

2. **Arrange-Act-Assert**
   - Organize os testes em três seções claras
   
3. **Dados de Teste**
   - Use Bogus para gerar dados
     - Para strings use preferencialmente `p.Random.Hash()`, para evitar strings com caracteres especiais, que quebram a verificação de igualdade
   - Crie métodos auxiliares para dados comuns

4. **Mocks**
   - Em testes de AppServices, mock apenas o necessário

5. **Asserções**
   - Use Shouldly para verificar resultados esperados e não esperados


**[Próximo: Testes-Web](/Tutorial-de-Início/Testes/Testes-Web.md)**