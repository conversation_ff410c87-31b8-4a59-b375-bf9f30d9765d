# Formulários Dinâmicos

## Introdução

`abp-dynamic-form` cria um formulário bootstrap para um dado modelo C#.

Uso básico:

````xml
<abp-dynamic-form abp-model="@Model.MyDetailedModel"/>
````

Modelo:

````csharp
public class DynamicFormsModel : PageModel
{
    [BindProperty]
    public DetailedModel MyDetailedModel { get; set; }

    public List<SelectListItem> CountryList { get; set; } = new List<SelectListItem>
    {
        new SelectListItem { Value = "CA", Text = "Canada"},
        new SelectListItem { Value = "US", Text = "USA"},
        new SelectListItem { Value = "UK", Text = "United Kingdom"},
        new SelectListItem { Value = "RU", Text = "Russia"}
    };

    public void OnGet()
    {
            MyDetailedModel = new DetailedModel
            {
                Name = "",
                Description = "Lorem ipsum dolor sit amet.",
                IsActive = true,
                Age = 65,
                Day = DateTime.Now,
                MyCarType = CarType.Coupe,
                YourCarType = CarType.Sedan,
                Country = "RU",
                NeighborCountries = new List<string>() { "UK", "CA" }
            };
    }

    public class DetailedModel
    {
        [Required]
        [Placeholder("Enter your name...")]
        [Display(Name = "Name")]
        public string Name { get; set; }
        
        [TextArea(Rows = 4)]
        [Display(Name = "Description")]
        [InputInfoText("Describe Yourself")]
        public string Description { get; set; }

        [Required]
        [DataType(DataType.Password)]
        [Display(Name = "Password")]
        public string Password { get; set; }

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; }

        [Required]
        [Display(Name = "Age")]
        public int Age { get; set; }

        [Required]
        [Display(Name = "My Car Type")]
        public CarType MyCarType { get; set; }

        [Required]
        [AbpRadioButton(Inline = true)]
        [Display(Name = "Your Car Type")]
        public CarType YourCarType { get; set; }

        [DataType(DataType.Date)]
        [Display(Name = "Day")]
        public DateTime Day { get; set; }
        
        [SelectItems(nameof(CountryList))]
        [Display(Name = "Country")]
        public string Country { get; set; }
        
        [SelectItems(nameof(CountryList))]
        [Display(Name = "Neighbor Countries")]
        public List<string> NeighborCountries { get; set; }
    }

    public enum CarType
    {
        Sedan,
        Hatchback,
        StationWagon,
        Coupe
    }
}
````

## Demonstração

Veja a [página de demonstração de formulários dinâmicos](https://bootstrap-taghelpers.abp.io/Components/DynamicForms) para vê-lo em ação.

## Atributos

### abp-model

Define o modelo C# para o formulário dinâmico. As propriedades deste modal são transformadas em inputs no formulário.

### column-size

Aqui, use 'col-sm' para definir o tamanho. Ao definir esta propriedade, 'col-12' será adicionado ao mesmo tempo.

### submit-button

Pode ser `True` ou `False`.

Se `True`, um botão de envio será gerado na parte inferior do formulário.

O valor padrão é `False`.

### required-symbols

Pode ser `True` ou `False`.

Se `True`, os inputs obrigatórios terão um símbolo (*) que indica que são obrigatórios.

O valor padrão é `True`.

## Posicionamento do Conteúdo do Formulário

Por padrão, `abp-dynamic-form` limpa o html interno e coloca os inputs dentro dele. Se você quiser adicionar conteúdo adicional ao formulário dinâmico ou colocar os inputs em alguma área específica, você pode usar a tag `<abp-form-content />`. Esta tag será substituída pelo conteúdo do formulário e o restante do html interno da tag `abp-dynamic-form` permanecerá inalterado.

Uso:

````xml
<abp-dynamic-form abp-model="@Model.MyExampleModel">
    <div>
        Algum conteúdo....
    </div>
    <div class="input-area">
        <abp-form-content />
    </div>
    <div>
        Mais algum conteúdo....
    </div>
</abp-dynamic-form>
````

## Ordem dos Inputs

`abp-dynamic-form` ordena as propriedades pelo seu atributo `DisplayOrder` e depois pela ordem das propriedades na classe modelo.

O número padrão do atributo `DisplayOrder` é 10000 para cada propriedade.

Veja o exemplo abaixo:

````csharp
public class OrderExampleModel
{
    [DisplayOrder(10004)]
    public string Name{ get; set; }
    
    [DisplayOrder(10005)]
    public string Surname{ get; set; }

    //Padrão 10000
    public string EmailAddress { get; set; }

    [DisplayOrder(10003)]
    public string PhoneNumber { get; set; }

    [DisplayOrder(9999)]
    public string City { get; set; }
}
````

Neste exemplo, os campos de input serão exibidos nesta ordem: `City` > `EmailAddress` > `PhoneNumber` > `Name` > `Surname`.

## Ignorando uma propriedade

Por padrão, `abp-dynamic-form` gera input para cada propriedade na classe modelo. Se você quiser ignorar uma propriedade, use o atributo `DynamicFormIgnore`.

Veja o exemplo abaixo:

````csharp
        public class MyModel
        {
            public string Name { get; set; }

            [DynamicFormIgnore]
            public string Surname { get; set; }
        }
````

Neste exemplo, nenhum input será gerado para a propriedade `Surname`.

## Indicando Text box, Radio Group e Combobox

Se você leu o [documento de elementos de formulário](form-elements.md), você percebeu que as tags `abp-radio` e `abp-select` são muito semelhantes no modelo C#. Então, temos que usar o atributo `[AbpRadioButton()]` para dizer ao `abp-dynamic-form` quais de suas propriedades serão radio group e quais serão combobox. Veja o exemplo abaixo:

````xml
<abp-dynamic-form abp-model="@Model.MyDetailedModel"/>
````

Modelo:

````csharp
public class DynamicFormsModel : PageModel
{
    [BindProperty]
    public DetailedModel MyDetailedModel { get; set; }

    public List<SelectListItem> CountryList { get; set; } = new List<SelectListItem>
    {
        new SelectListItem { Value = "CA", Text = "Canada"},
        new SelectListItem { Value = "US", Text = "USA"},
        new SelectListItem { Value = "UK", Text = "United Kingdom"},
        new SelectListItem { Value = "RU", Text = "Russia"}
    };

    public void OnGet()
    {
            MyDetailedModel = new DetailedModel
            {
                ComboCarType = CarType.Coupe,
                RadioCarType = CarType.Sedan,
                ComboCountry = "RU",
                RadioCountry = "UK"
            };
    }

    public class DetailedModel
    {
        public CarType ComboCarType { get; set; }

        [AbpRadioButton(Inline = true)]
        public CarType RadioCarType { get; set; }
        
        [SelectItems(nameof(CountryList))]
        public string ComboCountry { get; set; }
        
        [AbpRadioButton()]
        [SelectItems(nameof(CountryList))]
        public string RadioCountry { get; set; }
    }

    public enum CarType
    {
        Sedan,
        Hatchback,
        StationWagon,
        Coupe
    }
}
````

Como você vê no exemplo acima:

* Se `[AbpRadioButton()]` for usado em uma propriedade **Enum**, será um radio group. Caso contrário, combobox.
* Se `[SelectItems()]` e `[AbpRadioButton()]` forem usados em uma propriedade, será um radio group.
* Se apenas `[SelectItems()]` for usado em uma propriedade, será um combobox.
* Se nenhum desses atributos for usado em uma propriedade, será uma text box.

## Localização

`abp-dynamic-form` também lida com a localização.

Por padrão, ele tentará encontrar as chaves de localização "DisplayName:{PropertyName}" ou "{PropertyName}" e definirá o valor de localização como o rótulo do input.

Você pode definir você mesmo usando o atributo `[Display()]` do Asp.Net Core. Você pode usar uma chave de localização neste atributo. Veja o exemplo abaixo:

````csharp
[Display(Name = "Name")]
public string Name { get; set; }
````

## Veja Também

* [Elementos de Formulário](form-elements.md)
