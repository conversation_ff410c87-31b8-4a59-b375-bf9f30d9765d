# Guia de Validação de Formulários no ABP Framework 8

[[_TOC_]]

## Introdução

Este guia apresenta as melhores práticas para implementação de validação de formulários em aplicações ABP Framework 8 usando Razor Pages e jQuery. O guia é baseado em exemplos reais do sistema SISPREC, especificamente nos módulos de Livros e Autores.

### Objetivos
- Implementar validação client-side e server-side
- Utilizar componentes ABP para formulários
- Implementar validação em modais AJAX
- Seguir as melhores práticas do ABP Framework

## Pré-requisitos

- ABP Framework 8
- ASP.NET Core 8
- jQuery Validation
- Bootstrap 5
- Conhecimento básico de Razor Pages

## Implementação

### 1. Configuração do ViewModel

O ViewModel é a base para a validação de formulários. Utilize Data Annotations para definir regras de validação:

```csharp
/// <summary>
/// ViewModel compartilhado entre os modais de criação e edição de livros.
/// Contém todas as propriedades necessárias para o formulário, com suas
/// respectivas validações e atributos de exibição.
/// </summary>
public class CreateEditLivroViewModel
{
    [Required(ErrorMessage = "O título é obrigatório")]
    [StringLength(LivroConsts.TITULO_TAMANHO_MAX,
        ErrorMessage = "O título deve ter no máximo {1} caracteres")]
    [Display(Name = "Título")]
    public string Titulo { get; set; } = string.Empty;

    [Required(ErrorMessage = "O preço é obrigatório")]
    [Range(1, 9999.99, ErrorMessage = "O preço deve estar entre {1} e {2}")]
    [Display(Name = "Preço")]
    [DataType(DataType.Currency)]
    public decimal Preco { get; set; }

    [Required(ErrorMessage = "A data de publicação é obrigatória")]
    [DataType(DataType.Date)]
    [Display(Name = "Data de Publicação")]
    [DisplayFormat(DataFormatString = "{0:dd/MM/yyyy}", 
        ApplyFormatInEditMode = true)]
    public DateTime DataPublicacao { get; set; }
}
```

#### Principais Data Annotations

- `[Required]`: Campo obrigatório
- `[StringLength]`: Limite de caracteres
- `[Range]`: Intervalo de valores
- `[DataType]`: Tipo de dado específico
- `[Display]`: Nome de exibição
- `[DisplayFormat]`: Formato de exibição
- `[UIHint]`: Dica de interface

### 2. Configuração da View

Use os tag helpers do ABP para criar formulários com validação integrada:

```html
<form id="NovoLivroForm" method="post" data-ajaxForm="true" asp-page="CreateModal" abp-model="ViewModel">
    <abp-modal scrollable="true" size="Large">
        <abp-modal-header title="Cadastro de Livro"></abp-modal-header>
        <abp-modal-body>
            <abp-row>
                <abp-column size="_6">
                    <abp-input asp-for="ViewModel.Titulo" 
                              placeholder="Digite o título do livro" />
                </abp-column>
                
                <abp-column size="_3">
                    <abp-select asp-for="ViewModel.Categoria"
                               asp-items="EnumExtensions.GetEnumSelectList<ECategoriaLivro>()"
                               placeholder="Selecione a categoria" />
                </abp-column>
            </abp-row>

            <abp-row>
                <abp-column size="_4">
                    <abp-date-picker asp-for="ViewModel.DataPublicacao" />
                </abp-column>
            </abp-row>
        </abp-modal-body>
        <abp-modal-footer buttons="@(AbpModalButtons.Cancel|AbpModalButtons.Save)">
        </abp-modal-footer>
    </abp-modal>
</form>
```

#### Tag Helpers do ABP

- `abp-input`: Campos de entrada com validação
- `abp-select`: Dropdowns com suporte a enums
- `abp-date-picker`: Seletor de data localizado
- `abp-modal`: Container para modais

### Required Symbols (Indicadores de Campo Obrigatório)

O ABP Framework oferece suporte nativo para exibição de símbolos de campo obrigatório (*) através dos tag helpers. Esta funcionalidade é automaticamente habilitada para campos marcados com o atributo `[Required]`.

#### Configuração Básica

```html
<abp-input asp-for="ViewModel.Titulo" required-symbol="true" />
```

### 3. Validação Server-Side

A validação no servidor é implementada no PageModel usando o método `ValidateModel()`:

```csharp
/// <summary>
/// Modelo da página Razor para o modal de criação de livros.
/// Herda de SISPRECPageModel para aproveitar funcionalidades comuns do ABP Framework.
/// </summary>
public class CreateModalModel : SISPRECPageModel
{
    [BindProperty]
    public CreateEditLivroViewModel ViewModel { get; set; } = new();

    private readonly ILivroAppService _service;

    public CreateModalModel(ILivroAppService service)
    {
        _service = service;
    }

    /// <summary>
    /// Método POST chamado quando o formulário é submetido.
    /// Valida o modelo, mapeia para DTO e chama o serviço de aplicação.
    /// </summary>
    public virtual async Task<IActionResult> OnPostAsync()
    {
        // Executa validação do modelo antes de prosseguir
        ValidateModel();

        // Mapeia ViewModel para DTO
        var dto = ObjectMapper.Map<CreateEditLivroViewModel, CreateUpdateLivroDto>(ViewModel);
        
        // Chama o serviço de aplicação
        await _service.CreateAsync(dto);
        
        return NoContent();
    }
}
```

O método `ValidateModel()` verifica:
- Data Annotations definidas no ViewModel
- Estado do ModelState
- Regras de validação customizadas
- Lança AbpValidationException em caso de erro

### 4. Validação em Modais

Para modais AJAX, configure:

1. **Formulário com AJAX**
```html
<form id="NovoLivroForm" method="post" data-ajaxForm="true" asp-page="CreateModal">
```

2. **JavaScript para Select2 e outros componentes**
```javascript
$(function () {
    // Configura o Select2 para seleção de autores
    $('#ViewModel_AutoresIds').select2({
        dropdownParent: $('#CreateModalLivro'),
        theme: 'bootstrap-5',
        minimumInputLength: 3,
        multiple: true,
        placeholder: 'Selecione um ou mais autores...',
        allowClear: true,
        ajax: {
            url: abp.appPath + 'api/app/autor/autor-por-nome',
            dataType: 'json',
            data: function (params) {
                return {
                    nomeAutor: params.term
                };
            },
            processResults: function (data) {
                return {
                    results: $.map(data, function (obj) {
                        return {
                            id: obj.autorId,
                            text: obj.nomeCompleto
                        };
                    })
                };
            }
        }
    });
});
```

## Melhores Práticas

1. **Validação em Camadas**
   - Use Data Annotations no ViewModel
   - Implemente ValidateModel() no PageModel
   - Mantenha validações complexas no domínio

2. **Mensagens de Erro**
   - Use mensagens claras e específicas
   - Mantenha consistência no tom e formato
   - Forneça orientação para correção

3. **Performance**
   - Evite validações complexas no client-side
   - Use validação remota quando necessário

4. **Acessibilidade**
   - Use atributos ARIA apropriados
   - Forneça feedback visual e textual

## Solução de Problemas

### Problemas Comuns

1. **Validação não funciona em modais**
   - Verifique se `data-ajaxForm="true"` está presente
   - Confirme que o jQuery Validation está carregado
   - Verifique conflitos de z-index

2. **ValidateModel não dispara**
   - Verifique se o modelo herda de SISPRECPageModel
   - Confirme que [BindProperty] está presente
   - Verifique se o formulário tem o atributo method="post"

3. **Select2 não valida**
   - Verifique a configuração do dropdownParent
   - Confirme que o campo está dentro do form
   - Verifique se o campo está marcado como Required

### Dicas de Depuração

1. Use o console do navegador para erros JavaScript
2. Verifique a rede para chamadas AJAX
3. Inspecione o HTML gerado para atributos de validação
4. Verifique logs do servidor para erros de validação