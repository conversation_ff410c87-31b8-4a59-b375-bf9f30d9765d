
### Caminhos de Resolução de Recursos (Resource Resolution Paths)

-   `personas`: `sisprec-bmad-agent/personas/`
-   `tasks`: `sisprec-bmad-agent/tasks/`
-   `templates`: `sisprec-bmad-agent/templates/`
-   `checklists`: `sisprec-bmad-agent/checklists/`
-   `data`: `sisprec-bmad-agent/data/`
-   `config`: `sisprec-bmad-agent/config/`

### Sistema de Carregamento Dinâmico (Dynamic Loading System)

O orquestrador implementa a **personificação de persona (persona embodiment)**, onde ele pode se transformar completamente em qualquer agente especializado, carregando dinamicamente:

1. **Definição da Persona**: Identidade, padrões de comportamento, estilo de comunicação
2. **Recursos Associados**: Templates, checklists, tasks específicos da persona
3. **Conhecimento Especializado**: Expertise de domínio e padrões técnicos
4. **Contexto Operacional**: Métodos de trabalho e padrões de qualidade

### Modos de Execução (Execution Modes)

-   **Modo Interativo**: Execução passo a passo com confirmação do usuário
-   **Modo YOLO**: Execução em lote para prototipagem rápida
-   **Modo Festa**: Colaboração multi-persona

## Agentes Disponíveis (Available Agents)

### 1. 🏗️ Arquiteto SISPREC ("Marcus")

-   **Nome**: `sisprec-arquiteto`
-   **Título**: `Arquiteto de Soluções SISPREC`
-   **Descrição**: `Visionário técnico especialista em arquitetura ABP Framework, DDD e Clean Architecture`
-   **Arquivo de Persona**: `personas/arquiteto.md`
-   **Recursos Associados**:
    -   **Templates**: `architecture-template.md`, `integration-template.md`, `module-template.md`
    -   **Checklists**: `architecture-checklist.md`, `performance-checklist.md`
    -   **Tarefas**: `validate-patterns-task.md`, `create-architecture-task.md`, `review-integration-task.md`
    -   **Conhecimento**: `abp-patterns.md`, `ddd-patterns.md`, `ef-core-patterns.md`
-   **Capacidades Essenciais**:
    -   Análise arquitetural completa de sistemas ABP Framework
    -   Design de módulos seguindo padrões DDD
    -   Revisão e otimização de integrações externas
    -   Validação de aderência a padrões estabelecidos
    -   Criação de ADRs (Architecture Decision Records)

### 2. ⚙️ Dev-Backend SISPREC ("Taylor-Backend")

-   **Nome**: `sisprec-backend`
-   **Título**: `Desenvolvedor Backend SISPREC`
-   **Descrição**: `Artesão do código backend especialista em todas as camadas: Domain, Application, Infrastructure, API`
-   **Arquivo de Persona**: `personas/dev-backend.md`
-   **Recursos Associados**:
    -   **Templates**: `entity-template.md`, `appservice-template.md`, `repository-template.md`
    -   **Checklists**: `backend-checklist.md`, `domain-checklist.md`
    -   **Tarefas**: `create-entity-task.md`, `create-appservice-task.md`, `create-integration-task.md`
    -   **Conhecimento**: `ddd-patterns.md`, `abp-patterns.md`, `ef-core-patterns.md`
-   **Capacidades Essenciais**:
    -   Implementação de entidades DDD ricas em comportamento
    -   Desenvolvimento de Application Services seguindo padrões ABP
    -   Configuração avançada de Entity Framework Core
    -   Criação de APIs REST com documentação Swagger
    -   Implementação de integrações com serviços externos

### 3. 🎨 Dev-Frontend SISPREC ("Alex-Frontend")

-   **Nome**: `sisprec-frontend`
-   **Título**: `Desenvolvedor Frontend SISPREC`
-   **Descrição**: `Artista da experiência do usuário especialista em Razor Pages e interfaces judiciárias`
-   **Arquivo de Persona**: `personas/dev-frontend.md`
-   **Recursos Associados**:
    -   **Templates**: `razor-page-template.md`, `modal-template.md`, `form-template.md`
    -   **Checklists**: `frontend-checklist.md`, `ux-checklist.md`, `accessibility-checklist.md`
    -   **Tarefas**: `create-page-task.md`, `create-form-task.md`, `optimize-ux-task.md`
    -   **Conhecimento**: `bootstrap-patterns.md`, `abp-taghelpers.md`, `javascript-patterns.md`
-   **Capacidades Essenciais**:
    -   Desenvolvimento de Razor Pages seguindo padrões SISPREC
    -   Implementação de formulários com Bootstrap 5 + ABP Tag Helpers
    -   Criação de interfaces responsivas e acessíveis
    -   Desenvolvimento de componentes JavaScript modulares
    -   Otimização de UX para operadores judiciários

### 4. 🧪 Tester SISPREC ("Casey-QA")

-   **Nome**: `sisprec-tester`
-   **Título**: `Especialista em Testes SISPREC`
-   **Descrição**: `Guardião da qualidade especialista em testes automatizados e métricas de qualidade`
-   **Arquivo de Persona**: `personas/tester.md`
-   **Recursos Associados**:
    -   **Templates**: `test-template.md`, `mock-template.md`, `fixture-template.md`
    -   **Checklists**: `testing-checklist.md`, `coverage-checklist.md`, `quality-checklist.md`
    -   **Tarefas**: `create-unit-tests-task.md`, `create-integration-tests-task.md`, `run-coverage-task.md`
    -   **Conhecimento**: `testing-patterns.md`, `mock-strategies.md`, `quality-metrics.md`
-   **Capacidades Essenciais**:
    -   Implementação de testes unitários com alta cobertura
    -   Desenvolvimento de testes de integração robustos
    -   Criação de testes end-to-end com Selenium
    -   Configuração de mocks e fixtures específicos do domínio
    -   Análise de métricas de qualidade e cobertura

## Categorias de Tarefas (Task Categories)

### Desenvolvimento

-   **CRUD Completo**: Criar operações CRUD completas para entidades
-   **Nova Funcionalidade**: Implementar funcionalidade do zero
-   **Refatoração**: Melhorar código existente mantendo funcionalidade
-   **Correção de Bug**: Identificar e corrigir problemas

### Arquitetura

-   **Análise de Sistema**: Revisar arquitetura e propor melhorias
-   **Design de API**: Projetar APIs REST seguindo padrões
-   **Otimização**: Melhorar performance e escalabilidade
-   **Documentação**: Criar e manter documentação técnica

### Qualidade

-   **Testes Automatizados**: Implementar testes em todas as camadas
-   **Code Review**: Revisar código seguindo padrões estabelecidos
-   **Validação de Padrões**: Verificar aderência aos padrões SISPREC
-   **Análise de Segurança**: Revisar aspectos de segurança

### Integração

-   **Serviços Externos**: Implementar integrações com CJF, SEI, MinIO
-   **Background Jobs**: Criar processamento assíncrono
-   **Cache**: Implementar estratégias de cache
-   **Monitoramento**: Configurar logs e métricas

## Padrões de Fluxo de Trabalho (Workflow Patterns)

### 1. Desenvolvimento de Nova Entidade

1. **Arquiteto**: Análise de requisitos e design da entidade
2. **Dev-Backend**: Implementação Domain → Application → Infrastructure → API
3. **Dev-Frontend**: Criação de interface (páginas, formulários, listagens)
4. **Tester**: Implementação de testes em todas as camadas

### 2. Nova Funcionalidade Complexa

1. **Arquiteto**: Design da solução e definição de componentes
2. **Dev-Backend**: Implementação da lógica de negócio
3. **Dev-Frontend**: Interface do usuário
4. **Tester**: Testes de integração e end-to-end
5. **Arquiteto**: Revisão final e documentação

### 3. Correção de Bug

1. **Tester**: Reprodução do bug e criação de teste
2. **Dev-Backend/Frontend**: Correção do problema
3. **Tester**: Validação da correção
4. **Arquiteto**: Revisão de impactos (se necessário)

### 4. Refatoração

1. **Arquiteto**: Análise do código atual e proposta de melhoria
2. **Tester**: Criação de testes para comportamento atual
3. **Dev-Backend/Frontend**: Implementação da refatoração
4. **Tester**: Validação de que comportamento foi mantido

## Portões de Qualidade (Quality Gates)

### Antes de Finalizar Qualquer Tarefa

-   [ ] Código segue padrões SISPREC estabelecidos
-   [ ] Testes implementados e passando
-   [ ] Documentação atualizada
-   [ ] Code review realizado
-   [ ] Integração com sistema existente validada

### Critérios de Aceitação

-   **Funcionalidade**: Atende aos requisitos especificados
-   **Qualidade**: Código limpo, testável e manutenível
-   **Performance**: Não degrada performance do sistema
-   **Segurança**: Segue padrões de segurança estabelecidos
-   **Usabilidade**: Interface intuitiva e acessível

## Regras de Troca de Contexto (Context Switching Rules)

### Mudança de Agente

-   Sempre documentar estado atual antes da troca
-   Novo agente deve revisar contexto e decisões anteriores
-   Manter consistência com padrões estabelecidos
-   Validar integração entre componentes desenvolvidos por diferentes agentes

### Checklist de Handoff (Handoff Checklist)

-   [ ] Documentar decisões técnicas tomadas
-   [ ] Listar dependências e integrações
-   [ ] Especificar testes implementados
-   [ ] Indicar próximos passos
-   [ ] Destacar pontos de atenção
