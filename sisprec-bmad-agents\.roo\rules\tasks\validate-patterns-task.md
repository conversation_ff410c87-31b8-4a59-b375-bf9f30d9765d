# Tarefa: Validar Padrões SISPREC

## Propósito

Executar validação abrangente da conformidade com os padrões arquiteturais, padrões de codificação e regras de negócio do SISPREC em todas as camadas da aplicação. Esta tarefa garante a aderência às convenções do ABP Framework, princípios DDD e requisitos específicos do SISPREC.

## Entradas para esta Tarefa

-   Componente ou módulo alvo para validar
-   Escopo da validação (Arquitetura | Backend | Frontend | Testes | Todos)
-   Modo de execução (Interativo | YOLO)
-   Checklist específico a aplicar (opcional)

## Instruções de Execução da Tarefa

### Passo 1: Inicializar Contexto de Validação

1.  **Carregar Base de Conhecimento SISPREC**

    -   Acessar `sisprec-bmad-agent/data/sisprec-kb.md`
    -   Carregar padrões do ABP Framework e princípios DDD
    -   Revisar regras de negócio específicas do SISPREC

2.  **Determinar Escopo da Validação**

    -   Se o escopo não for especificado, o padrão é "Todos"
    -   Mapear escopo para checklists apropriados:
        -   Arquitetura → `architecture-checklist.md`
        -   Backend → `backend-checklist.md`
        -   Frontend → `frontend-checklist.md`
        -   Testes → `testing-checklist.md`
        -   Todos → Todos os checklists

3.  **Definir Modo de Execução**
    -   Modo Interativo: Validação passo a passo com confirmação do usuário
    -   Modo YOLO: Validação em lote com relatório abrangente

### Passo 2: Validação de Padrão de Arquitetura

#### 2.1 Conformidade com Clean Architecture

**Modo Interativo**: Apresentar cada achado para revisão do usuário
**Modo YOLO**: Coletar todos os achados para relatório em lote

Validar:

-   [ ] Independência da camada de Domínio (sem dependências externas)
-   [ ] Abstração da camada de Aplicação (interfaces, não implementações)
-   [ ] Implementação da camada de Infraestrutura (classes concretas)
-   [ ] Separação da camada de Apresentação (sem lógica de negócio)
-   [ ] Direção do fluxo de dependência (apenas dependências para dentro)

**Script de Validação**:

```
Para cada projeto na solução:
  - Verificar referências do projeto
  - Analisar declarações `using`
  - Verificar organização de `namespace`
  - Validar responsabilidades da camada
```

#### 2.2 Validação de Padrão DDD (SISPREC)

Validar:

-   [ ] Entidades têm comportamento rico (não anêmicas) usando `Entity` ou `BaseEntidadeDominio`
-   [ ] **NUNCA usar Aggregates** - usar apenas `Entity` com relacionamentos simples
-   [ ] **NUNCA usar Value Objects** - usar tipos primitivos ou classes simples
-   [ ] Serviços de Domínio contêm lógica apropriada
-   [ ] Interfaces de Repositório na camada de domínio

#### 2.3 Integração com ABP Framework

Validar:

-   [ ] Configuração correta do Módulo
-   [ ] Injeção de dependência usada adequadamente
-   [ ] Sistema de permissão implementado
-   [ ] Trilha de auditoria (`audit trail`) funcionando

### Passo 3: Validação de Padrão de Backend

#### 3.1 Padrões da Camada de Domínio

**Verificar Padrões de Entidade (SISPREC)**:

```csharp
// Validar estrutura da entidade - Padrão SISPREC
[Audited]
public class EntityPattern : Entity, ISoftDelete
{
    // ✅ Deve usar anotação [Audited] para auditoria
    // ✅ Deve implementar ISoftDelete quando `soft delete` é necessário
    // ✅ Deve ter construtor protegido para EF Core
    // ✅ Deve ter construtor público com validações
    // ✅ Deve conter métodos de negócio
    // ✅ Deve manter invariantes
    // ✅ Propriedades de navegação devem usar [DisableAuditing]
    // ✅ Deve sobrescrever o método GetKeys()
}
```

**Verificar Padrões de BaseEntidadeDominio (SISPREC)**:

```csharp
// Validar estrutura de BaseEntidadeDominio - Padrão SISPREC
public class EntityPattern : BaseEntidadeDominio
{
    // ✅ Deve sobrescrever o método GetKeys()
    // ✅ Deve usar propriedades `required` quando apropriado
    // ✅ Deve conter métodos de negócio
    // ✅ Deve manter invariantes
}
```

#### 3.2 Padrões de Serviço de Aplicação (SISPREC)

**Verificar Seleção de AppService Base**:

-   [ ] Classe base correta selecionada com base nos requisitos da entidade:
    -   `BaseAppService` para operações de negócio personalizadas
    -   `BaseReadOnlyAppService` para entidades somente leitura
    -   `BaseCrudAppService` para operações CRUD completas
    -   `BaseCrudNoDeleteAppService` para entidades que não podem ser excluídas
    -   `BaseAtivaDesativaAppService` para entidades com estado ativo/inativo
    -   `BaseSincronizavelAppService` para entidades sincronizáveis com o CJF
    -   `SISPRECBaseSettingsAppService` para serviços de configuração

**Verificar Implementação de AppService**:

```csharp
// Validar estrutura do AppService - Padrão SISPREC
[DisableAuditing] // Quando apropriado
public class ExampleAppService : BaseCrudAppService<Entity, EntityDto, int, EntityGetListInput, CreateUpdateEntityDto>
{
    protected override string VisualizarPolicyName => SISPRECPermissoes.Entity.Visualizar;
    protected override string GravarPolicyName => SISPRECPermissoes.Entity.Gravar;

    private readonly IBaseDomainManager<Entity> _domainManager;

    public ExampleAppService(
        IRepository<Entity, int> repository,
        IBaseDomainManager<Entity> domainManager) : base(repository)
    {
        _domainManager = domainManager;
    }

    // ✅ Deve configurar propriedades de permissão
    // ✅ Deve injetar `domain manager` apropriado
    // ✅ Deve usar [DisableAuditing] quando necessário
    // ✅ Deve implementar métodos específicos do negócio
    // ✅ Deve tratar UserFriendlyException adequadamente
}
```

**❌ NUNCA usar Value Objects no SISPREC**:

```csharp
// ❌ PROIBIDO - Não usar Value Objects
// Usar tipos primitivos ou classes simples
```

#### 3.2 Padrões da Camada de Aplicação

**Verificar Padrões de Serviço de Aplicação**:

```csharp
// Validar estrutura do serviço de aplicação
[Authorize(SISPRECPermissions.Entity.Default)]
public class EntityAppService : CrudAppService<Entity, EntityDto, Guid>
{
    // ✅ Deve herdar de CrudAppService ou ApplicationService
    // ✅ Deve ter atributos de autorização adequados
    // ✅ Deve usar serviços de domínio para lógica de negócio
    // ✅ Não deve conter regras de negócio
}
```

#### 3.3 Padrões da Camada de Infraestrutura

**Verificar Padrões de Repositório**:

```csharp
// Validar implementação do repositório
public class EfEntityRepository : EfCoreRepository<SISPRECDbContext, Entity, Guid>, IEntityRepository
{
    // ✅ Deve implementar interface de domínio
    // ✅ Deve usar IQueryable apropriadamente
    // ✅ Deve evitar queries N+1
    // ✅ Deve implementar métodos específicos do domínio
}
```

### Passo 4: Validação de Padrão de Frontend

#### 4.1 Padrões de Razor Pages

Validar:

-   [ ] Usar formulários normais (não `abp-dynamic-form`)
-   [ ] Bootstrap 5 + Tag Helpers do ABP (sem CSS customizado)
-   [ ] Controles de filtro cabem em uma linha
-   [ ] Atributos `data-test` adequados para testes
-   [ ] Implementação de design responsivo

#### 4.2 Padrões de JavaScript

Validar:

-   [ ] Estrutura JavaScript modular
-   [ ] Uso de AJAX do ABP (`abp.ajax`, `abp.message`)
-   [ ] Tratamento de erro adequado
-   [ ] Integração com DataTables
-   [ ] Validação do lado do cliente (`client-side`)

### Passo 5: Validação de Regras de Negócio Específicas do SISPREC

#### 5.1 Regras de Negócio Críticas

**Padrão de Validação de Fase**:

```csharp
// ✅ Deve validar a conclusão da fase antes de prosseguir
if (!faseAtual.EstaFinalizada)
{
    throw new UserFriendlyException("A fase atual deve estar finalizada antes de prosseguir");
}
```

**Padrão de Controle de Proposta**:

```csharp
// ✅ Deve impedir verificação para propostas fechadas
if (proposta.Status == PropostaStatus.Fechada)
{
    throw new UserFriendlyException("Não é possível criar verificação para proposta fechada");
}
```

#### 5.2 Padrões de Integração

Validar:

-   [ ] Padrões de resiliência da integração com CJF
-   [ ] Tratamento de erro do serviço SEI
-   [ ] Padrões de armazenamento de arquivos do MinIO
-   [ ] Segurança da integração com sistema legado

### Passo 6: Validação de Padrão de Teste

#### 6.1 Validação da Estrutura de Teste

Validar:

-   [ ] Projetos de teste organizados por camada
-   [ ] Uso adequado de classes base de teste
-   [ ] Estratégias de `mocking` apropriadas
-   [ ] Isolamento e limpeza de teste

#### 6.2 Validação de Cobertura

Verificar:

-   [ ] Camada de Domínio: ≥90% de cobertura
-   [ ] Camada de Aplicação: ≥85% de cobertura
-   [ ] Camada de Infraestrutura: ≥70% de cobertura
-   [ ] Camada Web: ≥60% de cobertura

### Passo 7: Gerar Relatório de Validação

#### Relatório do Modo Interativo

Para cada seção de validação:

1.  Apresentar achados ao usuário
2.  Permitir discussão e esclarecimento
3.  Obter confirmação do usuário antes de prosseguir
4.  Documentar decisões e exceções

#### Relatório do Modo YOLO

Gerar relatório abrangente com:

-   **Resumo Executivo**: Status geral de conformidade
-   **Achados Detalhados**: Análise seção por seção
-   **Problemas Críticos**: Itens que devem ser corrigidos
-   **Recomendações**: Sugestões de melhoria
-   **Plano de Ação**: Passos de remediação priorizados

### Passo 8: Decisão do Portão de Qualidade (`Quality Gate`)

Com base nos resultados da validação:

-   **✅ APROVADO**: Todos os padrões críticos validados, problemas menores aceitáveis
-   **⚠️ PARCIAL**: Alguns problemas encontrados, requerem atenção mas não bloqueiam
-   **❌ REPROVADO**: Violações críticas de padrão encontradas, devem ser resolvidas
-   **N/A**: Seção não aplicável ao contexto atual

## Opções de Refinamento Avançado

Após concluir a validação, oferecer estas opções de refinamento:

1.  **Análise Profunda de Problemas Críticos**: Análise detalhada de validações reprovadas
2.  **Recomendações de Melhoria de Padrão**: Sugestões específicas para padrões melhores
3.  **Planejamento da Evolução da Arquitetura**: Roteiro de melhoria de longo prazo
4.  **Avaliação de Necessidades de Treinamento da Equipe**: Identificar lacunas de conhecimento
5.  **Configuração de Validação Automatizada**: Configurar verificação contínua de padrões
6.  **Atualização da Documentação**: Melhorar documentação de padrões
7.  **Catálogo de Melhores Práticas**: Criar biblioteca de exemplos de padrões
8.  **Finalizar Validação e Gerar Plano de Ação**

## Entregáveis

### Relatório de Validação

-   **Arquivo**: `docs/validation-reports/patterns-validation-{timestamp}.md`
-   **Conteúdo**: Análise abrangente com achados e recomendações

### Plano de Ação

-   **Arquivo**: `docs/action-plans/pattern-improvements-{timestamp}.md`
-   **Conteúdo**: Lista priorizada de melhorias com cronogramas

### Painel de Conformidade de Padrão

-   **Arquivo**: `docs/dashboards/pattern-compliance.md`
-   **Conteúdo**: Representação visual do status de conformidade

## Critérios de Sucesso

-   [ ] Todos os padrões críticos do SISPREC validados
-   [ ] Conformidade com regras de negócio verificada
-   [ ] Padrões do ABP Framework confirmados
-   [ ] Aderência aos princípios DDD verificada
-   [ ] Portões de qualidade (`quality gates`) implementados adequadamente
-   [ ] Recomendações acionáveis fornecidas
-   [ ] Alinhamento da equipe sobre padrões alcançado

## Ações de Acompanhamento

1.  **Imediato**: Resolver violações críticas de padrão
2.  **Curto prazo**: Implementar melhorias recomendadas
3.  **Médio prazo**: Estabelecer validação automatizada de padrões
4.  **Longo prazo**: Evoluir padrões com base no crescimento do projeto

Esta tarefa garante que a base de código do SISPREC mantenha alta qualidade arquitetural e consistência com os padrões estabelecidos e requisitos de negócio.
