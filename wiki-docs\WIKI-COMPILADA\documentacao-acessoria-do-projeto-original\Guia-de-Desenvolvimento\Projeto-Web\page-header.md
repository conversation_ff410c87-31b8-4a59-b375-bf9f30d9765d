# ASP.NET Core MVC / Razor Pages: Cabeça<PERSON><PERSON> da Página

O serviço `IPageLayout` pode ser usado para definir o título da página, o item de menu selecionado e os itens de breadcrumb para uma página. É responsabilidade do [tema](theming.md) renderizá-los na página.

## IPageLayout

`IPageLayout` pode ser injetado em qualquer página/view para definir as propriedades do cabeçalho da página.

### Título da Página

O título da página pode ser definido como mostrado no exemplo abaixo:

```csharp
@inject IPageLayout PageLayout
@{
    PageLayout.Content.Title = "Lista de Livros";
}
```

* O Título da Página é definido na tag HTML `title` (além do [nome da marca/aplicação](branding.md)).
* O tema pode renderizar o Título da Página antes do Conteúdo da Página (não implementado pelo Tema Básico).

### Breadcrumb

> **O [Tema Básico](basic-theme.md) atualmente não implementa os breadcrumbs.**
>
> O [Tema LeptonX Lite](../../../ui-themes/lepton-x-lite/asp-net-core.md) suporta breadcrumbs.

Os itens de breadcrumb podem ser adicionados ao `PageLayout.Content.BreadCrumb`.

**Exemplo: Adicionar Gerenciamento de Idioma aos itens de breadcrumb.**

```
PageLayout.Content.BreadCrumb.Add("Gerenciamento de Idioma");
```

O tema então renderiza o breadcrumb. Um exemplo de resultado da renderização pode ser:

![breadcrumbs-example](/ABP-Docs/images/breadcrumbs-example.png)

* O ícone Home é renderizado por padrão. Defina `PageLayout.Content.BreadCrumb.ShowHome` para `false` para ocultá-lo.
* O nome da Página Atual (obtido de `PageLayout.Content.Title`) é adicionado como o último por padrão. Defina `PageLayout.Content.BreadCrumb.ShowCurrent` para `false` para ocultá-lo.

Qualquer item que você adicionar é inserido entre os itens Home e Página Atual. Você pode adicionar quantos itens precisar. O método `BreadCrumb.Add(...)` recebe três parâmetros:

* `text`: O texto a ser exibido para o item de breadcrumb.
* `url` (opcional): Uma URL para navegar, se o usuário clicar no item de breadcrumb.
* `icon` (opcional): Uma classe de ícone (como `fas fa-user-tie` para Font-Awesome) para exibir com o `text`.

### O Item de Menu Selecionado

> **O [Tema Básico](basic-theme.md) atualmente não implementa o item de menu selecionado, pois não é aplicável ao menu superior, que é a única opção para o Tema Básico por enquanto.**
>
> O [Tema LeptonX Lite](../../../ui-themes/lepton-x-lite/asp-net-core.md) suporta o item de menu selecionado.

Você pode definir o nome do Item de Menu relacionado a esta página:

```csharp
PageLayout.Content.MenuItemName = "BookStore.Books";
```

O nome do item de menu deve corresponder a um nome de item de menu exclusivo definido usando o sistema [Navegação / Menu](navigation-menu.md). Neste caso, espera-se que o tema torne o item de menu "ativo" no menu principal.
