[[_TOC_]]
# Introdução

Este tutorial tem como objetivo implementar operações CRUD para duas entidades relacionadas, **Autor** e **Livro**, demonstrando a arquitetura do sistema e passando por todas as camadas da aplicação. Ao longo do tutorial, abordaremos desde a configuração inicial até a implementação completa das funcionalidades.

## Índice

1. [Projeto Domain.Shared](/Tutorial-de-Início/Projeto-Domain.Shared)
2. [Projeto Domain](/Tutorial-de-Início/Projeto-Domain)
3. [Projeto EntityFrameworkCore](/Tutorial-de-Início/Projeto-EFCore)
4. [Projeto Application.Contracts](/Tutorial-de-Início/Projeto-Application.Contracts)
5. [Projeto Application](/Tutorial-de-Início/Projeto-Application)
6. [Projeto Web](/Tutorial-de-Início/Projeto-Web)
7. [Testes](/Tutorial-de-Início/Testes)

## Estrutura do Tutorial

O tutorial está organizado em etapas progressivas, cada uma focando em uma camada específica da aplicação:

1. **Domain.Shared (Camada Compartilhada)**
   - Definição de constantes, enums e tipos básicos
   - Configuração de validações e regras compartilhadas
   - Classes Helpers úteis em todas camadas

2. **Domain (Camada de Domínio)**
   - Criação das entidades Autor e Livro
   - Definição de interfaces de repositório, serviços de infraestrutura.
   - Serviços de domínio e managers
     - Definição de interfaces e implementações
   - Definição de regras de negócio

3. **EntityFrameworkCore (Camada de Persistência)**
   - Configuração do contexto do banco de dados
   - Mapeamento das entidades usando Fluent API
   - Implementação dos repositórios
   - Configuração de convenções de nomenclatura

4. **Application.Contracts (Contratos da Aplicação)**
   - Implementação/Criação de DTOs
      - Implementação de validações via Data Annotations
   - Criação de interfaces de serviços de aplicação

5. **Application (Camada de Aplicação)**
   - Implementação dos serviços de aplicação
   - Desenvolvimento de lógica de negócio
   - Configuração do AutoMapper
   - Tratamento de operações CRUD

6. **Web (Interface do Usuário)**
   - Desenvolvimento de páginas Razor
   - Implementação de formulários e validações
   - Configuração de rotas e controllers
   - Integração com JavaScript e UI

7. **Testes (Qualidade e Validação)**
   - Implementação de testes de unidade
   - Desenvolvimento de testes de integração
   - Configuração de mocks
   - Validação de regras de negócio

Cada seção do tutorial inclui:
- Explicações detalhadas dos conceitos
- Exemplos práticos de código
- Melhores práticas e padrões
- Dicas de implementação

**[Próximo: Domain.Shared](/Tutorial-de-Início/Projeto-Domain.Shared)**