# UI para ASP.NET Core MVC / Razor Pages: API JavaScript de Eventos

O objeto `abp.event` é um serviço simples que é usado para publicar e se inscrever em eventos globais **no navegador**.

> Esta API não está relacionada a eventos locais ou distribuídos do lado do servidor. Ela funciona nos limites do navegador para fazer com que os componentes da UI (partes do código) se comuniquem de forma fracamente acoplada.

## Uso Básico

### Publicando Eventos

Use `abp.event.trigger` para publicar eventos.

**Exemplo: Publicar um evento *Cesta Atualizada***

```js
abp.event.trigger('basketUpdated');
```

Isso acionará todos os callbacks inscritos.

### Inscrevendo-se nos Eventos

Use `abp.event.on` para se inscrever em eventos.

**Exemplo: Consumir o evento *Cesta Atualizada***

```js
abp.event.on('basketUpdated', function() {
  console.log('Manipulou o evento basketUpdated...');
});
```

Você começa a receber eventos depois de se inscrever no evento.

### Cancelando a Inscrição nos Eventos

Se você precisar cancelar a inscrição em um evento pré-inscrito, você pode usar a função `abp.event.off(eventName, callback)`. Neste caso, você tem o callback como uma declaração de função separada.

**Exemplo: Inscrever e Cancelar a Inscrição**

```js
function onBasketUpdated() {
  console.log('Manipulou o evento basketUpdated...');
}

//Inscrever
abp.event.on('basketUpdated', onBasketUpdated);

//Cancelar a Inscrição
abp.event.off('basketUpdated', onBasketUpdated);
```

Você não recebe eventos depois de cancelar a inscrição no evento.

## Argumentos de Evento

Você pode passar argumentos (de qualquer contagem) para o método `trigger` e obtê-los no callback de inscrição.

**Exemplo: Adicionar a cesta como o argumento do evento**

```js
//Inscrever no evento
abp.event.on('basketUpdated', function(basket) {
  console.log('O novo objeto cesta: ');
  console.log(basket);
});

//Acionar o evento
abp.event.trigger('basketUpdated', {
  items: [
    {
      "productId": "123",
      "count": 2
    },
    {
      "productId": "832",
      "count": 1
    }
  ]
});
```

### Múltiplos Argumentos

Se você quiser passar múltiplos argumentos, você pode passar como `abp.event.on('basketUpdated', arg0, arg1, agr2)`. Então você pode adicionar a mesma lista de argumentos para a função de callback no lado do assinante.

> **Dica:** Alternativamente, você pode enviar um único objeto que tenha um campo separado para cada argumento. Isso torna mais fácil estender/alterar os argumentos do evento no futuro sem quebrar os assinantes.
