---
description: Diretrizes para criar e manter regras Roo Code para garantir consistência e efetividade.
globs: .roo/rules/*.md
alwaysApply: true
---

- **Estrutura Obrigatória da Regra:**
  ```markdown
  ---
  description: Descrição clara e objetiva do que a regra exige
  globs: caminho/para/arquivos/*.ext, outro/caminho/**/*
  alwaysApply: boolean
  ---

  - **Pontos principais em negrito**
    - Subpontos com detalhes
    - Exemplos e explicações
  ```

- **Referências de Arquivo:**
  - Use `[nome-do-arquivo](mdc:caminho/para/arquivo)` ([nome-do-arquivo](mdc:nome-do-arquivo)) para referenciar arquivos
  - Exemplo: [prisma.md](mdc:.roo/rules/prisma.md) para referência de regras
  - Exemplo: [schema.prisma](mdc:prisma/schema.prisma) para referência de código

- **Exemplos de Código:**
  - Use blocos de código específicos da linguagem
  ```typescript
  // ✅ FAÇA: Mostre bons exemplos
  const exemploBom = true;
  
  // ❌ NÃO FAÇA: Mostre anti-padrões
  const exemploRuim = false;
  ```

- **Diretrizes para Conteúdo das Regras:**
  - Comece com uma visão geral de alto nível
  - Inclua requisitos específicos e acionáveis
  - Mostre exemplos de implementação correta
  - Referencie código existente sempre que possível
  - Mantenha as regras DRY referenciando outras regras

- **Manutenção das Regras:**
  - Atualize as regras quando surgirem novos padrões
  - Adicione exemplos do código real do projeto
  - Remova padrões desatualizados
  - Faça referência cruzada entre regras relacionadas

- **Boas Práticas:**
  - Use listas para maior clareza
  - Mantenha descrições concisas
  - Inclua exemplos DO e NÃO FAÇA
  - Referencie código real em vez de exemplos teóricos
  - Use formatação consistente entre as regras