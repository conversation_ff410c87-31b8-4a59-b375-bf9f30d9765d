# API JavaScript

O ABP fornece um conjunto de APIs JavaScript para aplicações ASP.NET Core MVC / Razor Pages. Elas podem ser usadas para realizar facilmente requisitos comuns da aplicação no lado do cliente e integrar ao lado do servidor.

## APIs

* [Ajax](JavaScript/Ajax.md)
* [Autenticação](JavaScript/Autenticação.md)
* [Loading (Block UI)](JavaScript/Loading-(Block-UI).md)
* [Carregar Scripts](JavaScript/Carregar-Scripts.md)
* [Configurações (Settings)](JavaScript/Configurações-(Settings).md)
* [DOM](JavaScript/DOM.md)
* [Eventos](JavaScript/Eventos.md)
* [Notificações](JavaScript/Notificações.md)
* [Logging](JavaScript/Logging.md)
* [Usuário Atual](JavaScript/CurrentUser.md)
