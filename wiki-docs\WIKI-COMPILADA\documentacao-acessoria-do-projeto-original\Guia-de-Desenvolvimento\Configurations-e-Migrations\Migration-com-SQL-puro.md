[[_TOC_]]
Caso seja estritamente necessário executar alguma alteração no banco de dados com SQL puro, é possível usar duas abordagens em dois cenários:
- Grande número de linhas: criar um arquivo .sql separado, obter seu conteúdo e aplicar na migration como foi feito na migration `20241114201748_MigracaoInicial.cs`
- Pouco número de linhas: criar o sql e atribuir a uma variável dentro da própria migration. Exemplo: `20241118202423_CorrecaoNomeTabelaContratual.cs`