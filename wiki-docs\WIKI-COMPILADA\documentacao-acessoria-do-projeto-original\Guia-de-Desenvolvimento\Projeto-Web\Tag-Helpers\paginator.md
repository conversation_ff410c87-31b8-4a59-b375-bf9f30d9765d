# Paginator

## Introdução

`abp-paginator` é a tag abp para paginação. Requer um model do tipo `Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Pagination.PagerModel`.

Uso básico:

```xml
<abp-paginator model="Model.PagerModel" show-info="true"></abp-paginator>
```

Model:

```csharp
using Microsoft.AspNetCore.Mvc.RazorPages;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Pagination;

namespace Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.Demo.Pages.Components
{
    public class PaginatorModel : PageModel
    {
        public PagerModel PagerModel { get; set; }

        public void OnGet(int currentPage, string sort)
        {
            PagerModel = new PagerModel(100, 10, currentPage, 10, "/Components/Paginator", sort);
        }
    }
}
```

## Demo

Veja a [página de demonstração do paginator](https://bootstrap-taghelpers.abp.io/Components/Paginator) para vê-lo em ação.

## Atributos

### model

Um model do tipo `Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Pagination.PagerModel` pode ser inicializado com os seguintes dados:

*   `totalCount`
*   `shownItemsCount`
*   `currentPage`
*   `pageSize`
*   `pageUrl`
*   `sort` (padrão null)

### show-info

Um valor que indica se uma informação extra sobre o início, fim e total de registros será exibida. Deve ser um dos seguintes valores:

*   `false` (valor padrão)
*   `true`
