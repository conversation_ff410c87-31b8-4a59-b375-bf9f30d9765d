# Domain

O projeto Domain contém as entidades de domínio, interfaces de repositório, serviços de domínio e regras de negócio. Este projeto é o coração da aplicação, onde as regras e lógicas de negócio são implementadas.

## Estrutura do Projeto

O projeto Domain está organizado em namespaces que agrupam as classes relacionadas:

```
src/TRF3.SISPREC.Domain/
└── Autores/
    ├── Autor.cs
    ├── IAutorRepository.cs
    ├── IAutorManager.cs
    ├── IValidarExcluirAutorService.cs
    └── Servicos/
        ├── AutorManager.cs
        └── ValidarExcluirAutorService.cs
└── Livros/
    ├── Livro.cs
    ├── ILivroRepository.cs
    ├── ILivroManager.cs
    └── Servicos/
        └── LivroManager.cs
```

## Entidades

As entidades são classes que representam os objetos principais do domínio. Elas encapsulam o estado e o comportamento do negócio.

Autor
```csharp
using System.ComponentModel;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.Livros;
using TRF3.SISPREC.Municipios;
using Volo.Abp.Auditing;
using Volo.Abp.Domain.Entities;

namespace TRF3.SISPREC.Autores;

/// <summary>
/// Classe Autor no namespace TRF3.SISPREC.Autores.
/// Diretório relativo: src/TRF3.SISPREC.Domain/Autores
/// 
/// Representa a entidade de domínio para autores no sistema.
/// 
/// A annotation [Audited] do ABP Framework habilita o rastreamento automático
/// de alterações nesta entidade. Quando uma propriedade é modificada, o framework
/// registra automaticamente:
/// - Quem fez a alteração (UserId)
/// - Quando a alteração foi feita (DateTime)
/// - O que foi alterado (propriedades antigas e novas)
/// 
/// Note que a propriedade Livros está marcada com [DisableAuditing] para evitar
/// o rastreamento da coleção, que poderia causar problemas de performance.
/// </summary>
[Audited]
public class Autor : Entity
{
    /// <summary>
    /// Sobrescreve o método GetKeys da classe Entity do ABP Framework.
    /// Retorna um array com a chave primária da entidade (AutorId).
    /// Este método é usado internamente pelo framework para identificação única da entidade.
    /// </summary>
    /// <returns>Array contendo o AutorId como chave primária</returns>
    public override object[] GetKeys()
    {
        return new object[] { AutorId };
    }

    /// <summary>
    /// Identificador único do autor no sistema.
    /// Chave primária da entidade.
    /// </summary>
    [DisplayName("AutorId")]
    public int AutorId { get; set; }

    /// <summary>
    /// Nome do autor.
    /// Tamanho máximo definido em AutorConsts.NOME_TAMANHO_MAX.
    /// </summary>
    [DisplayName("Nome")]
    public string Nome { get; set; }

    /// <summary>
    /// Sobrenome do autor.
    /// Tamanho máximo definido em AutorConsts.SOBRENOME_TAMANHO_MAX.
    /// </summary>
    [DisplayName("Sobrenome")]
    public string Sobrenome { get; set; }

    /// <summary>
    /// Gênero biológico do autor.
    /// Usa o enum EGeneroBiologico que possui Description para exibição amigável.
    /// </summary>
    [DisplayName("Gênero Biológico")]
    public EGeneroBiologico GeneroBiologico { get; set; }

    /// <summary>
    /// Biografia do autor.
    /// Campo opcional com tamanho máximo definido em AutorConsts.BIOGRAFIA_TAMANHO_MAX.
    /// </summary>
    [DisplayName("Biografia")]
    public string? Biografia { get; set; }

    /// <summary>
    /// Email de contato do autor.
    /// Campo opcional com tamanho máximo definido em AutorConsts.EMAIL_TAMANHO_MAX.
    /// </summary>
    [DisplayName("Email")]
    public string? Email { get; set; }

    /// <summary>
    /// Telefone de contato do autor.
    /// Campo opcional com tamanho máximo definido em AutorConsts.TELEFONE_TAMANHO_MAX.
    /// </summary>
    [DisplayName("Telefone")]
    public string? Telefone { get; set; }

    /// <summary>
    /// CPF do autor.
    /// Campo opcional com tamanho máximo definido em AutorConsts.CPF_TAMANHO_MAX.
    /// </summary>
    [DisplayName("CPF")]
    public string? Cpf { get; set; }

    /// <summary>
    /// Website pessoal ou profissional do autor.
    /// Campo opcional com tamanho máximo definido em AutorConsts.WEBSITE_TAMANHO_MAX.
    /// </summary>
    [DisplayName("Website")]
    public string? Website { get; set; }

    /// <summary>
    /// ID do município de naturalidade do autor.
    /// Chave estrangeira para a entidade Municipio.
    /// </summary>
    [DisplayName("Naturalidade")]
    public int MunicipioId { get; set; }

    /// <summary>
    /// Município de naturalidade do autor.
    /// Propriedade de navegação carregada via Entity Framework Core.
    /// </summary>
    [DisplayName("Município")]
    public virtual Municipio Municipio { get; set; }

    /// <summary>
    /// Coleção de livros escritos pelo autor.
    /// Relacionamento muitos-para-muitos com a entidade Livro.
    /// Marcado com [DisableAuditing] para evitar problemas de performance.
    /// </summary>
    [DisplayName("Livros")]
    [DisableAuditing]
    public virtual ICollection<Livro> Livros { get; set; }
}
```
Livro 
```csharp
using System.ComponentModel;
using TRF3.SISPREC.Autores;
using TRF3.SISPREC.Enums;
using Volo.Abp.Domain.Entities;

namespace TRF3.SISPREC.Livros;

/// <summary>
/// Entidade que representa um livro no sistema.
/// Namespace: TRF3.SISPREC.Livros
/// Diretório: src/TRF3.SISPREC.Domain/Livros
/// 
/// Esta classe implementa Entity do ABP Framework e representa
/// a entidade principal do domínio de livros. Contém todas as
/// informações relevantes sobre um livro, incluindo seu título,
/// categoria, preço, disponibilidade e relacionamento com autores.
/// </summary>
public class Livro : Entity
{
    /// <summary>
    /// Sobrescreve o método GetKeys do ABP Framework para definir
    /// a chave primária da entidade como LivroId.
    /// </summary>
    /// <returns>Array contendo o LivroId como chave primária</returns>
    public override object[] GetKeys()
    {
        return new object[] { LivroId };
    }

    /// <summary>
    /// Identificador único do livro (chave primária)
    /// </summary>
    [DisplayName("LivroId")]
    public int LivroId { get; set; }

    /// <summary>
    /// Título do livro - limitado por LivroConsts.TITULO_TAMANHO_MAX
    /// </summary>
    [DisplayName("Título")]
    public string Titulo { get; set; } = string.Empty;

    /// <summary>
    /// Categoria do livro - enum que define os tipos possíveis de livros
    /// </summary>
    [DisplayName("Categoria")]
    public ECategoriaLivro Categoria { get; set; }

    /// <summary>
    /// Data em que o livro foi publicado
    /// </summary>
    [DisplayName("Data de Publicação")]
    public DateTime DataPublicacao { get; set; }

    /// <summary>
    /// Preço de venda do livro
    /// </summary>
    [DisplayName("Preço")]
    public decimal Preco { get; set; }

    /// <summary>
    /// Descrição detalhada do livro - limitado por LivroConsts.DESCRICAO_TAMANHO_MAX
    /// Pode ser nulo
    /// </summary>
    [DisplayName("Descrição")]
    public string? Descricao { get; set; }

    /// <summary>
    /// Quantidade disponível em estoque
    /// </summary>
    [DisplayName("Quantidade")]
    public int Quantidade { get; set; }

    /// <summary>
    /// Indica se o livro está disponível para venda/empréstimo
    /// </summary>
    [DisplayName("Disponível")]
    public bool Disponivel { get; set; }

    /// <summary>
    /// Coleção virtual de autores associados ao livro
    /// Relacionamento muitos-para-muitos com a entidade Autor
    /// </summary>
    [DisplayName("Autores")]
    public virtual ICollection<Autor> Autores { get; set; }
}

```

## Interfaces de Repositório

As interfaces de repositório definem os contratos para acesso a dados. O ABP Framework fornece uma implementação base com operações CRUD comuns.

IAutorRepository 
```csharp
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC.Autores;

/// <summary>
/// Interface do repositório para a entidade Autor.
/// Herda de IRepository do ABP Framework para fornecer operações básicas de CRUD.
/// </summary>
/// <remarks>
/// Esta interface herda todas as operações padrão do IRepository:
/// - InsertAsync: Inserir novo autor
/// - UpdateAsync: Atualizar autor existente
/// - DeleteAsync: Excluir autor
/// - GetAsync: Buscar autor por ID
/// - GetListAsync: Listar autores com paginação e ordenação
/// - GetQueryableAsync: Obter IQueryable para consultas customizadas
/// 
/// A implementação concreta (AutorRepository) é feita na camada
/// EntityFrameworkCore usando Entity Framework Core.
/// </remarks>
public interface IAutorRepository : IRepository<Autor>
{
}
```

ILivroRepository 
```csharp
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC.Livros;

/// <summary>
/// Interface do repositório para a entidade Livro.
/// Herda de IRepository do ABP Framework para fornecer operações básicas de CRUD.
/// </summary>
/// <remarks>
/// Esta interface herda todas as operações padrão do IRepository:
/// - InsertAsync: Inserir novo livro
/// - UpdateAsync: Atualizar livro existente
/// - DeleteAsync: Excluir livro
/// - GetAsync: Buscar livro por ID
/// - GetListAsync: Listar livros com paginação e ordenação
/// - GetQueryableAsync: Obter IQueryable para consultas customizadas
/// 
/// A implementação concreta (LivroRepository) é feita na camada
/// EntityFrameworkCore usando Entity Framework Core.
/// </remarks>
public interface ILivroRepository : IRepository<Livro>
{
}
```

# Implementando Domain Services

Os serviços de domínio são responsáveis por encapsular a lógica de negócio de única entidade. No SISPREC, utilizamos dois tipos principais de serviços de domínio:

1. **Domain Managers**: Gerenciam operações de negócio para entidades. Exemplos: Operações CRUD, validações, chamada de outros serviços relacionados, etc.
2. **Outros Domain Services**: Implementam regras de negócio específicas, complexas ou que envolvem mais de uma entidade. Exemplos: Validações de exclusão, regras de negócio complexas, integração com serviços externos, etc.

- Os Domain Managers herdam de `BaseDomainManager<T>` e implementam a interface `IBaseDomainManager<T>`, que fornece operações básicas de CRUD.
- Os outros Domain Services herdam de `DomainService` e implementam interfaces específicas do domínio.
- Os Managers podem ser usados como façade para chamar os Domain Services, deixando o Manager mais enxuto e delegando a lógica de negócio para os serviços especializados. Isso facilita a manutenção e o teste de cada parte separadamente.

## Domain Managers

Os Domain Managers implementam a lógica de negócio específica do domínio. Eles coordenam operações entre repositórios e aplicam regras de negócio.

```csharp
using TRF3.SISPREC.Domain;

namespace TRF3.SISPREC.Autores;

/// <summary>
/// Interface do gerenciador de domínio para a entidade Autor.
/// Define o contrato para operações de negócio específicas do domínio de Autores.
/// Herda de IBaseDomainManager<Autor> para operações básicas de CRUD.
/// </summary>
/// <remarks>
/// Esta interface estende as funcionalidades básicas de IBaseDomainManager com:
/// - Validações específicas do domínio de Autores
/// - Regras de negócio relacionadas ao ciclo de vida de um Autor
/// - Integração com outros serviços do domínio
/// 
/// A implementação concreta (AutorManager) deve:
/// 1. Garantir a consistência das operações de negócio
/// 2. Aplicar as regras de domínio
/// 3. Coordenar operações entre repositórios e serviços
/// 
/// Exemplos de operações que podem ser implementadas:
/// - Validação de dados específicos de Autores
/// - Aplicação de regras de negócio complexas
/// - Integração com serviços externos
/// </remarks>
public interface IAutorManager : IBaseDomainManager<Autor>
{
}
```

```csharp
using TRF3.SISPREC.Domain;

namespace TRF3.SISPREC.Livros;

/// <summary>
/// Interface do gerenciador de domínio para a entidade Livro.
/// Define o contrato para operações de negócio específicas do domínio de Livros.
/// Herda de IBaseDomainManager<Livro> para operações básicas de CRUD.
/// </summary>
/// <remarks>
/// Esta interface estende as funcionalidades básicas de IBaseDomainManager com:
/// - Validações específicas do domínio de Livros
/// - Regras de negócio relacionadas ao ciclo de vida de um Livro
/// - Integração com outros serviços do domínio
/// 
/// A implementação concreta (LivroManager) deve:
/// 1. Garantir a consistência das operações de negócio
/// 2. Aplicar as regras de domínio
/// 3. Coordenar operações entre repositórios e serviços
/// 
/// Exemplos de operações que podem ser implementadas:
/// - Validação de dados específicos de Livros
/// - Aplicação de regras de negócio complexas
/// - Integração com serviços externos
/// </remarks>
public interface ILivroManager : IBaseDomainManager<Livro>
{
}
```

```csharp
using TRF3.SISPREC.Domain;

namespace TRF3.SISPREC.Autores;

/// <summary>
/// Implementação concreta do gerenciador de domínio para a entidade Autor.
/// Responsável por coordenar as operações de negócio relacionadas a Autores.
/// </summary>
/// <remarks>
/// Esta classe implementa IAutorManager e herda de BaseDomainManager<Autor>,
/// fornecendo operações básicas de CRUD e funcionalidades específicas do domínio.
/// 
/// Principais responsabilidades:
/// - Gerenciar o ciclo de vida de Autores
/// - Coordenar operações entre repositórios e serviços
/// - Aplicar regras de negócio específicas
/// - Garantir a consistência das operações
/// 
/// Exemplos de operações:
/// - Criação de novos Autores com validações específicas
/// - Atualização de informações de Autores existentes
/// - Exclusão de Autores com validações de integridade
/// 
/// Padrões utilizados:
/// - Injeção de Dependência (DI) para serviços e repositórios
/// - Domain Services para validações complexas
/// - Repository Pattern para acesso a dados
/// </remarks>
public class AutorManager : BaseDomainManager<Autor>, IAutorManager
{
    /// <summary>
    /// Serviço de validação para exclusão de Autores.
    /// Utilizado para garantir que todas as regras de negócio sejam validadas
    /// antes da exclusão de um Autor.
    /// </summary>
    private readonly IValidarExcluirAutorService _validarExcluirAutorService;

    /// <summary>
    /// Construtor do gerenciador de Autores.
    /// </summary>
    /// <param name="repository">Repositório de Autores</param>
    /// <param name="validarExcluirAutorService">Serviço de validação para exclusão</param>
    public AutorManager(IAutorRepository repository, IValidarExcluirAutorService validarExcluirAutorService) : base(repository)
    {
        _validarExcluirAutorService = validarExcluirAutorService;
    }

    /// <summary>
    /// Realiza a exclusão de um Autor após validar todas as regras de negócio.
    /// </summary>
    /// <param name="entidade">Autor a ser excluído</param>
    /// <param name="autoSave">Indica se deve salvar automaticamente após a exclusão</param>
    /// <param name="cancellationToken">Token de cancelamento da operação</param>
    /// <returns>Task representando a operação assíncrona</returns>
    /// <exception cref="DomainException">
    /// Lançada quando alguma validação de negócio falha
    /// </exception>
    public override async Task ExcluirAsync(Autor entidade, bool autoSave = false, CancellationToken cancellationToken = default)
    {
        await _validarExcluirAutorService.Validar(entidade);
        await base.ExcluirAsync(entidade, autoSave, cancellationToken);
    }
}
```

```csharp
using TRF3.SISPREC.Autores;
using TRF3.SISPREC.Domain;
using Volo.Abp;

namespace TRF3.SISPREC.Livros;

/// <summary>
/// Gerenciador de livros do sistema.
/// Namespace: TRF3.SISPREC.Livros
/// Diretório: .\src\TRF3.SISPREC.Domain\Livros\Servicos
/// </summary>
public class LivroManager : BaseDomainManager<Livro>, ILivroManager
{
    private readonly IAutorRepository _autorRepository;
    public LivroManager(ILivroRepository repository, IAutorRepository autorRepository) : base(repository)
    {
        _autorRepository = autorRepository;
    }

    /// <summary>
    /// Insere um novo livro com seus autores relacionados.
    /// </summary>
    /// <param name="entity">Entidade do livro a ser inserido</param>
    /// <param name="autoresIds">IDs dos autores que serão vinculados ao livro</param>
    /// <param name="autoSave">Indica se deve salvar automaticamente</param>
    /// <exception cref="UserFriendlyException">Lançada quando algum autor informado não é encontrado</exception>
    public async Task InserirAsync(Livro entity, IEnumerable<int> autoresIds, bool autoSave = false)
    {
        var autores = (await _autorRepository.GetQueryableAsync()).Where(a => autoresIds.Contains(a.AutorId)).ToList();

        if (autores.Count != autoresIds.Count())
        {
            throw new UserFriendlyException("Nem todos os autores informados foram encontrados.");
        }

        entity.Autores = autores;

        await base.InserirAsync(entity, autoSave);
    }

    /// <summary>
    /// Altera um livro existente e atualiza seus autores relacionados.
    /// </summary>
    /// <param name="entity">Entidade do livro a ser alterado</param>
    /// <param name="autoresIds">IDs dos autores que serão vinculados ao livro</param>
    /// <param name="autoSave">Indica se deve salvar automaticamente</param>
    /// <exception cref="UserFriendlyException">Lançada quando algum autor informado não é encontrado</exception>
    public async Task AlterarAsync(Livro entity, IEnumerable<int> autoresIds, bool autoSave = false)
    {
        var autores = (await _autorRepository.GetQueryableAsync()).Where(a => autoresIds.Contains(a.AutorId)).ToList();

        if (autores.Count != autoresIds.Count())
        {
            throw new UserFriendlyException("Nem todos os autores informados foram encontrados.");
        }

        entity.Autores = autores;

        await base.AlterarAsync(entity, autoSave);
    }
}

```	

### Serviços de Validação

Os serviços de validação são classes específicas que implementam regras de negócio complexas:

```csharp
using Volo.Abp.Domain.Services;

namespace TRF3.SISPREC.Autores;

/// <summary>
/// Interface para serviço de validação de exclusão de Autores.
/// Define o contrato para validações específicas que devem ser realizadas
/// antes da exclusão de um Autor no sistema.
/// </summary>
/// <remarks>
/// Esta interface herda de IDomainService do ABP Framework e deve ser implementada
/// para garantir que todas as regras de negócio relacionadas à exclusão de Autores
/// sejam validadas antes da operação ser realizada.
/// 
/// Exemplos de validações que podem ser implementadas:
/// - Verificar se o Autor está vinculado a outras entidades
/// - Validar permissões do usuário que está realizando a exclusão
/// - Verificar restrições temporais ou de estado
/// - Garantir a integridade referencial do domínio
/// 
/// A implementação concreta deve lançar exceções específicas do domínio
/// caso alguma validação falhe.
/// </remarks>
public interface IValidarExcluirAutorService : IDomainService
{
    /// <summary>
    /// Realiza todas as validações necessárias antes da exclusão de um Autor.
    /// </summary>
    /// <param name="autor">Instância do Autor a ser validado</param>
    /// <returns>Task representando a operação assíncrona</returns>
    /// <exception cref="DomainException">
    /// Lançada quando alguma validação falha
    /// </exception>
    Task Validar(Autor autor);
}

```

```csharp
using Microsoft.EntityFrameworkCore;
using TRF3.SISPREC.Livros;
using Volo.Abp;
using Volo.Abp.Domain.Services;

namespace TRF3.SISPREC.Autores;

/// <summary>
/// Implementação concreta do serviço de validação para exclusão de Autores.
/// Segue o padrão Domain Service do ABP Framework para encapsular regras de negócio complexas.
/// </summary>
/// <remarks>
/// Esta classe implementa IValidarExcluirAutorService e herda de DomainService,
/// fornecendo validações específicas do domínio relacionadas à exclusão de Autores.
/// 
/// Principais responsabilidades:
/// - Validar se um Autor pode ser excluído
/// - Verificar dependências com outras entidades
/// - Garantir a integridade referencial do domínio
/// - Fornecer mensagens de erro claras para o usuário final
/// 
/// Padrões utilizados:
/// - Domain Service: Encapsula regras de negócio complexas
/// - Injeção de Dependência (DI): Recebe dependências via construtor
/// - Repository Pattern: Acessa dados através de interfaces
/// - UserFriendlyException: Fornece mensagens claras para o usuário
/// </remarks>
public class ValidarExcluirAutorService : DomainService, IValidarExcluirAutorService
{
    private readonly ILivroRepository _livroRepository;

    /// <summary>
    /// Construtor do serviço de validação.
    /// </summary>
    /// <param name="livroRepository">Repositório de Livros para verificar dependências</param>
    public ValidarExcluirAutorService(ILivroRepository livroRepository)
    {
        _livroRepository = livroRepository;
    }

    /// <summary>
    /// Realiza todas as validações necessárias antes da exclusão de um Autor.
    /// </summary>
    /// <param name="autor">Autor a ser validado</param>
    /// <returns>Task representando a operação assíncrona</returns>
    /// <exception cref="ArgumentNullException">
    /// Lançada quando o Autor é nulo
    /// </exception>
    /// <exception cref="UserFriendlyException">
    /// Lançada quando o Autor possui Livros vinculados
    /// </exception>
    public async Task Validar(Autor autor)
    {
        if (autor == null)
        {
            throw new ArgumentNullException(nameof(autor));
        }

        // Verifica se existem Livros vinculados ao Autor
        if ((await _livroRepository.GetQueryableAsync())
            .Include(a => a.Autores)
            .Where(a => a.Autores.Any(a => a.AutorId == autor.AutorId))
            .Any())
        {
            throw new UserFriendlyException("Não é possível excluir Autor com Livro vinculado.");
        }
    }
}
```


## Boas Práticas

1. **Entidades**
   - Use annotations para configurar comportamentos
   - Implemente validações de domínio
   - Mantenha a lógica de negócio encapsulada
   - Documente propriedades e relacionamentos

2. **Repositórios**
   - Defina interfaces claras e coesas
   - Use operações assíncronas
   - Implemente consultas otimizadas
   - Documente o comportamento esperado

3. **Domain Managers**
   - Implemente regras de negócio complexas em serviços separados, que podem ser injetados no Manager.
   - Coordene operações entre repositórios
   - Mantenha a consistência dos dados
   - Documente as regras implementadas

4. **Documentação**
   - Use XML comments para documentar classes e membros
   - Inclua exemplos de uso quando relevante
   - Mantenha a documentação atualizada
   - Explique o propósito de cada componente

5. **Organização**
   - Agrupe classes relacionadas em namespaces
   - Separe interfaces de implementações
   - Use pastas para organizar o código
   - Mantenha uma estrutura consistente

6. **Validações**
   - Use `UserFriendlyException` para mensagens ao usuário
   - Valide dados antes de persistir
   - Centralize regras de negócio no domínio

Leia mais sobre [Serviços de Domínio](/ABP-Docs/Arquitetura/Domain-Driven-Design/domain-layer/Serviços-de-Domínio).

**[Próximo: Projeto EntityFrameworkCore](/Tutorial-de-Início/Projeto-EFCore)**