# ASP.NET Core MVC / Razor Pages: Tematização de UI

## Introdução

O ABP fornece um sistema completo de **Tematização de UI** com os seguintes objetivos:

* Os [módulos de aplicação](../../../modules) reutilizáveis são desenvolvidos de forma **independente do tema**, para que possam funcionar com qualquer tema de UI.
* O tema da UI é **decidido pela aplicação final**.
* O tema é distribuído através de pacotes NuGet/NPM, para que seja **facilmente atualizável**.
* A aplicação final pode **customizar** o tema selecionado.

Para atingir estes objetivos, o ABP:

* Determina um conjunto de **bibliotecas base** usadas e adaptadas por todos os temas. Assim, os desenvolvedores de módulos e aplicações podem depender e usar estas bibliotecas sem depender de um tema específico.
* Fornece um sistema que consiste em [menus de navegação](navigation-menu.md), [toolbars](toolbars.md), [layout hooks](layout-hooks.md)... que é implementado por todos os temas. Assim, os módulos e a aplicação podem contribuir para o layout, para compor uma UI de aplicação consistente.

### Temas Atuais

Atualmente, quatro temas são **fornecidos oficialmente**:

* O [Tema Básico](basic-theme.md) é o tema minimalista com o estilo Bootstrap simples. É **open source e gratuito**.
* O [Tema LeptonX Lite](../../../ui-themes/lepton-x-lite/asp-net-core.md) é um tema moderno e elegante de UI Bootstrap. É ideal se quiser ter um tema de UI pronto para produção. Também é **open source e gratuito**.
* O [Tema Lepton](https://abp.io/themes) é um tema **comercial** desenvolvido pela equipe central do ABP e faz parte da licença [ABP](https://abp.io/).
* O [Tema LeptonX](../../../ui-themes/lepton-x/index.md) também é um tema **comercial** desenvolvido pelo tema central do ABP e faz parte da licença [ABP](https://abp.io/). Este é o tema padrão após o ABP v6.0.0.

Existem também alguns temas impulsionados pela comunidade para o ABP (pode pesquisar na web).

## Geral

### As Bibliotecas Base

Todos os temas devem depender do pacote NPM [@abp/aspnetcore.mvc.ui.theme.shared](https://www.npmjs.com/package/@abp/aspnetcore.mvc.ui.theme.shared), por isso dependem indiretamente das seguintes bibliotecas:

* [Twitter Bootstrap](https://getbootstrap.com/) como o framework fundamental de HTML/CSS.
* [JQuery](https://jquery.com/) para manipulação do DOM.
* [DataTables.Net](https://datatables.net/) para grids de dados.
* [JQuery Validation](https://github.com/jquery-validation/jquery-validation) para validação client-side e [unobtrusive](https://github.com/aspnet/jquery-validation-unobtrusive)
* [FontAwesome](https://fontawesome.com/) como a biblioteca de fontes CSS fundamental.
* [SweetAlert](https://sweetalert.js.org/) para mostrar mensagens de alerta e caixas de diálogo de confirmação.
* [Toastr](https://github.com/CodeSeven/toastr) para mostrar notificações toast.
* [Lodash](https://lodash.com/) como uma biblioteca de utilitários.
* [Luxon](https://moment.github.io/luxon/) para operações de data/hora.
* [JQuery Form](https://github.com/jquery-form/form) para formulários AJAX.
* [bootstrap-datepicker](https://github.com/uxsolutions/bootstrap-datepicker) para mostrar seletores de data.
* [Select2](https://select2.org/) para melhores caixas de seleção/combo.
* [Timeago](http://timeago.yarp.com/) para mostrar timestamps fuzzy com atualização automática.
* [malihu-custom-scrollbar-plugin](https://github.com/malihu/malihu-custom-scrollbar-plugin) para scrollbars customizados.

Estas bibliotecas são selecionadas como as bibliotecas base e estão disponíveis para as aplicações e módulos.

#### Abstrações / Wrappers

Existem algumas abstrações no ABP para tornar o seu código independente também de algumas destas bibliotecas. Exemplos;

* [Tag Helpers](tag-helpers) tornam fácil gerar as UIs do Bootstrap.
* As APIs JavaScript [Message](javascript-api/message.md) e [Notification](javascript-api/notify.md) fornecem abstrações para usar o Sweetalert e o Toastr.
* O sistema [Forms & Validation](forms-validation.md) lida automaticamente com a validação, pelo que na maioria das vezes não digita diretamente nenhum código de validação.

### Os Layouts Standard

A principal responsabilidade de um tema é fornecer os layouts. Existem **três layouts pré-definidos que devem ser implementados por todos os temas**:

* **Aplicação**: O layout padrão que é usado pelas páginas principais da aplicação.
* **Conta**: Usado principalmente pelo [módulo de conta](../../../modules/account.md) para as páginas de login, registo, esqueceu-se da palavra-passe...
* **Vazio**: O layout mínimo que não tem componentes de layout.

Os nomes dos layouts são constantes definidas na classe `Volo.Abp.AspNetCore.Mvc.UI.Theming.StandardLayouts`.

#### O Layout da Aplicação

Este é o layout padrão que é usado pelas páginas principais da aplicação. A imagem seguinte mostra a página de gestão de utilizadores no layout de aplicação do [Tema Básico](basic-theme.md):

![basic-theme-application-layout](/ABP-Docs/images/basic-theme-application-layout.png)

E a mesma página é mostrada abaixo com o layout de aplicação do [Tema Lepton](https://abp.io/themes):

![lepton-theme-application-layout](/ABP-Docs/images/lepton-theme-application-layout.png)

Como pode ver, a página é a mesma, mas o aspeto é completamente diferente nos temas acima.

O layout da aplicação inclui normalmente as seguintes partes;

* Um [menu principal](navigation-menu.md)
* Uma [Toolbar](toolbars.md) principal com os seguintes componentes;
    * Menu do utilizador
    * Dropdown para troca de idioma
* [Alertas de página](page-alerts.md)
* O conteúdo da página (também conhecido como `RenderBody()`)
* [Layout hooks](layout-hooks.md)

Alguns temas podem fornecer mais partes, como breadcrumbs, cabeçalho e toolbar da página... etc. Veja a seção *Layout Parts*.

#### O Layout da Conta

O layout da Conta é normalmente usado pelo [módulo de conta](../../../modules/account.md) para as páginas de login, registo, esqueceu-se da palavra-passe...

![basic-theme-account-layout](/ABP-Docs/images/basic-theme-account-layout.png)

Este layout normalmente fornece as seguintes partes;

* Dropdown para troca de idioma
* Área de troca de tenant (se a aplicação for [multi-tenant](../../architecture/multi-tenancy) e a atual for resolvida pelo cookie)
* [Alertas de página](page-alerts.md)
* O conteúdo da página (também conhecido como `RenderBody()`)
* [Layout hooks](layout-hooks.md)

O [Tema Básico](basic-theme.md) também renderiza a barra de navegação superior para este layout (como mostrado acima).

Aqui, o layout de conta do Tema Lepton:

![lepton-theme-account-layout](/ABP-Docs/images/lepton-theme-account-layout.png)

O [Tema Lepton](https://abp.io/themes) mostra o logotipo da aplicação e o rodapé neste layout.

> Pode sobrescrever os layouts de tema completa ou parcialmente numa aplicação para a [customizar](customization-user-interface.md).

#### O Layout Vazio

O layout vazio fornece uma página vazia. Normalmente inclui as seguintes partes;

* [Alertas de página](page-alerts.md)
* O conteúdo da página (também conhecido como `RenderBody()`)
* [Layout hooks](layout-hooks.md)

## Implementando um Tema

### A Forma Mais Fácil

A forma mais fácil de criar um novo tema é adicionar o [Código Fonte do Tema Básico](https://github.com/abpframework/abp/tree/dev/modules/basic-theme) com os códigos fonte e customizá-lo.

```bash
abp add-package Volo.Abp.AspNetCore.Mvc.UI.Theme.Basic --with-source-code --add-to-solution-file
```

### A Interface ITheme

A interface `ITheme` é usada pelo ABP para selecionar o layout para a página atual. Um tema deve implementar esta interface para fornecer o caminho do layout solicitado.

Esta é a implementação de `ITheme` do [Tema Básico](basic-theme.md).

````csharp
using Volo.Abp.AspNetCore.Mvc.UI.Theming;
using Volo.Abp.DependencyInjection;

namespace Volo.Abp.AspNetCore.Mvc.UI.Theme.Basic
{
    [ThemeName(Name)]
    public class BasicTheme : ITheme, ITransientDependency
    {
        public const string Name = "Basic";

        public virtual string GetLayout(string name, bool fallbackToDefault = true)
        {
            switch (name)
            {
                case StandardLayouts.Application:
                    return "~/Themes/Basic/Layouts/Application.cshtml";
                case StandardLayouts.Account:
                    return "~/Themes/Basic/Layouts/Account.cshtml";
                case StandardLayouts.Empty:
                    return "~/Themes/Basic/Layouts/Empty.cshtml";
                default:
                    return fallbackToDefault
                        ? "~/Themes/Basic/Layouts/Application.cshtml"
                        : null;
            }
        }
    }
}
````

* O atributo `[ThemeName]` é obrigatório e um tema deve ter um nome único, `Basic` neste exemplo.
* O método `GetLayout` deve retornar um caminho se o layout solicitado (`name`) for fornecido pelo tema. Os *Layouts Padrão* devem ser implementados se o tema se destinar a ser usado por uma aplicação padrão. Pode implementar layouts adicionais.

Assim que o tema implementar a interface `ITheme`, deve adicionar o tema às `AbpThemingOptions` no método `ConfigureServices` do [módulo](../../architecture/modularity/basics.md).

````csharp
Configure<AbpThemingOptions>(options =>
{
    options.Themes.Add<BasicTheme>();
});
````

#### O Serviço IThemeSelector

O ABP permite usar vários temas em conjunto. É por isso que `options.Themes` é uma lista. O serviço `IThemeSelector` seleciona o tema em tempo de execução. O desenvolvedor da aplicação pode definir o `AbpThemingOptions.DefaultThemeName` para definir o tema a ser usado ou substituir a implementação do serviço `IThemeSelector` (a implementação padrão é `DefaultThemeSelector`) para controlar completamente a seleção do tema em tempo de execução.

### Bundles

O [sistema de bundling](bundling-minification.md) fornece uma forma padrão de importar arquivos de estilo e script para páginas. Existem dois bundles padrão definidos pelo ABP:

* `StandardBundles.Styles.Global`: O bundle global que inclui os arquivos de estilo usados em todas as páginas. Normalmente, inclui os arquivos CSS das Bibliotecas Base.
* `StandardBundles.Scripts.Global`: O bundle global que inclui os arquivos de script usados em todas as páginas. Normalmente, inclui os arquivos JavaScript das Bibliotecas Base.

Um tema geralmente estende estes bundles padrão, adicionando arquivos CSS/JavaScript específicos do tema.

A melhor forma de definir novos bundles, herdar dos bundles padrão e adicionar às `AbpBundlingOptions` como mostrado abaixo (este código é do [Tema Básico](basic-theme.md)):

````csharp
Configure<AbpBundlingOptions>(options =>
{
    options
        .StyleBundles
        .Add(BasicThemeBundles.Styles.Global, bundle =>
        {
            bundle
                .AddBaseBundles(StandardBundles.Styles.Global)
                .AddContributors(typeof(BasicThemeGlobalStyleContributor));
        });

    options
        .ScriptBundles
        .Add(BasicThemeBundles.Scripts.Global, bundle =>
        {
            bundle
                .AddBaseBundles(StandardBundles.Scripts.Global)
                .AddContributors(typeof(BasicThemeGlobalScriptContributor));
        });
});
````

`BasicThemeGlobalStyleContributor` e `BasicThemeGlobalScriptContributor` são contribuidores de bundle. Por exemplo, `BasicThemeGlobalStyleContributor` é definido como mostrado abaixo:

```csharp
public class BasicThemeGlobalStyleContributor : BundleContributor
{
    public override void ConfigureBundle(BundleConfigurationContext context)
    {
        context.Files.Add("/themes/basic/layout.css");
    }
}
```

Em seguida, o tema pode renderizar estes bundles num layout. Por exemplo, pode renderizar os Estilos Globais como mostrado abaixo:

````html
<abp-style-bundle name="@BasicThemeBundles.Styles.Global" />
````

Veja o documento [Bundle & Minification](bundling-minification.md) para entender melhor o sistema de Bundling.

### Partes do Layout

Um Layout típico consiste em várias partes. O tema deve incluir as partes necessárias em cada layout.

**Exemplo: O Tema Básico tem as seguintes partes para o Layout da Aplicação**

![basic-theme-application-layout-parts](/ABP-Docs/images/basic-theme-application-layout-parts.png)

O código da aplicação e os módulos só podem mostrar conteúdo na parte do Conteúdo da Página. Se precisarem de alterar as outras partes (para adicionar um item de menu, para adicionar um item de toolbar, para alterar o nome da aplicação na área de branding...) devem usar as APIs do ABP.

As seções seguintes explicam as partes fundamentais pré-definidas pelo ABP e que podem ser implementadas pelos temas.

> É uma boa prática dividir o layout em componentes/partials, para que a aplicação final os possa sobrescrever parcialmente para fins de customização.

#### Branding

O serviço `IBrandingProvider` deve ser usado para obter o nome e o URL do logotipo da aplicação para renderizar na parte de Branding.

O [Modelo de Startup da Aplicação](../../../solution-templates/layered-web-application) tem uma implementação desta interface para definir os valores pelo desenvolvedor da aplicação.

#### Menu Principal

O serviço `IMenuManager` é usado para obter os itens do menu principal e renderizar no layout.

**Exemplo: Obter o Menu Principal para renderizar num componente de view**

```csharp
public class MainNavbarMenuViewComponent : AbpViewComponent
{
    private readonly IMenuManager _menuManager;

    public MainNavbarMenuViewComponent(IMenuManager menuManager)
    {
        _menuManager = menuManager;
    }

    public async Task<IViewComponentResult> InvokeAsync()
    {
        var menu = await _menuManager.GetAsync(StandardMenus.Main);
        return View("~/Themes/Basic/Components/Menu/Default.cshtml", menu);
    }
}
```

Veja o documento [Navegação / Menus](navigation-menu.md) para saber mais sobre o sistema de navegação.

#### Toolbar Principal

O serviço `IToolbarManager` é usado para obter os itens da Toolbar Principal e renderizar no layout. Cada item desta toolbar é um Componente de View, pelo que pode incluir qualquer tipo de elementos de UI. Injete o `IToolbarManager` e use o `GetAsync` para obter os itens da toolbar:

````csharp
var toolbar = await _toolbarManager.GetAsync(StandardToolbars.Main);
````

> Veja o documento [Toolbars](toolbars.md) para saber mais sobre o sistema de toolbars.

O tema tem a responsabilidade de adicionar dois itens pré-definidos à toolbar principal: Seleção de Idioma e Menu do Utilizador. Para isso, crie uma classe que implemente a interface `IToolbarContributor` e adicione-a às `AbpToolbarOptions` como mostrado abaixo:

```csharp
Configure<AbpToolbarOptions>(options =>
{
    options.Contributors.Add(new BasicThemeMainTopToolbarContributor());
});
```

##### Seleção de Idioma

O item da toolbar de Seleção de Idioma é geralmente um dropdown que é usado para alternar entre idiomas. O `ILanguageProvider` é usado para obter a lista de idiomas disponíveis e o `CultureInfo.CurrentUICulture` é usado para saber o idioma atual.

O endpoint `/Abp/Languages/Switch` pode ser usado para alternar o idioma. Este endpoint aceita os seguintes parâmetros de query string:

* `culture`: A cultura selecionada, como `en-US` ou `en`.
* `uiCulture`: A cultura da UI selecionada, como `en-US` ou `en`.
* `returnUrl` (opcional): Pode ser usado para retornar um URL dado após alternar o idioma.

`culture` e `uiCulture` devem corresponder a um dos idiomas disponíveis. O ABP define um cookie de cultura no endpoint `/Abp/Languages/Switch`.

##### Menu do Utilizador

O menu do utilizador inclui links relacionados com a conta do utilizador. O `IMenuManager` é usado da mesma forma que o Menu Principal, mas desta vez com o parâmetro `StandardMenus.User`, como mostrado abaixo:

````csharp
var menu = await _menuManager.GetAsync(StandardMenus.User);
````

Os serviços [ICurrentUser](.././../infrastructure/current-user.md) e [ICurrentTenant](../../architecture/multi-tenancy) podem ser usados para obter os nomes do utilizador e tenant atuais.

#### Alertas de Página

O serviço `IAlertManager` é usado para obter os alertas da página atual para renderizar no layout. Use a lista `Alerts` do `IAlertManager`. Geralmente é renderizado imediatamente antes do conteúdo da página (`RenderBody()`).

Veja o documento [Alertas de Página](page-alerts.md) para saber mais.

#### Layout Hooks

Como o Layout está no pacote do tema, a aplicação final ou qualquer módulo não pode manipular diretamente o conteúdo do layout. O sistema [Layout Hook](layout-hooks.md) permite injetar componentes em alguns pontos específicos do layout.

O tema é responsável por renderizar os hooks no lugar correto.

**Exemplo: Renderizar o Hook `LayoutHooks.Head.First` no Layout da Aplicação**

````html
<head>
    @await Component.InvokeLayoutHookAsync(LayoutHooks.Head.First, StandardLayouts.Application)
    ...
````

Veja o documento [Layout Hook](layout-hooks.md) para aprender os layout hooks padrão.

#### Seções de Script / Estilo

Todos os layouts devem renderizar as seguintes seções opcionais:

* A seção `styles` é renderizada no final do `head`, imediatamente antes do `LayoutHooks.Head.Last`.
* A seção `scripts` é renderizada no final do `body`, imediatamente antes do `LayoutHooks.Body.Last`.

Desta forma, a página pode importar estilos e scripts para o layout.

**Exemplo: Renderizar a seção `styles`**

````csharp
@await RenderSectionAsync("styles", required: false)
````

#### Seção da Toolbar de Conteúdo

Outra seção pré-definida é a seção da Toolbar de Conteúdo, que pode ser usada pelas páginas para adicionar código imediatamente antes do conteúdo da página. O Tema Básico renderiza-o como mostrado abaixo:

````html
<div id="AbpContentToolbar">
    <div class="text-end mb-2">
        @RenderSection("content_toolbar", false)
    </div>
</div>
````

O id do div do container deve ser `AbpContentToolbar`. Esta seção deve vir antes do `RenderBody()`.

#### Recursos de Widgets

O [Sistema de Widgets](widgets.md) permite definir widgets reutilizáveis com seus próprios arquivos de estilo/script. Todos os layouts devem renderizar o estilo e os scripts do widget.

Os **Estilos de Widget** são renderizados como mostrado abaixo, imediatamente antes da seção `styles`, após o bundle de estilo global:

````csharp
@await Component.InvokeAsync(typeof(WidgetStylesViewComponent))
````

Os **Scripts de Widget** são renderizados como mostrado abaixo, imediatamente antes da seção `scripts`, após o bundle de script global:

````csharp
@await Component.InvokeAsync(typeof(WidgetScriptsViewComponent))
````

#### Scripts do ABP

O ABP tem alguns scripts especiais que devem ser incluídos em todos os layouts. Não estão incluídos nos bundles globais, uma vez que são criados dinamicamente com base no utilizador atual.

Os scripts do ABP (`ApplicationConfigurationScript` e `ServiceProxyScript`) devem ser adicionados imediatamente após o bundle de script global, como mostrado abaixo:

````html
<script src="~/Abp/ApplicationConfigurationScript"></script>
<script src="~/Abp/ServiceProxyScript"></script>
````

#### Título da Página, Item de Menu Selecionado e Breadcrumbs

O serviço `IPageLayout` pode ser injetado por qualquer página para definir o Título da Página, o nome do item de menu selecionado e os itens do breadcrumb. Em seguida, o tema pode usar este serviço para obter estes valores e renderizar na UI.

O Tema Básico não implementa este serviço, mas o Tema Lepton implementa:

![breadcrumbs-example](/ABP-Docs/images/breadcrumbs-example.png)

Veja o documento [Cabeçalho da Página](page-header.md) para mais informações.

#### Troca de Tenant

O Layout da Conta deve permitir que o utilizador troque o tenant atual se a aplicação for multi-tenant e o tenant tiver sido resolvido a partir dos cookies. Veja o [Layout de Conta do Tema Básico](https://github.com/abpframework/abp/blob/dev/modules/basic-theme/src/Volo.Abp.AspNetCore.Mvc.UI.Theme.Basic/Themes/Basic/Layouts/Account.cshtml) como um exemplo de implementação.

### Classes de Layout

Os Layouts Padrão (`Application`, `Account` e `Empty`) devem adicionar as seguintes classes CSS à tag `body`:

* `abp-application-layout` para o layout `Application`.
* `abp-account-layout` para o layout `Account`.
* `abp-empty-layout` para o layout `Empty`.

Desta forma, as aplicações ou módulos podem ter seletores baseados no layout atual.

### RTL

Para suportar idiomas da Direita para a Esquerda, o Layout deve verificar a cultura atual e adicionar `dir="rtl"` à tag `html` e a classe CSS `rtl` à tag `body`.

Pode verificar `CultureInfo.CurrentUICulture.TextInfo.IsRightToLeft` para entender se o idioma atual é um idioma RTL.

### O Pacote NPM

Um tema deve ter um pacote NPM que dependa do pacote [@abp/aspnetcore.mvc.ui.theme.shared](https://www.npmjs.com/package/@abp/aspnetcore.mvc.ui.theme.shared). Desta forma, herda todas as Bibliotecas Base. Se o tema exigir bibliotecas adicionais, então também deve definir estas dependências.

As aplicações usam o sistema [Client Side Package Management](client-side-package-management.md) para adicionar bibliotecas client-side ao projeto. Assim, se uma aplicação usar o seu tema, deve adicionar a dependência ao pacote NPM do seu tema, bem como a dependência do pacote NuGet.
