# ASP.NET Core MVC / Razor Pages: Data Tables

Uma Data Table (também conhecida como Data Grid) é um componente de UI para exibir dados tabulares para os usuários. Existem muitos componentes/bibliotecas de Data table e **você pode usar qualquer um que preferir** com o ABP. No entanto, os templates de inicialização vêm com a biblioteca [DataTables.Net](https://datatables.net/) **pré-instalada e configurada**. O ABP fornece adaptadores para esta biblioteca e torna mais fácil usá-la com os endpoints da API.

Uma captura de tela de exemplo da página de gerenciamento de usuários que mostra a lista de usuários em uma data table:

![datatables-example](/ABP-Docs/images/datatables-example.png)

## Integração com DataTables.Net

Primeiramente, você pode seguir a documentação oficial para entender como o [DataTables.Net](https://datatables.net/) funciona. Esta seção se concentrará nos addons e pontos de integração do ABP, em vez de cobrir totalmente o uso desta biblioteca.

### Um Exemplo Rápido

Você pode seguir o [tutorial de desenvolvimento de aplicação web](../../../tutorials/book-store/part-01.md) para um exemplo completo de aplicação que usa o DataTables.Net como a Data Table. Esta seção mostra um exemplo minimalista.

Você não precisa fazer nada para adicionar a biblioteca DataTables.Net à página, pois ela já está adicionada ao [bundle](bundling-minification.md) global por padrão.

Primeiro, adicione um `abp-table` como mostrado abaixo, com um `id`:

````html
<abp-table striped-rows="true" id="BooksTable"></abp-table>
````

> `abp-table` é um [Tag Helper](tag-helpers) definido pelo ABP, mas uma tag `<table...>` simples também funcionaria.

Em seguida, chame o plugin `DataTable` no seletor da tabela:

````js
var dataTable = $('#BooksTable').DataTable(
    abp.libs.datatables.normalizeConfiguration({
        serverSide: true,
        paging: true,
        order: [[1, "asc"]],
        searching: false,
        ajax: abp.libs.datatables.createAjax(acme.bookStore.books.book.getList),
        columnDefs: [
            {
                title: l('Actions'),
                rowAction: {
                    items:
                        [
                            {
                                text: l('Edit'),
                                action: function (data) {
                                    ///...
                                }
                            }
                        ]
                }
            },
            {
                title: l('Name'),
                data: "name"
            },
            {
                title: l('PublishDate'),
                data: "publishDate",
                render: function (data) {
                    return luxon
                        .DateTime
                        .fromISO(data, {
                            locale: abp.localization.currentCulture.name
                        }).toLocaleString();
                }
            },
            {
                title: l('Price'),
                data: "price"
            }
        ]
    })
);
````

O código de exemplo acima usa alguns recursos de integração do ABP que serão explicados nas próximas seções.

### Normalização da Configuração

A função `abp.libs.datatables.normalizeConfiguration` pega uma configuração do DataTables e a normaliza para simplificá-la;

* Define a opção `scrollX` para `true`, se não estiver definida.
* Define o índice `target` para as definições de coluna.
* Define a opção `language` para [localizar](../../fundamentals/localization.md) a tabela no idioma atual.

#### Configuração Padrão

`normalizeConfiguration` usa a configuração padrão. Você pode alterar a configuração padrão usando o objeto `abp.libs.datatables.defaultConfigurations`. Exemplo:

````js
abp.libs.datatables.defaultConfigurations.scrollX = false;
````

Aqui, todas as opções de configuração;

* `scrollX`: `false` por padrão.
* `dom`: O valor padrão é `<"dataTable_filters"f>rt<"row dataTable_footer"<"col-auto"l><"col-auto"i><"col"p>>`.
* `language`: Uma função que retorna o texto de localização usando o idioma atual.

### Adaptador AJAX

O DataTables.Net tem seu próprio formato de dados esperado ao obter resultados de uma chamada AJAX para o servidor para obter os dados da tabela. Eles estão especialmente relacionados a como os parâmetros de paginação e classificação são enviados e recebidos. O ABP também oferece suas próprias convenções para a comunicação [AJAX](javascript-api/ajax.md) cliente-servidor.

O método `abp.libs.datatables.createAjax` (usado no exemplo acima) adapta o formato de dados de solicitação e resposta e funciona perfeitamente com o sistema [Dynamic JavaScript Client Proxy](dynamic-javascript-proxies.md).

Isso funciona automaticamente, então na maioria das vezes você não precisa saber como funciona. Veja o documento [DTO](../../architecture/domain-driven-design/data-transfer-objects.md)Data-Transfer-Objects.md se você quiser saber mais sobre `IPagedAndSortedResultRequest`, `IPagedResult` e outras interfaces padrão e classes DTO base que são usadas na comunicação cliente-servidor.

O `createAjax` também oferece suporte para você personalizar parâmetros de solicitação e manipular as respostas.

**Exemplo:**

````csharp
var inputAction = function (requestData, dataTableSettings) {
    return {
        id: $('#Id').val(),
        name: $('#Name').val(),
    };
};

var responseCallback = function(result) {

    // seu código personalizado.

    return {
        recordsTotal: result.totalCount,
        recordsFiltered: result.totalCount,
        data: result.items
    };
};

ajax: abp.libs.datatables.createAjax(acme.bookStore.books.book.getList, inputAction, responseCallback)
````

Se você não precisa acessar ou modificar o `requestData` ou o `dataTableSettings`, você pode especificar um objeto simples como segundo parâmetro.

````js
ajax: abp.libs.datatables.createAjax(
    acme.bookStore.books.book.getList, 
    { id: $('#Id').val(), name: $('#Name').val() }
)
````

### Ações de Linha

`rowAction` é uma opção definida pelo ABP para as definições de coluna para mostrar um botão suspenso para realizar ações para uma linha na tabela.

A captura de tela de exemplo abaixo mostra as ações para cada usuário na tabela de gerenciamento de usuários:

![datatables-example](/ABP-Docs/images/datatables-row-actions.png)

`rowAction` é definido como parte de uma definição de coluna:

````csharp
{
    title: l('Actions'),
    rowAction: {
        //TODO: CONFIGURAÇÃO
    }
},
````

**Exemplo: Mostrar ações *Editar* e *Excluir* para uma linha de livro**

````js
{
    title: l('Actions'),
    rowAction: {
        items:
            [
                {
                    text: l('Edit'),
                    action: function (data) {
                        //TODO: Abrir um modal para editar o livro
                    }
                },
                {
                    text: l('Delete'),
                    confirmMessage: function (data) {
                        return "Tem certeza de que deseja excluir o livro " + data.record.name;
                    },
                    action: function (data) {
                        acme.bookStore.books.book
                            .delete(data.record.id)
                            .then(function() {
                                abp.notify.info("Excluído com sucesso!");
                                data.table.ajax.reload();
                            });
                    }
                }
            ]
    }
},
````

#### Itens de Ação

`items` é um array de definições de ação. Uma definição de ação pode ter as seguintes opções;

* `text`: O texto (uma `string`) para esta ação a ser mostrada no menu suspenso de ações.
* `action`: Uma `function` que é executada quando o usuário clica na ação. A função recebe um argumento `data` que possui os seguintes campos;
  * `data.record`: Este é o objeto de dados relacionado à linha. Você pode acessar os campos de dados como `data.record.id`, `data.record.name`... etc.
  * `data.table`: A instância do DataTables.
* `confirmMessage`: Uma `function` (veja o exemplo acima) que retorna uma mensagem (`string`) para mostrar um diálogo para obter uma confirmação do usuário antes de executar a `action`. Exemplo de diálogo de confirmação:

![datatables-row-actions-confirmation](/ABP-Docs/images/datatables-row-actions-confirmation.png)

Você pode usar o sistema de [localização](javascript-api/localization.md) para mostrar uma mensagem localizada.

* `visible`: Um `bool` ou uma `function` que retorna um `bool`. Se o resultado for `false`, a ação não será exibida no dropdown de ações. Isso geralmente é combinado com o sistema de [autorização](javascript-api/auth.md) para ocultar a ação se o usuário não tiver permissão para realizar esta ação. Exemplo:

````js
visible: abp.auth.isGranted('BookStore.Books.Delete');
````

Se você definir uma `function`, então a `function` tem dois argumentos: `record` (o objeto de dados da linha relacionada) e a `table` (a instância do DataTable). Assim, você pode decidir mostrar/ocultar a ação dinamicamente, com base nos dados da linha e outras condições.

* `iconClass`: Pode ser usado para mostrar um font-icon, como um ícone [Font-Awesome](https://fontawesome.com/) (ex: `fas fa-trash-alt`), próximo ao texto da ação. Exemplo de captura de tela:

![datatables-row-actions-confirmation](/ABP-Docs/images/datatables-row-actions-icon.png)

* `enabled`: Uma `function` que retorna um `bool` para desabilitar a ação. A `function` recebe um objeto `data` com dois campos: `data.record` é o objeto de dados relacionado à linha e `data.table` é a instância do DataTables.
* `displayNameHtml`: Defina isso como `true` se o valor `text` contiver tags HTML.

Existem algumas regras com os itens de ação;

* Se nenhum dos itens de ação estiver visível, a coluna de ações não é renderizada.

### Formato de Dados

#### O Problema

Veja a coluna *Creation Time* no exemplo abaixo:

````js
{
    title: l('CreationTime'),
    data: "creationTime",
    render: function (data) {
        return luxon
            .DateTime
            .fromISO(data, {
                locale: abp.localization.currentCulture.name
            }).toLocaleString(luxon.DateTime.DATETIME_SHORT);
    }
}
````

O `render` é uma opção padrão do DataTables para renderizar o conteúdo da coluna por uma função customizada. Este exemplo usa a biblioteca [luxon](https://moment.github.io/luxon/) (que está instalada por padrão) para escrever um valor legível por humanos de `creationTime` no idioma do usuário atual. Exemplo de saída da coluna:

![datatables-custom-render-date](/ABP-Docs/images/datatables-custom-render-date.png)

Se você não definir a opção de renderização, o resultado será feio e não amigável:

![datatables-custom-render-date](/ABP-Docs/images/datatables-default-render-date.png)

No entanto, renderizar um `DateTime` é quase igual e repetir a mesma lógica de renderização em todos os lugares é contra o princípio DRY (Don't Repeat Yourself!).

#### Opção dataFormat

A opção de coluna `dataFormat` especifica o formato de dados que é usado para renderizar os dados da coluna. A mesma saída poderia ser alcançada usando a seguinte definição de coluna:

````js
{
    title: l('CreationTime'),
    data: "creationTime",
    dataFormat: 'datetime'
}
````

`dataFormat: 'datetime'` especifica o formato de dados para esta coluna. Existem alguns `dataFormat`s predefinidos:

* `boolean`: Mostra um ícone de `check` para o valor `true` e um ícone de `times` para o valor `false` e é útil para renderizar valores `bool`.
* `date`: Mostra a parte da data de um valor `DateTime`, formatado com base na cultura atual.
* `datetime`: Mostra a data e a hora (excluindo segundos) de um valor `DateTime`, formatado com base na cultura atual.

### Renderizadores Padrão

A opção `abp.libs.datatables.defaultRenderers` permite que você defina novos formatos de dados e defina renderizadores para eles.

**Exemplo: Renderizar ícones masculino / feminino com base no gênero**

````js
abp.libs.datatables.defaultRenderers['gender'] = function(value) {
    if (value === 'f') {
        return '<i class="fa fa-venus"></i>';
    } else {
        return '<i class="fa fa-mars"></i>';
    }
};
````

Assumindo que os valores possíveis para os dados de uma coluna sejam `f` e `m`, o formato de dados `gender` mostra ícones feminino/masculino em vez de textos `f` e `m`. Agora você pode definir `dataFormat: 'gender'` para uma definição de coluna que tenha os valores de dados apropriados.

> Você pode escrever os renderizadores padrão em um único arquivo JavaScript e adicioná-lo ao [Global Script Bundle](bundling-minification.md), para que possa reutilizá-los em todas as páginas.

## Outras Data Grids

Você pode usar qualquer biblioteca que preferir. Por exemplo, [veja este artigo](https://abp.io/community/articles/using-devextreme-components-with-the-abp-framework-zb8z7yqv) para aprender como usar o DevExtreme Data Grid em seus aplicativos.
