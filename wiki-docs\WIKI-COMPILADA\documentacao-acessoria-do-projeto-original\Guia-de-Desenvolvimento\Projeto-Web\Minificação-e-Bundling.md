# Minificação e Bundling no ASP.NET Core MVC

Existem muitas maneiras de agrupar e minificar recursos do lado do cliente (arquivos JavaScript e CSS). As maneiras mais comuns são:

* Usar a extensão [Bundler & Minifier](https://marketplace.visualstudio.com/items?itemName=MadsKristensen.BundlerMinifier) do Visual Studio ou o [pacote NuGet](https://www.nuget.org/packages/BuildBundlerMinifier/).
* Usar gerenciadores de tarefas [Gulp](https://gulpjs.com/)/[Grunt](https://gruntjs.com/) e seus plugins.

O ABP oferece uma maneira simples, dinâmica, poderosa, modular e integrada.

## Pacote Volo.Abp.AspNetCore.Mvc.UI.Bundling

> Este pacote já está instalado por padrão com os templates de inicialização. Então, na maioria das vezes, você não precisa instalá-lo manualmente.

Se você não estiver usando um template de inicialização, pode usar o ABP CLI para instalá-lo no seu projeto. Execute o seguinte comando na pasta que contém o arquivo .csproj do seu projeto:

`````
abp add-package Volo.Abp.AspNetCore.Mvc.UI.Bundling
`````

> Se você ainda não fez isso, primeiro precisa instalar o ABP CLI. Para outras opções de instalação, veja [a página de descrição do pacote](https://abp.io/package-detail/Volo.Abp.AspNetCore.Mvc.UI.Bundling).

## Tag Helpers de Agrupamento no Razor

A maneira mais simples de criar um grupo é usar os tag helpers `abp-script-bundle` ou `abp-style-bundle`. Exemplo:

`````html
<abp-style-bundle name="MyGlobalBundle">
    <abp-style src="/libs/bootstrap/css/bootstrap.css" />
    <abp-style src="/libs/font-awesome/css/font-awesome.css" />
    <abp-style src="/libs/toastr/toastr.css" />
    <abp-style src="/styles/my-global-style.css" />
</abp-style-bundle>
`````

Este grupo define um grupo de estilo com um **nome único**: `MyGlobalBundle`. É muito fácil entender como usá-lo. Vamos ver como ele *funciona*:

* O ABP cria o grupo como **lazy** a partir dos arquivos fornecidos quando é **solicitado pela primeira vez**. Para as chamadas subsequentes, ele é retornado do **cache**. Isso significa que se você adicionar condicionalmente os arquivos ao grupo, isso será executado apenas uma vez e quaisquer alterações na condição não afetarão o grupo para as próximas solicitações.
* O ABP adiciona arquivos do grupo **individualmente** à página para o ambiente de `desenvolvimento`. Ele agrupa e minifica automaticamente para outros ambientes (`staging`, `produção`...). Veja a seção *Modo de Agrupamento* para alterar esse comportamento.
* Os arquivos do grupo podem ser arquivos **físicos** ou arquivos [**virtuais/embutidos**](../../Virtual-File-System.md).
* O ABP adiciona automaticamente uma **string de consulta de versão** ao URL do arquivo do grupo para evitar que os navegadores armazenem em cache quando o grupo está sendo atualizado. (como ?_v=67872834243042 - gerado a partir da data da última alteração dos arquivos relacionados). A versionamento funciona mesmo que os arquivos do grupo sejam adicionados individualmente à página (no ambiente de desenvolvimento).

### Importando os Tag Helpers de Agrupamento

> Isso já é importado por padrão com os templates de inicialização. Então, na maioria das vezes, você não precisa adicioná-lo manualmente.

Para usar os tag helpers de grupo, você precisa adicioná-lo ao seu arquivo `_ViewImports.cshtml` ou à sua página:

``````
@addTagHelper *, Volo.Abp.AspNetCore.Mvc.UI.Bundling
``````

### Grupos Sem Nome

O `name` é **opcional** para os tag helpers de grupo no razor. Se você não definir um nome, ele será automaticamente **calculado** com base nos nomes dos arquivos do grupo usados (eles são **concatenados** e **hashados**). Exemplo:

`````html
<abp-style-bundle>
    <abp-style src="/libs/bootstrap/css/bootstrap.css" />
    <abp-style src="/libs/font-awesome/css/font-awesome.css" />
    <abp-style src="/libs/toastr/toastr.css" />
    @if (ViewBag.IncludeCustomStyles != false)
    {
        <abp-style src="/styles/my-global-style.css" />
    }
</abp-style-bundle>
`````

Isso potencialmente criará **dois grupos diferentes** (um inclui o `my-global-style.css` e o outro não).

Vantagens dos grupos **sem nome**:

* Pode **adicionar itens condicionalmente** ao grupo. Mas isso pode levar a várias variações do grupo com base nas condições.

Vantagens dos grupos **com nome**:

* Outros **módulos podem contribuir** para o grupo pelo seu nome (veja as seções abaixo).

### Arquivo Único

Se você precisar apenas adicionar um único arquivo à página, pode usar a tag `abp-script` ou `abp-style` sem envolver na tag `abp-script-bundle` ou `abp-style-bundle`. Exemplo:

`````xml
<abp-script src="/scripts/my-script.js" />
`````

O nome do grupo será *scripts.my-scripts* para o exemplo acima ("/" é substituído por "."). Todos os recursos de agrupamento funcionam como esperado para grupos de arquivo único também.

## Opções de Agrupamento

Se você precisar usar o mesmo grupo em **múltiplas páginas** ou quiser usar alguns recursos mais **poderosos**, pode configurar grupos **por código** na sua classe de [módulo](../../Module-Development-Basics.md).

### Criando Um Novo Grupo

Exemplo de uso:

`````C#
[DependsOn(typeof(AbpAspNetCoreMvcUiBundlingModule))]
public class MyWebModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpBundlingOptions>(options =>
        {
            options
                .ScriptBundles
                .Add("MyGlobalBundle", bundle => {
                    bundle.AddFiles(
                        "/libs/jquery/jquery.js",
                        "/libs/bootstrap/js/bootstrap.js",
                        "/libs/toastr/toastr.min.js",
                        "/scripts/my-global-scripts.js"
                    );
                });                
        });
    }
}
`````

> Você pode usar o mesmo nome (*MyGlobalBundle* aqui) para um grupo de script e estilo, pois eles são adicionados a coleções diferentes (`ScriptBundles` e `StyleBundles`).

Depois de definir um grupo assim, ele pode ser incluído em uma página usando os mesmos tag helpers definidos acima. Exemplo:

`````html
<abp-script-bundle name="MyGlobalBundle" />
`````

Desta vez, nenhum arquivo foi definido na definição do tag helper porque os arquivos do grupo são definidos pelo código.

### Configurando Um Grupo Existente

O ABP suporta [modularidade](../../Module-Development-Basics.md) para agrupamento também. Um módulo pode modificar um grupo existente que foi criado por um módulo dependente. Exemplo:

`````C#
[DependsOn(typeof(MyWebModule))]
public class MyWebExtensionModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpBundlingOptions>(options =>
        {
            options
                .ScriptBundles
                .Configure("MyGlobalBundle", bundle => {
                    bundle.AddFiles(
                        "/scripts/my-extension-script.js"
                    );
                });
        });
    }
}
`````

Você também pode usar o método `ConfigureAll` para configurar todos os grupos existentes:

`````C#
[DependsOn(typeof(MyWebModule))]
public class MyWebExtensionModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpBundlingOptions>(options =>
        {
            options
                .ScriptBundles
                .ConfigureAll(bundle => {
                    bundle.AddFiles(
                        "/scripts/my-extension-script.js"
                    );
                });
        });
    }
}
`````

## Contribuidores de Grupo

Adicionar arquivos a um grupo existente parece útil. E se você precisar **substituir** um arquivo no grupo ou quiser **adicionar arquivos condicionalmente**? Definir um contribuidor de grupo fornece poder extra para esses casos.

Um exemplo de contribuidor de grupo que substitui bootstrap.css por uma versão personalizada:

`````C#
public class MyExtensionGlobalStyleContributor : BundleContributor
{
    public override void ConfigureBundle(BundleConfigurationContext context)
    {
        context.Files.ReplaceOne(
            "/libs/bootstrap/css/bootstrap.css",
            "/styles/extensions/bootstrap-customized.css"
        );
    }
}
`````

Então você pode usar este contribuidor como abaixo:

`````C#
services.Configure<AbpBundlingOptions>(options =>
{
    options
        .ScriptBundles
        .Configure("MyGlobalBundle", bundle => {
            bundle.AddContributors(typeof(MyExtensionGlobalStyleContributor));
        });
});
`````

> Você também pode adicionar contribuidores ao criar um novo grupo.

Os contribuidores também podem ser usados nos tag helpers de grupo. Exemplo:

`````xml
<abp-style-bundle>
    <abp-style type="@typeof(BootstrapStyleContributor)" />
    <abp-style src="/libs/font-awesome/css/font-awesome.css" />
    <abp-style src="/libs/toastr/toastr.css" />
</abp-style-bundle>
`````

As tags `abp-style` e `abp-script` podem receber atributos `type` (em vez de atributos `src`) como mostrado neste exemplo. Quando você adiciona um contribuidor de grupo, suas dependências também são automaticamente adicionadas ao grupo.

### Dependências de Contribuidor

Um contribuidor de grupo pode ter uma ou mais dependências de outros contribuidores. 
Exemplo:

`````C#
[DependsOn(typeof(MyDependedBundleContributor))] //Define a dependência
public class MyExtensionStyleBundleContributor : BundleContributor
{
    //...
}
`````

Quando um contribuidor de grupo é adicionado, suas dependências são **automaticamente e recursivamente** adicionadas. As dependências são adicionadas pela **ordem de dependência** evitando **duplicatas**. As duplicatas são evitadas mesmo que estejam em grupos separados. O ABP organiza todos os grupos em uma página e elimina duplicações.

Criar contribuidores e definir dependências é uma maneira de organizar a criação de grupos em diferentes módulos.

### Extensões de Contribuidor

Em alguns cenários avançados, você pode querer fazer alguma configuração adicional sempre que um contribuidor de grupo for usado. As extensões de contribuidor funcionam perfeitamente quando o contribuidor estendido é usado.

O exemplo abaixo adiciona alguns estilos para a biblioteca prism.js:

`````csharp
public class MyPrismjsStyleExtension : BundleContributor
{
    public override void ConfigureBundle(BundleConfigurationContext context)
    {
        context.Files.AddIfNotContains("/libs/prismjs/plugins/toolbar/prism-toolbar.css");
    }
}
`````

Então você pode configurar `AbpBundleContributorOptions` para estender o `PrismjsStyleBundleContributor` existente.

`````csharp
Configure<AbpBundleContributorOptions>(options =>
{
    options
        .Extensions<PrismjsStyleBundleContributor>()
        .Add<MyPrismjsStyleExtension>();
});
`````

Sempre que `PrismjsStyleBundleContributor` for adicionado a um grupo, `MyPrismjsStyleExtension` também será automaticamente adicionado.

### Acessando o IServiceProvider

Embora raramente seja necessário, `BundleConfigurationContext` tem uma propriedade `ServiceProvider` que você pode resolver dependências de serviço dentro do método `ConfigureBundle`.

### Contribuidores de Pacote Padrão

Adicionar um recurso específico de um pacote NPM (arquivos js, css) a um grupo é bastante direto para esse pacote. Por exemplo, você sempre adiciona o arquivo `bootstrap.css` para o pacote NPM bootstrap.

Existem contribuidores integrados para todos os [pacotes NPM padrão](Client-Side-Package-Management.md). Por exemplo, se o seu contribuidor depende do bootstrap, você pode simplesmente declará-lo, em vez de adicionar o bootstrap.css você mesmo.

`````C#
[DependsOn(typeof(BootstrapStyleContributor))] //Define a dependência de estilo do bootstrap
public class MyExtensionStyleBundleContributor : BundleContributor
{
    //...
}
`````

Usar os contribuidores integrados para pacotes padrão;

* Evita que você digite **os caminhos de recursos inválidos**.
* Evita mudar seu contribuidor se o **caminho do recurso mudar** (o contribuidor dependente lidará com isso).
* Evita que vários módulos adicionem **arquivos duplicados**.
* Gerencia **dependências recursivamente** (adiciona dependências de dependências, se necessário).

#### Pacote Volo.Abp.AspNetCore.Mvc.UI.Packages

> Este pacote já está instalado por padrão nos templates de inicialização. Então, na maioria das vezes, você não precisa instalá-lo manualmente.

Se você não estiver usando um template de inicialização, pode usar o [ABP CLI](../../CLI.md) para instalá-lo no seu projeto. Execute o seguinte comando na pasta que contém o arquivo .csproj do seu projeto:

``````
abp add-package Volo.Abp.AspNetCore.Mvc.UI.Packages
``````

> Se você ainda não fez isso, primeiro precisa instalar o [ABP CLI](../../CLI.md). Para outras opções de instalação, veja [a página de descrição do pacote](https://abp.io/package-detail/Volo.Abp.AspNetCore.Mvc.UI.Packages).

### Herança de Grupo

Em alguns casos específicos, pode ser necessário criar um **novo** grupo **herdado** de outros grupos. Herdar de um grupo (recursivamente) herda todos os arquivos/contribuidores desse grupo. Então o grupo derivado pode adicionar ou modificar arquivos/contribuidores **sem modificar** o grupo original. 
Exemplo:

`````c#
services.Configure<AbpBundlingOptions>(options =>
{
    options
        .StyleBundles
        .Add("MyTheme.MyGlobalBundle", bundle => {
            bundle
                .AddBaseBundles("MyGlobalBundle") //Pode adicionar múltiplos
                .AddFiles(
                    "/styles/mytheme-global-styles.css"
                );
        });
});
`````

## Opções Adicionais

Esta seção mostra outras opções úteis para o sistema de agrupamento e minificação.

### Modo de Agrupamento

O ABP adiciona arquivos do grupo individualmente à página para o ambiente de `desenvolvimento`. Ele agrupa e minifica automaticamente para outros ambientes (`staging`, `produção`...). Na maioria das vezes, esse é o comportamento que você deseja. No entanto, você pode querer configurá-lo manualmente em alguns casos. Existem quatro modos;

* `Auto`: Determina automaticamente o modo com base no ambiente.
* `None`: Sem agrupamento ou minificação.
* `Bundle`: Agrupado, mas não minificado.
* `BundleAndMinify`: Agrupado e minificado.

Você pode configurar `AbpBundlingOptions` no `ConfigureServices` do seu [módulo](../../Module-Development-Basics.md).

**Exemplo:**

`````csharp
Configure<AbpBundlingOptions>(options =>
{
    options.Mode = BundlingMode.Bundle;
});
`````

### Ignorar Para Minificação

É possível ignorar um arquivo específico para a minificação.

**Exemplo:**

`````csharp
Configure<AbpBundlingOptions>(options =>
{
    options.MinificationIgnoredFiles.Add("/scripts/myscript.js");
});
`````

O arquivo fornecido ainda é adicionado ao grupo, mas não é minificado neste caso.

### Carregar JavaScript e CSS de forma assíncrona

Você pode configurar `AbpBundlingOptions` para carregar todos ou um único arquivo js/css de forma assíncrona.

**Exemplo:**

`````csharp
Configure<AbpBundlingOptions>(options =>
{
    options.PreloadStyles.Add("/__bundles/Basic.Global");
    options.DeferScriptsByDefault = true;
});
`````

**HTML de Saída:**
`````html
<link rel="preload" href="/__bundles/Basic.Global.F4FA61F368098407A4C972D0A6914137.css?_v=637697363694828051" as="style" onload="this.rel='stylesheet'"/>

<script defer src="/libs/timeago/locales/jquery.timeago.en.js?_v=637674729040000000"></script>
`````

### Suporte a Arquivos Externos/CDN

O sistema de agrupamento reconhece automaticamente os arquivos externos/CDN e os adiciona à página sem qualquer alteração.

#### Usando Arquivos Externos/CDN em `AbpBundlingOptions`

`````csharp
Configure<AbpBundlingOptions>(options =>
{
    options.StyleBundles
        .Add("MyStyleBundle", configuration =>
        {
            configuration
                .AddFiles("/styles/my-style1.css")
                .AddFiles("/styles/my-style2.css")
                .AddFiles("https://cdn.abp.io/bootstrap.css")
                .AddFiles("/styles/my-style3.css")
                .AddFiles("/styles/my-style4.css");
        });

    options.ScriptBundles
        .Add("MyScriptBundle", configuration =>
        {
            configuration
                .AddFiles("/scripts/my-script1.js")
                .AddFiles("/scripts/my-script2.js")
                .AddFiles("https://cdn.abp.io/bootstrap.js")
                .AddFiles("/scripts/my-script3.js")
                .AddFiles("/scripts/my-script4.js");
        });
});
`````

**HTML de Saída:**

`````html
<link rel="stylesheet" href="/__bundles/MyStyleBundle.EA8C28419DCA43363E9670973D4C0D15.css?_v=638331889644609730" />
<link rel="stylesheet" href="https://cdn.abp.io/bootstrap.css" />
<link rel="stylesheet" href="/__bundles/MyStyleBundle.AC2E0AA6C461A0949A1295E9BDAC049C.css?_v=638331889644623860" />

<script src="/__bundles/MyScriptBundle.C993366DF8840E08228F3EE685CB08E8.js?_v=638331889644937120"></script>
<script src="https://cdn.abp.io/bootstrap.js"></script>
<script src="/__bundles/MyScriptBundle.2E8D0FDC6334D2A6B847393A801525B7.js?_v=638331889644943970"></script>
`````

#### Usando Arquivos Externos/CDN em Tag Helpers.

`````html
<abp-style-bundle name="MyStyleBundle">
    <abp-style src="/styles/my-style1.css" />
    <abp-style src="/styles/my-style2.css" />
    <abp-style src="https://cdn.abp.io/bootstrap.css" />
    <abp-style src="/styles/my-style3.css" />
    <abp-style src="/styles/my-style4.css" />
</abp-style-bundle>

<abp-script-bundle name="MyScriptBundle">
    <abp-script src="/scripts/my-script1.js" />
    <abp-script src="/scripts/my-script2.js" />
    <abp-script src="https://cdn.abp.io/bootstrap.js" />
    <abp-script src="/scripts/my-script3.js" />
    <abp-script src="/scripts/my-script4.js" />
</abp-script-bundle>
`````

**HTML de Saída:**

`````html
<link rel="stylesheet" href="/__bundles/MyStyleBundle.C60C7B9C1F539659623BB6E7227A7C45.css?_v=638331889645002500" />
<link rel="stylesheet" href="https://cdn.abp.io/bootstrap.css" />
<link rel="stylesheet" href="/__bundles/MyStyleBundle.464328A06039091534650B0E049904C6.css?_v=638331889645012300" />

<script src="/__bundles/MyScriptBundle.55FDCBF2DCB9E0767AE6FA7487594106.js?_v=638331889645050410"></script>
<script src="https://cdn.abp.io/bootstrap.js"></script>
<script src="/__bundles/MyScriptBundle.191CB68AB4F41C8BF3A7AE422F19A3D2.js?_v=638331889645055490"></script>
`````

## Temas

Os temas usam os contribuidores de pacotes padrão para adicionar recursos de biblioteca aos layouts de página. Os temas também podem definir alguns grupos padrão/globais, para que qualquer módulo possa contribuir para esses grupos padrão/globais. Veja a [documentação de temas](Theming.md) para mais.

## Melhores Práticas e Sugestões

É sugerido definir vários grupos para um aplicativo, cada um usado para diferentes propósitos.

* **Grupo Global**: Grupos de estilo/script globais são incluídos em todas as páginas do aplicativo. Os temas já definem grupos de estilo e script globais. Seu módulo pode contribuir para eles.
* **Grupos de Layout**: Este é um grupo específico para um layout individual. Contém apenas recursos compartilhados entre todas as páginas que usam o layout. Use os tag helpers de agrupamento para criar o grupo como uma boa prática.
* **Grupos de Módulo**: Para recursos compartilhados entre as páginas de um módulo individual.
* **Grupos de Página**: Grupos específicos criados para cada página. Use os tag helpers de agrupamento para criar o grupo como uma boa prática.

Estabeleça um equilíbrio entre desempenho, uso de largura de banda de rede e quantidade de muitos grupos.

## Veja Também

* [Gerenciamento de Pacotes do Lado do Cliente](/Guia-de-Desenvolvimento/Projeto-Web/Razor-Pages/Gerenciamento_de_Pacotes_do_Lado_do_Cliente)
* [Temas](/Guia-de-Desenvolvimento/Projeto-Web/Razor-Pages/Tematização)
