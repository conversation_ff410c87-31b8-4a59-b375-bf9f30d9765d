[[_TOC_]]
# Gitflow Workflow

O controle de versão do código-fonte do projeto TRF3.SISPREC seguirá o Gitflow Workflow. Leia a seguir a regras do projeto e também a descrição do processo Gitflow Workflow.

## Regras para criação de branches e pull requests (PRs)
- Para implementar as task de novas funcionalidades, as branches deverão ser criadas com base na branch "desenvolvimento" e deverão ter o prefixo "feature/<NUMERO_TASK>_nome_da_branch"
  - Após finalizar a implementação, um Pull Request deverá ser criado com destino para a branch "desenvolvimento". Uma pipeline será disparada. Aguarde a conclusão da pipeline com sucesso e observe o resultado da análise no sonarqube. Caso a análise não passe pelo quality gate, implemente os ajustes necessários e faça um novo commit, o qual irá disparar uma nova análise. Quando a pipeline do PR terminar com sucesso e o quality gate do sonarqube também passar com sucesso, aprove e conclua o PR.
- Para implementar as task de correção de bugs, as branches deverão ser criadas com base na branch "main" e deverão ter o formato "bugfix/<NUMERO_TASK>_nome_da_branch"
  - Após finalizar a implementação, dois Pull Requests deverão ser criados, um com destino para a branch "main" e outro para a branch "desenvolvimento". Para ambos PRs, serão disparados pipelines. Aguarde a conclusão da pipeline com sucesso e observe o resultado da análise no sonarqube. Caso a análise não passe pelo quality gate, implemente os ajustes necessários e faça um novo commit, o qual irá disparar uma nova análise. Quando a pipeline do PR terminar com sucesso e o quality gate do sonarqube também passar, aprove e conclua o PR.

**Boas práticas Antes de realizar um PR:**
  - atualize sua branch com um merge da branch de destino na branch atual, para evitar problemas como conflitos entre arquivos e erros de build.
    - IMPORTANTE: Caso tenha muito código a ser atualizado no merge, para não correr risco de perder código implementado por eventual conflito, recomendo, antes de tudo, fazer um commit na branch local, um push dela, e uma cópia da pasta antes de fazer o merge.
- Execute os testes automatizados e verfique o resultado. Isso evitará problemas na pipeline, como erros de build e falhas nos testes.


O **Gitflow Workflow** é uma estratégia para controle de versionamento de código durante o desenvolvimento e manutenção de software que emprega o sistema de controle de versão Git, oferecendo uma estrutura clara para gerenciar projetos de software complexos. 

Esta metodologia é apreciada por suas diretrizes definidas, que simplificam a coordenação de tarefas e a implementação de novas funcionalidades e de correções.

É possível visualizar o fluxo na imagem abaixo:

<img src="https://nvie.com/img/<EMAIL>" style="width: 90%; height: auto;"/>

## Principais Objetivos

1. **Estruturação e Organização:** Gitflow sugere a criação de branches dedicados para propósitos específicos—como `funcionalidade`, `desenvolvimento`, `release`, `bugfix`, e `main`. Por exemplo, para adicionar uma nova funcionalidade, um desenvolvedor cria uma branch implementá-la a partir da branch `desenvolvimento`, assegurando que o trabalho em andamento não afete o código estável (`main`).
   
2. **Paralelismo no Desenvolvimento:** Permite que a equipe trabalhe em várias funcionalidades ao mesmo tempo sem interferência, utilizando branches `funcionalidade` separados. Imagine um desenvolvedor implementando uma nova interface de usuário enquanto outro ajusta a lógica de negócios; ambos podem avançar sem conflitos.
   
3. **Gestão de Lançamentos:** Quando uma versão está pronta para ser lançada, uma branch `release` é criada a partir do `desenvolvimento`. Isso facilita ajustes finais e testes específicos para o lançamento. Por exemplo, antes de lançar a versão 2.0, pequenos bugs podem ser corrigidos diretamente no branch `release`, sem interromper o fluxo principal de desenvolvimento.

4. **Integração Contínua:** Alterações são regularmente integradas ao branch `desenvolvimento`, e, eventualmente, ao `main`, garantindo que o produto final esteja sempre testado e estável. Um exemplo seria configurar um pipeline de CI/CD para automatizar testes e compilações sempre que mudanças são confirmadas, além de deploy automático em diferentes ambientes, como homologação e produção.

## Principais Benefícios

1. **Melhoria na Colaboração:** A definição clara do propósito de cada branch evita confusões sobre onde submeter mudanças. Se alguém corrige um bug importante, sabe que deve utilizar um branch `bugfix` que depois é integrado tanto no `desenvolvimento` quanto no `main`.
   
2. **Estabilidade do Código:** A separação das branches de desenvolvimento e produção mantém o código em `main` livre de bugs inesperados. Isso significa que uma funcionalidade em teste no `desenvolvimento` não afetará a versão atual em produção.
   
3. **Flexibilidade em Lançamentos:** O branch `release` permite preparar um lançamento sem interromper o desenvolvimento principal. Assim, é possível preparar a versão 2.1 enquanto a equipe ainda trabalha nas funcionalidades da versão 2.2.
   
4. **Facilidade na Manutenção:** Bugs críticos podem ser rapidamente corrigidos através de branches `bugfix`, que são posteriormente integrados às branches relevantes. Um exemplo prático seria a rápida solução de um problema de segurança sem atrasar outros trabalhos em andamento.
   
5. **Histórico de Versões Claro:** O Gitflow promove um registro detalhado de mudanças, facilitando o rastreamento de quando e por que cada funcionalidade foi introduzida ou modificada. Por exemplo, é possível acompanhar a evolução do projeto desde seu lançamento inicial até a versão atual, 2.3.

