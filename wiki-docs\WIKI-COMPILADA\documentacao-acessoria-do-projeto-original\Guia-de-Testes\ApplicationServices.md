[[_TOC_]]

# Guia de Testes de Application Services

Este guia demonstra como implementar testes de integração para serviços de aplicação (Application Services) no ABP Framework 8.

## Estrutura de Testes

### Bibliotecas Utilizadas

- **xUnit**: Framework de testes
- **Shouldly**: Biblioteca para asserções mais legíveis e expressivas
- **NSubstitute**: Framework para criação de mocks
- **Bogus**: Biblioteca para geração de dados fictícios
- **SQLite**: Banco de dados em memória para testes de integração

### Organização dos Testes

```
test/
├── TRF3.SISPREC.EntityFrameworkCore.Tests/  # Testes de integração
    └── EntityFrameworkCore/
        └── Applications/                     # Testes de Application Services
```

## Configuração do Ambiente

### Classe Base para Testes

```csharp
public class BaseAppServiceTests<TStartupModule> : SISPRECTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    // Configuração básica herdada por todas as classes de teste
    protected IServiceProvider ServiceProvider { get; }
    
    protected T GetRequiredService<T>() where T : notnull
    {
        return ServiceProvider.GetRequiredService<T>();
    }
}
```

### Uso do WithUnitOfWorkAsync

O método `WithUnitOfWorkAsync` é essencial para testes que envolvem operações de banco de dados. Ele garante que as operações sejam executadas dentro de uma transação.

**IMPORTANTE**: Sempre use o `.Wait()` após o `WithUnitOfWorkAsync` no construtor para garantir que qualquer erro durante a inserção dos registros seja lançado imediatamente:

```csharp
public class AutorAppServiceTests : BaseAppServiceTests<SISPRECEntityFrameworkCoreTestModule>
{
    private readonly IAutorAppService _appService;
    private Autor autorObj1;

    public AutorAppServiceTests()
    {
        _appService = GetRequiredService<IAutorAppService>();

        // Criando dados de teste dentro de uma transação
        WithUnitOfWorkAsync(async () =>
        {
            var autor = new Autor { Nome = "Teste" };
            autorObj1 = await _repository.InsertAsync(autor, true);
        })
        .Wait(); // Importante: garante que erros na inserção sejam lançados
    }
}
```

## Casos de Uso Comuns

### 1. Testando Operações CRUD

```csharp
[Fact]
public async Task Criar_Entidade_Deve_Passar()
{
    // Arrange
    var input = new CreateUpdateAutorDto
    {
        Nome = new Faker().Random.Hash(),
        Email = new Faker().Internet.Email()
    };

    // Act
    var result = await _appService.CreateAsync(input);

    // Assert
    result.ShouldNotBeNull();
    result.AutorId.ShouldBeGreaterThan(0);
    result.Nome.ShouldBe(input.Nome);
}
```

### 2. Testando Validações e Exceções

```csharp
[Fact]
public async Task Criar_Entidade_Com_Dados_Invalidos_Deve_Falhar()
{
    // Arrange
    var input = new CreateUpdateAutorDto
    {
        Nome = string.Empty // Nome é obrigatório
    };

    // Act & Assert
    await Should.ThrowAsync<AbpValidationException>(
        async () => await _appService.CreateAsync(input)
    );
}
```

### 3. Testando Consultas e Filtros

```csharp
[Fact]
public async Task Filtrar_Entidades_Deve_Retornar_Resultados_Corretos()
{
    // Arrange
    var input = new GetAutorListInput
    {
        Filter = "João",
        MaxResultCount = 10,
        SkipCount = 0,
        Sorting = "Nome"
    };

    // Act
    var result = await _appService.GetListAsync(input);

    // Assert
    result.Items.ShouldNotBeEmpty();
    result.Items.ShouldAllBe(a => a.Nome.Contains(input.Filter));
}
```

### 4. Testando Regras de Negócio Complexas

```csharp
[Fact]
public async Task Validar_Regra_Negocio_Especifica()
{
    // Arrange
    var input = new ProcessarRequisicaoDto
    {
        Valor = 1000,
        TipoRequisicao = "RPV"
    };

    // Act
    var result = await _appService.ProcessarRequisicaoAsync(input);

    // Assert
    result.Status.ShouldBe(StatusProcessamento.Aprovado);
    result.JustificativaProcessamento.ShouldNotBeNullOrEmpty();
}
```

## Boas Práticas

### 1. Nomenclatura de Testes
- Use nomes descritivos que indicam o cenário testado
- Siga o padrão `Metodo_Cenario_ResultadoEsperado`
- Use termos em português para manter consistência

### 2. Organização do Código
- Divida os testes em Arrange, Act e Assert
- Use comentários para explicar partes complexas
- Mantenha os testes focados e concisos

### 3. Geração de Dados
```csharp
private Autor CriarAutorFake()
{
    return new Faker<Autor>()
        .RuleFor(a => a.Nome, f => f.Random.Hash())
        .RuleFor(a => a.Email, f => f.Internet.Email())
        .Generate();
}
```

### 4. Uso do Shouldly
```csharp
// Preferir
result.Nome.ShouldBe("Esperado");
result.Items.ShouldNotBeEmpty();

// Ao invés de
Assert.Equal("Esperado", result.Nome);
Assert.NotEmpty(result.Items);
```

### 5. Tratamento de Exceções
```csharp
var exception = await Should.ThrowAsync<UserFriendlyException>(
    async () => await _appService.DeleteAsync(id)
);
exception.Message.ShouldContain("não pode ser excluído");
```

## Exemplos Práticos

### Exemplo 1: Teste Completo de CRUD

```csharp
public class LivroAppServiceTests : BaseAppServiceTests<SISPRECEntityFrameworkCoreTestModule>
{
    private readonly ILivroAppService _appService;
    private readonly ILivroRepository _repository;

    public LivroAppServiceTests()
    {
        _appService = GetRequiredService<ILivroAppService>();
        _repository = GetRequiredService<ILivroRepository>();

        // Criando dados iniciais
        WithUnitOfWorkAsync(async () =>
        {
            // Código de inicialização
        })
        .Wait(); // Importante: garante que erros na inicialização sejam lançados
    }

    [Fact]
    public async Task CRUD_Completo_Deve_Funcionar()
    {
        // Arrange - Create
        var createDto = new CreateUpdateLivroDto
        {
            Titulo = new Faker().Random.Hash(),
            Preco = 29.90m
        };

        // Act & Assert - Create
        var created = await _appService.CreateAsync(createDto);
        created.ShouldNotBeNull();
        created.LivroId.ShouldBeGreaterThan(0);

        // Act & Assert - Get
        var retrieved = await _appService.GetAsync(created.LivroId);
        retrieved.ShouldNotBeNull();
        retrieved.Titulo.ShouldBe(createDto.Titulo);

        // Arrange - Update
        var updateDto = new CreateUpdateLivroDto
        {
            Titulo = new Faker().Random.Hash(),
            Preco = 39.90m
        };

        // Act & Assert - Update
        var updated = await _appService.UpdateAsync(created.LivroId, updateDto);
        updated.ShouldNotBeNull();
        updated.Titulo.ShouldBe(updateDto.Titulo);

        // Act & Assert - Delete
        await _appService.DeleteAsync(created.LivroId);
        var deleted = await _repository.FindAsync(created.LivroId);
        deleted.ShouldBeNull();
    }
}
```

### Exemplo 2: Teste de Regras de Negócio

```csharp
public class RequisicaoAppServiceTests : BaseAppServiceTests<SISPRECEntityFrameworkCoreTestModule>
{
    private readonly IRequisicaoAppService _appService;

    public RequisicaoAppServiceTests()
    {
        _appService = GetRequiredService<IRequisicaoAppService>();

        // Inicializando dados necessários
        WithUnitOfWorkAsync(async () =>
        {
            // Código de inicialização
        })
        .Wait(); // Importante: garante que erros na inicialização sejam lançados
    }

    [Fact]
    public async Task Validar_Limite_Valor_RPV()
    {
        // Arrange
        var dto = new CreateRequisicaoDto
        {
            Valor = 70000, // Acima do limite de RPV
            TipoRequisicao = "RPV"
        };

        // Act & Assert
        var exception = await Should.ThrowAsync<BusinessException>(
            async () => await _appService.CreateAsync(dto)
        );

        exception.Code.ShouldBe(SISPRECErrorCodes.ValorAcimaLimiteRPV);
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-100)]
    public async Task Validar_Valor_Positivo(decimal valorInvalido)
    {
        // Arrange
        var dto = new CreateRequisicaoDto
        {
            Valor = valorInvalido,
            TipoRequisicao = "RPV"
        };

        // Act & Assert
        await Should.ThrowAsync<AbpValidationException>(
            async () => await _appService.CreateAsync(dto)
        );
    }
}
```

## Dicas Importantes

1. **Banco de Dados**
   - Use SQLite em memória para testes mais rápidos
   - Aproveite a carga básica de dados disponível
   - Limpe dados criados após cada teste

2. **Asserts**
   - Prefira Shouldly para mensagens de erro mais claras
   - Verifique todos os aspectos relevantes do resultado
   - Não esqueça de testar cenários de erro

3. **Dados de Teste**
   - Use Bogus com `Random.Hash()` para strings
   - Crie métodos auxiliares para dados comuns
   - Mantenha os dados realistas mas simples

4. **Performance**
   - Evite criar dados desnecessários
   - Use `WithUnitOfWorkAsync` adequadamente
   - Mantenha os testes focados e independentes

5. **Construtor**
   - Sempre use `.Wait()` após `WithUnitOfWorkAsync` no construtor
   - Isso garante que erros durante a inserção de registros sejam lançados imediatamente
   - Facilita a identificação de problemas na inicialização dos testes

## Utilizando a Carga de Dados Básica

O projeto possui uma carga básica de dados em SQLite para facilitar os testes de integração. Esta seção explica como utilizar esses dados pré-carregados de forma eficiente.

### Localização e Estrutura

- A carga de dados está definida em: `test\TRF3.SISPREC.EntityFrameworkCore.Tests\EntityFrameworkCore\Sql\CARGA_BASICA_SQLITE.sql`
- Os IDs dos registros inseridos estão disponíveis como constantes na classe `SISPRECTestConsts`

### Como Utilizar

1. **Acessando Registros Pré-carregados**:
```csharp
public class RequisicaoAppServiceTests : BaseAppServiceTests<SISPRECEntityFrameworkCoreTestModule>
{
    private readonly IRequisicaoAppService _appService;
    private readonly IRequisicaoRepository _repository;

    public RequisicaoAppServiceTests()
    {
        _appService = GetRequiredService<IRequisicaoAppService>();
        _repository = GetRequiredService<IRequisicaoRepository>();

        // Usando ID pré-carregado via constante
        WithUnitOfWorkAsync(async () =>
        {
            var requisicao = await _repository.GetAsync(
                r => r.NumeroProtocolo == SISPRECTestConsts.NumeroProtocoloRequisicao
            );
            // Usar a requisição carregada...
        })
        .Wait(); // Importante: garante que erros na inicialização sejam lançados
    }
}
```

2. **Evitando Conflitos de Chaves**:
```csharp
public class PessoaAppServiceTests : BaseAppServiceTests<SISPRECEntityFrameworkCoreTestModule>
{
    [Fact]
    public async Task Criar_Pessoa_Deve_Passar()
    {
        // Arrange - Usar ID que não conflite com dados pré-carregados
        var input = new CreateUpdatePessoaDto
        {
            Nome = new Faker().Random.Hash(),
            // Usar constante para MunicipioId pré-carregado
            MunicipioId = SISPRECTestConsts.MunicipioId
        };

        // Act & Assert...
    }
}
```

### Boas Práticas

1. **Uso de Constantes**:
   - Sempre use as constantes de `SISPRECTestConsts` para referenciar IDs pré-carregados
   - Evite usar números mágicos ou strings literais para IDs

2. **Dados Relacionados**:
   - Aproveite os relacionamentos já existentes na carga básica
   - Exemplo: Se precisar de uma Requisição com Partes, use o número de protocolo '20240086838' que já possui esses relacionamentos

3. **Tratamento de Erros**:
```csharp
public class ExemploAppServiceTests : BaseAppServiceTests<SISPRECEntityFrameworkCoreTestModule>
{
    public ExemploAppServiceTests()
    {
        WithUnitOfWorkAsync(async () =>
        {
            try
            {
                // Tentativa de carregar dados pré-existentes
                var entidade = await _repository.GetAsync(SISPRECTestConsts.EntidadeId);
            }
            catch (EntityNotFoundException)
            {
                // Criar nova entidade se necessário
                entidade = await _repository.InsertAsync(
                    new Entidade { /* ... */ },
                    autoSave: true
                );
            }
        })
        .Wait(); // Importante: garante que erros sejam lançados
    }
}
```

### Tabelas com Dados Pré-carregados

A carga básica inclui dados para diversas tabelas. Algumas das principais são:

- **REQ_REQUISICAO_PROTOCOLO**: Requisições de protocolo completas
- **REQ_REQUISICAO_PARTE**: Partes de requisições (requerentes e réus)
- **CJF_Unidade_Judici**: Unidades judiciais
- **TRF_MUNICIPIO**: Municípios
- **TRF_UF**: Unidades federativas
- **REQ_Pessoa**: Pessoas
- **REQ_Tipo_Procedimento**: Tipos de procedimento (PRC, RPV)

### Observações Importantes

1. **Uso dos IDs**:
   - **SEMPRE** use os IDs através das constantes em `SISPRECTestConsts`
   - Se precisar de um ID que não está nas constantes, considere adicioná-lo

2. **Duplicação de Chaves**:
   - Se um teste falhar por duplicação de chave (gerada aleatoriamente pelo Bogus), refatore-o para usar um registro já inserido
   - Evite gerar IDs aleatórios quando existir um registro adequado na carga básica

3. **Manutenção da Carga**:
   - Se a estrutura do banco for alterada, atualize o arquivo SQL da carga básica
   - Mantenha a documentação das constantes atualizada em `SISPRECTestConsts`

4. **Criação de Novos Registros**:
   - Antes de criar novos registros no construtor, verifique se não existe um registro adequado na carga básica
   - Se criar novos registros for necessário, considere adicioná-los à carga básica para reuso
