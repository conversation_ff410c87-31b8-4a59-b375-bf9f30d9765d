# Carousel

## Introdução

`abp-carousel` é a tag abp para o elemento carousel.

Uso básico:

````html
<abp-carousel>
    <abp-carousel-item src=""></abp-carousel-item>
    <abp-carousel-item src=""></abp-carousel-item>
    <abp-carousel-item src=""></abp-carousel-item>
</abp-carousel>
````

## Demonstração

Veja a [página carousel_demo](https://bootstrap-taghelpers.abp.io/Components/Carousel) para vê-lo em ação.

## Atributos

### id

Um valor que define o id do carousel. Se não definido, um id gerado será definido sempre que a tag for criada.

### controls

Um valor para habilitar os controles (botões anterior e próximo) no carousel. Deve ser um dos seguintes valores:

* `false`
* `true`

### indicators

Um valor para habilitar os indicadores no carousel. Deve ser um dos seguintes valores:

* `false`
* `true`

### crossfade

Um valor para habilitar a animação de fade em vez de slide no carousel. Deve ser um dos seguintes valores:

* `false`
* `true`

## Atributos abp-carousel-item

### caption-title

Um valor que define o título da legenda do item do carousel.

### caption

Um valor que define a legenda do item do carousel.

### src

Um valor de link que define a fonte da imagem exibida no item do carousel.

### active

Um valor para definir o item do carousel ativo. Deve ser um dos seguintes valores:

* `false`
* `true`

### alt

Um valor que define o texto alternativo para a imagem do item do carousel quando a imagem não pode ser exibida.
