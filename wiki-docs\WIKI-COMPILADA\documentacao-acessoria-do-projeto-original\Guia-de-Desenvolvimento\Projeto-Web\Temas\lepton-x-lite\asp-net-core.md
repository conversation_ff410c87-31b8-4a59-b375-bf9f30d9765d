# LeptonX Lite MVC UI

O LeptonX Lite possui implementação para o ABP Razor Pages. É uma variação simplificada do [Tema LeptonX](https://x.leptontheme.com/).

> Se você está procurando um tema profissional, pronto para o uso corporativo, você pode verificar o [Tema LeptonX](https://x.leptontheme.com/), que faz parte do [ABP](https://abp.io/).

> Consulte o documento [Theming document](../../framework/ui/mvc-razor-pages/theming.md) para aprender sobre temas.

## Instalação

Este tema já está **instalado** quando você cria uma nova solução usando os *startup templates*. Se você estiver usando qualquer outro *template*, você pode instalar este tema seguindo os passos abaixo:

- Adicione o pacote **Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite** para sua aplicação **Web**.

```bash
dotnet add package Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite --prerelease
```

- Remova a referência **Volo.Abp.AspNetCore.Mvc.UI.Theme.Basic** do projeto, já que não é necessário após trocar para o LeptonX Lite.

- Certifique-se de que o tema antigo foi removido e o LeptonX foi adicionado na sua classe Module.

```diff
[DependsOn(
     // Remove o módulo BasicTheme do atributo DependsOn
-    typeof(AbpAspNetCoreMvcUiBasicThemeModule),
     
     // Adiciona o módulo LeptonX Lite ao atributo DependsOn
+    typeof(AbpAspNetCoreMvcUiLeptonXLiteThemeModule),
)]
```

- Substitua `BasicThemeBundles` por `LeptonXLiteThemeBundles` em `AbpBundlingOptions`:

```diff
Configure<AbpBundlingOptions>(options =>
{
    options.StyleBundles.Configure(
        // Remove a linha seguinte
-       BasicThemeBundles.Styles.Global,
        // Adiciona a linha seguinte em vez disso
+       LeptonXLiteThemeBundles.Styles.Global,
        bundle =>
        {
            bundle.AddFiles("/global-styles.css");
        }
    );
});
```

## Customização

### Layouts

LeptonX Lite MVC fornece **layouts** para sua **interface de usuário** baseado em [ABP Theming](../../framework/ui/mvc-razor-pages/theming.md). Você pode usar **layouts** para **organizar sua interface de usuário**.

A principal responsabilidade de um tema é **fornecer** os *layouts*. Existem **três layouts pré-definidos que devem ser implementados por todos os temas:**

* **Application:** O *layout* **padrão** que é usado pelas páginas **principais** da aplicação.

* **Account:** Usado principalmente pelo **módulo de conta** para páginas de **login**, **registro**, **esqueci minha senha**...

* **Empty:** O *layout* **mínimo** que **não possui componentes de layout** algum.

**Nomes dos layouts** são **constantes** definidas na classe `LeptonXLiteTheme` na **raiz** do projeto **MVC**.

> As páginas de *layout* são definidas na pasta `Themes/LeptonXLite/Layouts` e você pode **sobrescrevê-las** criando um arquivo com o **mesmo nome** e **dentro** da **mesma pasta**.

### Toolbars

LeptonX Lite inclui *toolbars* separadas para desktop e mobile. Você pode gerenciar as *toolbars* independentemente. Os nomes das *toolbars* podem ser acessados na classe **LeptonXLiteToolbars**.

- `LeptonXLiteToolbars.Main`
- `LeptonXLiteToolbars.MainMobile`

```csharp
public class MyProjectNameMainToolbarContributor : IToolbarContributor
{
    public async Task ConfigureToolbarAsync(IToolbarConfigurationContext context)
    {
        if (context.Toolbar.Name == LeptonXLiteToolbars.Main)
        {
            context.Toolbar.Items.Add(new ToolbarItem(typeof(MyDesktopComponent)));
        }

        if (context.Toolbar.Name == LeptonXLiteToolbars.MainMobile)
        {
            context.Toolbar.Items.Add(new ToolbarItem(typeof(MyMobileComponent)));
        }
    }
}
```

# Componentes LeptonX Lite MVC

O ABP **ajuda** você a fazer uma **UI altamente customizável**. Você pode facilmente **customizar** seus temas para adequar às suas necessidades. O **Virtual File System** torna possível **gerenciar arquivos** que **não existem fisicamente** no **sistema de arquivos** (disco). É usado principalmente para incorporar arquivos **(js, css, image...)** dentro de *assemblies* e **usá-los como** arquivos físicos em tempo de execução. Uma aplicação (ou outro módulo) pode **sobrescrever** um **arquivo virtual de um módulo** assim como colocar um arquivo com o **mesmo nome** e **extensão** dentro da **mesma pasta** do **arquivo virtual**.

LeptonX Lite é construído sobre o [ABP](https://abp.io/), então você pode **facilmente** customizar sua interface de usuário Asp.Net Core MVC seguindo [Abp Mvc UI Customization](../../framework/ui/mvc-razor-pages/customization-user-interface.md).

## Componente Branding

O **componente branding** é um componente simples que pode ser usado para exibir sua marca. Ele contém um **logo** e um **nome de empresa**.

![Componente de marca](/ABP-Docs/images/leptonxlite-brand-component.png)

### Como sobrescrever o Componente Branding no LeptonX Lite MVC

* A **página do componente branding (arquivo .cshtml)** é definida no arquivo `Themes/LeptonXLite/Components/Brand/Default.cshtml` e você pode **sobrescrevê-la** criando um arquivo com o **mesmo nome** e **dentro** da **mesma pasta**.

* O **componente branding (arquivo C#)** é definido no arquivo `Themes/LeptonXLite/Components/Brand/MainNavbarBrandViewComponent.cs` e você pode **sobrescrevê-lo** criando um arquivo com o **mesmo nome** e dentro da **mesma pasta**.

### Como sobrescrever o favicon no LeptonX Lite MVC

Você pode adicionar um novo *favicon* nos caminhos `~/wwwroot/favicon.svg` e `~/wwwroot/favicon.ico` para sobrescrever o *favicon* atual.

## Componente Breadcrumb

Em websites que possuem muitas páginas, a **navegação *breadcrumb*** pode melhorar muito a forma como os usuários se encontram no site. Em termos de **usabilidade**, *breadcrumbs* reduzem o número de ações que um **visitante** do *website* precisa tomar para chegar em uma **página de nível superior**, e eles **melhoram** a **capacidade de encontrar** as **seções** e **páginas do website**.

![Componente Breadcrumb](/ABP-Docs/images/leptonxlite-breadcrumb-component.png)

### Como sobrescrever o Componente Breadcrumb no LeptonX Lite MVC

* A **página do componente *breadcrumb* (arquivo .cshtml)** é definida no arquivo `Themes/LeptonXLite/Components/Breadcrumbs/Default.cshtml` e você pode **sobrescrevê-la** criando um arquivo com o **mesmo nome** e **dentro** da **mesma pasta**.

* O **componente *breadcrumb* (arquivo C#)** é definido no arquivo `Themes/LeptonXLite/Components/Breadcrumbs/BreadcrumbsViewComponent.cs` e você pode **sobrescrevê-lo** criando um arquivo com o **mesmo nome** e **dentro** da **mesma pasta**.

## Componente Menu da Barra Lateral

Os menus da barra lateral são usados como **um diretório para Páginas Relacionadas** a uma oferta de **Serviço**, itens de **Navegação** para um **serviço específico** ou tópico e até mesmo apenas como **Links** nos quais o usuário pode estar interessado.

![Componente de menu da barra lateral](/ABP-Docs/images/leptonxlite-sidebar-menu-component.png)

### Como sobrescrever o Componente Menu da Barra Lateral no LeptonX Lite MVC

* A **página do menu da barra lateral (.cshtml)** é definida no arquivo `Themes/LeptonXLite/Components/Menu/Default.cshtml` e você pode **sobrescrevê-la** criando um arquivo com o **mesmo nome** e **dentro** da **mesma pasta**.

* Se você deseja **sobrescrever o componente de menu (C#)**, você pode sobrescrever o arquivo `Themes/LeptonXLite/Components/Menu/MainMenuViewComponent.cs` e você pode **sobrescrevê-lo** criando um arquivo com o **mesmo nome** e **dentro** da **mesma pasta**.

> O **menu da barra lateral** renderiza itens de menu **dinamicamente**. O **item de menu** é uma **partial view** e é definida no arquivo `Themes/LeptonXLite/Components/Menu/_MenuItem.cshtml` e você pode **sobrescrevê-la** criando um arquivo com o **mesmo nome** e **dentro** da **mesma pasta**.

## Componente Page Alerts

Fornece **mensagens de feedback** contextuais para ações típicas do usuário com o punhado de **mensagens de alerta** **disponíveis** e **flexíveis**. Os alertas estão disponíveis para qualquer tamanho de texto, bem como um **botão de dispensar opcional**.

![Componente Page Alerts](/ABP-Docs/images/leptonxlite-page-alerts-component.png)

### Como sobrescrever o Componente Page Alerts no LeptonX Lite MVC

* A **página do componente *page alerts* (arquivo .cshtml)** é definida no arquivo `Themes/LeptonXLite/Components/PageAlerts/Default.cshtml` e você pode **sobrescrevê-la** criando um arquivo com o **mesmo nome** e **dentro** da **mesma pasta**.

* O **componente *page alerts* (C#)** é definido no arquivo `Themes/LeptonXLite/Components/PageAlerts/PageAlertsViewComponent.cs` e você pode **sobrescrevê-lo** criando um arquivo com o **mesmo nome** e **dentro** da **mesma pasta**.

## Componente Toolbar

Os itens da *toolbar* são usados para adicionar **funcionalidade extra à *toolbar***. A *toolbar* é uma **barra horizontal** que **contém** um grupo de **itens da *toolbar***.

### Como sobrescrever o Componente Toolbar no LeptonX Lite MVC

* A **página do componente *toolbar* (arquivo .cshtml)** é definida no arquivo `Themes/LeptonXLite/Components/Toolbar/Default.cshtml` e você pode **sobrescrevê-la** criando um arquivo com o **mesmo nome** e **dentro** da **mesma pasta**.

* O **componente *toolbar* (C#)** é definido no arquivo `Themes/LeptonXLite/Components/Toolbar/ToolbarViewComponent.cs` e você pode **sobrescrevê-lo** criando um arquivo com o **mesmo nome** e **dentro** da **mesma pasta**.

## Componente Toolbar Item

O item da *toolbar* é um **item único** que **contém** um **link**, um **ícone**, um **rótulo**, etc.

### Como sobrescrever o Componente Toolbar Item no LeptonX Lite MVC

* A **página do componente *toolbar item* (arquivo .cshtml)** é definida no arquivo `Themes/LeptonXLite/Components/ToolbarItems/Default.cshtml` e você pode **sobrescrevê-la** criando um arquivo com o **mesmo nome** e **dentro** da **mesma pasta**.

* O **componente *toolbar item* (C#)** é definido no arquivo `Themes/LeptonXLite/Components/ToolbarItems/ToolbarItemsViewComponent.cs` e você pode **sobrescrevê-lo** criando um arquivo com o **mesmo nome** e **dentro** da **mesma pasta**.

Você pode encontrar os componentes de *toolbar* abaixo:

## Componente Language Switch

Pense em um *website* **multilíngue** e a primeira coisa que pode **vir à sua mente** é o **componente *language switch***. Uma **barra de navegação** é um **ótimo lugar** para **incorporar um *language switch***. Ao incorporar o *language switch* na barra de navegação do seu *website*, você **tornaria mais simples** para os usuários **encontrá-lo** e **facilmente** trocar o **idioma** <u>**sem tentar localizá-lo por todo o website.**</u>

![Componente Language Switch](/ABP-Docs/images/leptonxlite-language-switch-component.png)

### Como sobrescrever o Componente Language Switch no LeptonX Lite MVC

* A **página do componente *language switch* (arquivo .cshtml)** é definida no arquivo `Themes/LeptonXLite/Components/LanguageSwitch/Default.cshtml` e você pode **sobrescrevê-la** criando um arquivo com o **mesmo nome** e **dentro** da **mesma pasta**.

* O **componente *language switch* (C#)** é definido no arquivo `Themes/LeptonXLite/Components/LanguageSwitch/LanguageSwitchViewComponent.cs` e você pode **sobrescrevê-lo** criando um arquivo com o **mesmo nome** e **dentro** da **mesma pasta**.

## Componente Mobile Language Switch

O **componente *mobile language switch*** é usado para trocar o idioma do *website* **em dispositivos móveis**. O componente *mobile language switch* é um **menu dropdown** que **contém todos os idiomas** do *website*.

![Componente Mobile Language Switch](/ABP-Docs/images/leptonxlite-mobile-language-switch-component.png)

### Como sobrescrever o Componente Mobile Language Switch no LeptonX Lite MVC

* A **página do componente *mobile language switch* (arquivo .cshtml)** é definida no arquivo `Themes/LeptonXLite/Components/MobileLanguageSwitch/Default.cshtml` e você pode **sobrescrevê-la** criando um arquivo com o **mesmo nome** e **dentro** da **mesma pasta**.

* O **componente *mobile language switch* (C#)** é definido no arquivo `Themes/LeptonXLite/Components/MobileLanguageSwitch/MobileLanguageSwitchViewComponent.cs` e você pode **sobrescrevê-lo** criando um arquivo com o **mesmo nome** e **dentro** da **mesma pasta**.

## Componente User Menu

O **Menu do Usuário** é o **menu** que **aparece** quando você **clica no seu nome** ou **foto de perfil** no **canto superior direito** da sua página (**na *toolbar***). Ele aparece opções como **Configurações**, **Sair**, etc.

![Componente User Menu](/ABP-Docs/images/leptonxlite-user-menu-component.png)

### Como sobrescrever o Componente User Menu no LeptonX Lite MVC

* A **página do componente *user menu* (arquivo .cshtml)** é definida no arquivo `Themes/LeptonXLite/Components/UserMenu/Default.cshtml` e você pode **sobrescrevê-la** criando um arquivo com o **mesmo nome** e **dentro** da **mesma pasta**.

* O **componente *user menu* (C#)** é definido no arquivo `Themes/LeptonXLite/Components/UserMenu/UserMenuViewComponent.cs` e você pode **sobrescrevê-lo** criando um arquivo com o **mesmo nome** e **dentro** da **mesma pasta**.

## Componente Mobile User Menu

O **componente *mobile user menu*** é usado para exibir o **menu do usuário em dispositivos móveis**. O componente *mobile user menu* é um **menu dropdown** que contém todas as **opções** do **menu do usuário**.

![Componente Mobile User Menu](/ABP-Docs/images/leptonxlite-mobile-user-menu-component.png)

### Como sobrescrever o Componente Mobile User Menu no LeptonX Lite MVC

* A **página do componente *mobile user menu* (arquivo .cshtml)** é definida no arquivo `Themes/LeptonXLite/Components/MobileUserMenu/Default.cshtml` e você pode **sobrescrevê-la** criando um arquivo com o **mesmo nome** e **dentro** da **mesma pasta**.

* O **componente *mobile user menu* (C#)** é definido no arquivo `Themes/LeptonLite/Components/MobileUserMenu/MobileUserMenuViewComponent.cs` e você pode **sobrescrevê-lo** criando um arquivo com o **mesmo nome** e **dentro** da **mesma pasta**.
