# ASP.NET Core MVC / Razor Pages: O Tema Básico

O Tema Básico é uma implementação de tema para a interface de usuário ASP.NET Core MVC / Razor Pages. É um tema minimalista que não adiciona nenhum estilo em cima do [Bootstrap](https://getbootstrap.com/) puro. Você pode usar o Tema Básico como o **tema base** e construir seu próprio tema ou estilo sobre ele. Veja a seção *Customização*.

O Tema Básico possui suporte a RTL (idiomas da direita para a esquerda).

> Se você está procurando por um tema profissional, pronto para empresas, você pode verificar o [<PERSON><PERSON>](https://abp.io/themes), que faz parte do [ABP](https://abp.io/).

> Veja o [documento sobre Temas](theming.md) para aprender sobre temas.

## Instalação

Se você precisa instalar este tema manualmente, siga os passos abaixo:

* Instale o pacote NuGet [Volo.Abp.AspNetCore.Mvc.UI.Theme.Basic](https://www.nuget.org/packages/Volo.Abp.AspNetCore.Mvc.UI.Theme.Basic) no seu projeto web.
* Adicione `AbpAspNetCoreMvcUiBasicThemeModule` no atributo `[DependsOn(...)]` para sua [classe de módulo](../../architecture/modularity/basics.md) no projeto web.
* Instale o pacote NPM [@abp/aspnetcore.mvc.ui.theme.basic](https://www.npmjs.com/package/@abp/aspnetcore.mvc.ui.theme.basic) no seu projeto web (ex: `npm install @abp/aspnetcore.mvc.ui.theme.basic` ou `yarn add @abp/aspnetcore.mvc.ui.theme.basic`).
* Execute o comando `abp install-libs` em um terminal de linha de comando na pasta do projeto web.

## Layouts

O Tema Básico implementa os layouts padrão. Todos os layouts implementam as seguintes partes:

* [Bundles](bundling-minification.md) Globais
* [Alertas de Página](page-alerts.md)
* [Layout Hooks](layout-hooks.md)
* Recursos de [Widget](widgets.md)

### O Layout da Aplicação

![basic-theme-application-layout](/ABP-Docs/images/basic-theme-application-layout.png)

O Layout da Aplicação implementa as seguintes partes, além das partes comuns mencionadas acima;

* Branding
* [Menu](navigation-menu.md) Principal
* [Toolbar](toolbars.md) Principal com Seleção de Idioma e Menu do Usuário

### O Layout da Conta

![basic-theme-account-layout](/ABP-Docs/images/basic-theme-account-layout.png)

O Layout da Aplicação implementa as seguintes partes, além das partes comuns mencionadas acima;

* Branding
* [Menu](navigation-menu.md) Principal
* [Toolbar](toolbars.md) Principal com Seleção de Idioma e Menu do Usuário
* Área de Troca de Tenant

### Layout Vazio

O layout vazio é vazio, como o próprio nome indica. No entanto, ele implementa as partes comuns mencionadas acima.

## Customização

Você tem duas opções para customizar este tema:

### Sobrescrevendo Estilos/Componentes

Nesta abordagem, você continua usando o tema como pacotes NuGet e NPM e customiza as partes que você precisa. Existem várias maneiras de customizá-lo;

#### Sobrescrever os Estilos

1. Crie um arquivo CSS na pasta `wwwroot` do seu projeto:

![example-global-styles](/ABP-Docs/images/example-global-styles.png)

2. Adicione o arquivo de estilo ao bundle global, no método `ConfigureServices` do seu [módulo](../../architecture/modularity/basics.md):

````csharp
Configure<AbpBundlingOptions>(options =>
{
    options.StyleBundles.Configure(BasicThemeBundles.Styles.Global, bundle =>
    {
        bundle.AddFiles("/styles/global-styles.css");
    });
});
````

#### Sobrescrever os Componentes

Veja o [Guia de Customização da Interface de Usuário](customization-user-interface.md) para aprender como você pode substituir componentes, customizar e estender a interface de usuário.

### Copiar & Customizar

Você pode executar o seguinte comando [ABP CLI](../../../cli) no diretório do projeto **Web** para copiar o código fonte para sua solução:

`abp add-source-code Volo.Abp.BasicTheme`

----

Ou, você pode baixar o [código fonte](https://github.com/abpframework/abp/tree/dev/modules/basic-theme/src/Volo.Abp.AspNetCore.Mvc.UI.Theme.Basic) do Tema Básico, copiar manualmente o conteúdo do projeto para sua solução, reorganizar as dependências de pacote/módulo (veja a seção de Instalação acima para entender como foi instalado no projeto) e customizar livremente o tema com base nos requisitos da sua aplicação.

## Veja Também

* [Theming](theming.md)
