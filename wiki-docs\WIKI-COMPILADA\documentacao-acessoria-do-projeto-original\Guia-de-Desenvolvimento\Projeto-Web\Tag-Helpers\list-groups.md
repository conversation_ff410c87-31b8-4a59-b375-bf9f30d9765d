# List Groups

## Introdução

`abp-list-group` é o container principal para o conteúdo de um grupo de listas.

Uso básico:

````xml
<abp-list-group>
    <abp-list-group-item>Cras justo odio</abp-list-group-item>
    <abp-list-group-item>Dapibus ac facilisis in</abp-list-group-item>
    <abp-list-group-item>Morbi leo risus</abp-list-group-item>
    <abp-list-group-item>Vestibulum at eros</abp-list-group-item>
</abp-list-group>
````

## Demo

Veja a [página de demonstração de grupos de listas](https://bootstrap-taghelpers.abp.io/Components/ListGroup) para vê-lo em ação.

## Atributos

### flush

Um valor que indica se os itens `abp-list-group` devem remover algumas bordas e cantos arredondados para renderizar os itens do grupo de listas de ponta a ponta em um container pai. Deve ser um dos seguintes valores:

* `false` (valor padrão)
* `true`

### active

Um valor que indica se um `abp-list-group-item` deve estar ativo. Deve ser um dos seguintes valores:

* `false` (valor padrão)
* `true`

### disabled

Um valor que indica se um `abp-list-group-item` deve estar desabilitado. Deve ser um dos seguintes valores:

* `false` (valor padrão)
* `true`

### href

Um valor que indica se um `abp-list-group-item` possui um link. Deve ser um valor de link string.

### type

Um valor que indica uma classe de estilo `abp-list-group-item` com um background e cor com estado. Deve ser um dos seguintes valores:

* `Default` (valor padrão)
* `Primary`
* `Secondary`
* `Success`
* `Danger`
* `Warning`
* `Info`
* `Light`
* `Dark`
* `Link`

### Conteúdo Adicional

`abp-list-group-item` também pode conter elementos HTML adicionais como spans.

Exemplo:

````xml
<abp-list-group>
    <abp-list-group-item>Cras justo odio <span abp-badge-pill="Primary">14</span></abp-list-group-item>
    <abp-list-group-item>Dapibus ac facilisis in <span abp-badge-pill="Primary">2</span></abp-list-group-item>
    <abp-list-group-item>Morbi leo risus <span abp-badge-pill="Primary">1</span></abp-list-group-item>
</abp-list-group>
````
