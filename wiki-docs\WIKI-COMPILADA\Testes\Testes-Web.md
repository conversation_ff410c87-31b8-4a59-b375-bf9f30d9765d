[[_TOC_]]

# Testes de Interface Web

Este guia demonstra como implementar testes automatizados para a camada web do SISPREC, focando nos testes de Razor Pages.
Os testes de UI verificam o se as páginas são carregadas corretamente.
Atualmente não há infraestrutura para testar submissão de formulários.

## Estrutura de Testes

O projeto de testes web está organizado da seguinte forma:

```
test/
└── TRF3.SISPREC.Web.Tests/         # Testes da UI
    └── Pages/                      # Testes de Razor Pages
        ├── Autores/               # Testes relacionados a Autores
        └── Livros/                # Testes relacionados a Livros
```

## Testes da telas de Autor

Vamos analisar os testes implementados para as telas de Autor, que cobrem as principais funcionalidades da interface:

### Teste da Página de Índice

Este teste verifica se a página principal de listagem de autores carrega corretamente e contém os elementos essenciais, como a tabela de autores:

```csharp
using HtmlAgilityPack;
using Shouldly;

namespace TRF3.SISPREC.Pages.Tests.Autores;

/// <summary>
/// Classe de testes para a página de listagem de Autores.
/// Verifica o comportamento e estrutura da página de índice.
/// </summary>
public class Index_Tests : SISPRECWebTestBase
{
    /// <summary>
    /// Testa se a página de índice de Autores:
    /// 1. Retorna uma resposta válida
    /// 2. Contém os elementos esperados
    /// 3. Exibe corretamente a estrutura da tabela
    /// </summary>
    [Fact]
    public async Task Index_Page_Test()
    {
        // Arrange - Configura a URL da página
        string url = "/Autores";

        // Act - Obtém a resposta da requisição
        var response = await GetResponseAsync(url);
        var responseString = await GetResponseAsStringAsync(url);

        // Assert - Verificações principais
        response.ShouldNotBeNull();
        responseString.ShouldNotBeNull();
        response.StatusCode.ShouldBe(System.Net.HttpStatusCode.OK);

        // Analisa o HTML retornado
        var htmlDocument = new HtmlDocument();
        htmlDocument.LoadHtml(responseString);

        // Verifica a existência da tabela de autores
        var tableElement = htmlDocument.GetElementbyId("AutorTable");
        tableElement.ShouldNotBeNull();
    }
}
```

### Teste do Modal de Criação

Este teste verifica a estrutura e elementos do modal de criação de autores, garantindo que os botões e campos necessários estejam presentes:

```csharp
using HtmlAgilityPack;
using Shouldly;

namespace TRF3.SISPREC.Pages.Tests.Autores;

/// <summary>
/// Classe de testes para o modal de criação de Autores.
/// Verifica o comportamento e estrutura do modal de criação.
/// </summary>
public class CreateModalTests : SISPRECWebTestBase
{
    /// <summary>
    /// Testa se o modal de criação de Autores:
    /// 1. Retorna uma resposta válida
    /// 2. Contém o rodapé do modal
    /// 3. Possui os botões Cancelar e Salvar
    /// </summary>
    [Fact]
    public async Task Create_Modal_Teste()
    {
        // Arrange - Configura a URL do modal
        var url = "/Autores/CreateModal";

        // Act - Obtém a resposta da requisição
        var responseAsString = await GetResponseAsStringAsync(url);
        var response = await GetResponseAsync(url);

        // Assert - Verificações principais
        responseAsString.ShouldNotBeNull();
        var htmlDocument = new HtmlDocument();
        response.StatusCode.ShouldBe(System.Net.HttpStatusCode.OK);

        // Carrega o HTML para análise
        htmlDocument.LoadHtml(responseAsString);

        // Verifica se o rodapé do modal existe
        var footer = htmlDocument.DocumentNode.SelectSingleNode("//*[contains(@class, 'modal-footer')]");
        footer.ShouldNotBeNull();

        // Verifica os botões do modal
        var buttons = footer.SelectNodes(".//button");
        buttons.Count.ShouldBe(2);
        buttons[0].InnerText.Trim().ShouldBe("Cancelar");
        buttons[1].InnerText.Trim().ShouldBe("Salvar");
    }
}
```

### Teste do Modal de Edição

Este teste é mais complexo pois verifica não só a estrutura do modal, mas também se os dados do autor são carregados corretamente nos campos:

```csharp
using HtmlAgilityPack;
using Shouldly;
using TRF3.SISPREC.Autores;

namespace TRF3.SISPREC.Pages.Tests.Autores;

/// <summary>
/// Classe de testes para o modal de edição de Autores.
/// Verifica o comportamento e estrutura do modal de edição.
/// </summary>
public class EditModalTests : SISPRECWebTestBase
{
    private readonly IAutorRepository _autorRepository;
    private readonly Autor autorObj;

    /// <summary>
    /// Construtor que inicializa os dados de teste:
    /// 1. Configura o repositório mockado
    /// 2. Cria um autor faker para testes
    /// 3. Insere o autor no repositório
    /// </summary>
    public EditModalTests()
    {
        _autorRepository = GetRequiredService<IAutorRepository>();

        // Configurando o faker para autores
        autorObj = new Bogus.Faker<Autor>()
            .RuleFor(a => a.Nome, a => a.Random.Hash())
            .RuleFor(a => a.Sobrenome, a => a.Random.Hash())
            .RuleFor(a => a.GeneroBiologico, a => a.PickRandom<EGeneroBiologico>())
            .RuleFor(a => a.MunicipioId, SISPRECTestConsts.MunicipioId)
            .Generate();

        // Inserindo o autor
        _autorRepository.InsertAsync(autorObj, true).Wait();
    }

    /// <summary>
    /// Testa se o modal de edição de Autores:
    /// 1. Retorna uma resposta válida
    /// 2. Contém os elementos esperados
    /// 3. Exibe corretamente os dados do autor
    /// 4. Possui os botões de ação corretos
    /// </summary>
    [Fact]
    public async Task Edit_Modal_Teste()
    {
        // Arrange - Configura a URL do modal com o ID do autor
        var url = "/Autores/EditModal?id=" + autorObj.AutorId;

        // Act - Obtém a resposta da requisição
        var responseAsString = await GetResponseAsStringAsync(url);
        var response = await GetResponseAsync(url);

        // Assert - Verificações principais
        responseAsString.ShouldNotBeNull();
        response.StatusCode.ShouldBe(System.Net.HttpStatusCode.OK);

        // Analisa o HTML retornado
        var htmlDocument = new HtmlDocument();
        htmlDocument.LoadHtml(responseAsString);

        // Verifica o campo ID oculto
        var hiddenIdInput = htmlDocument.GetElementbyId("Id");
        hiddenIdInput.ShouldNotBeNull();
        hiddenIdInput.Attributes["value"].Value.ShouldBe(autorObj.AutorId.ToString());

        // Verifica os campos preenchidos
        var nomeInput = htmlDocument.GetElementbyId("ViewModel_Nome");
        nomeInput.ShouldNotBeNull();
        nomeInput.Attributes["value"].Value.ShouldBe(autorObj.Nome);

        var sobrenomeInput = htmlDocument.GetElementbyId("ViewModel_Sobrenome");
        sobrenomeInput.ShouldNotBeNull();
        sobrenomeInput.Attributes["value"].Value.ShouldBe(autorObj.Sobrenome);

        // Verifica o rodapé do modal
        var footer = htmlDocument.DocumentNode.SelectSingleNode("//*[contains(@class, 'modal-footer')]");
        footer.ShouldNotBeNull();

        // Verifica os botões de ação
        var buttons = footer.SelectNodes(".//button");
        buttons.Count.ShouldBe(2);
        buttons[0].InnerText.Trim().ShouldBe("Cancelar");
        buttons[1].InnerText.Trim().ShouldBe("Salvar");
    }
}
```

### Teste do Modal de Detalhes

Este teste verifica se o modal de detalhes carrega e exibe corretamente as informações do autor:

```csharp
using HtmlAgilityPack;
using Shouldly;
using TRF3.SISPREC.Autores;

namespace TRF3.SISPREC.Pages.Tests.Autores;

/// <summary>
/// Classe de testes para o modal de detalhes de Autores.
/// Verifica o comportamento e estrutura do modal de detalhes.
/// </summary>
public class DetalheModalTests : SISPRECWebTestBase
{
    private readonly IAutorRepository _repository;
    private readonly Autor autorObj;

    /// <summary>
    /// Construtor que inicializa os dados de teste:
    /// 1. Configura o repositório mockado
    /// 2. Cria um autor faker para testes
    /// 3. Insere o autor no repositório
    /// </summary>
    public DetalheModalTests()
    {
        _repository = GetRequiredService<IAutorRepository>();

        // Configurando o faker para autores
        autorObj = new Bogus.Faker<Autor>()
            .RuleFor(a => a.Nome, a => a.Random.Hash(10))
            .RuleFor(a => a.Sobrenome, a => a.Random.Hash(10))
            .RuleFor(a => a.GeneroBiologico, a => a.PickRandom<EGeneroBiologico>())
            .RuleFor(a => a.MunicipioId, SISPRECTestConsts.MunicipioId)
            .Generate();

        // Inserindo o autor
        _repository.InsertAsync(autorObj, true).Wait();
    }

    /// <summary>
    /// Testa se o modal de detalhes de Autores:
    /// 1. Retorna uma resposta válida
    /// 2. Contém os elementos esperados
    /// 3. Exibe corretamente os dados do autor
    /// </summary>
    [Fact]
    public async Task Detalhe_Modal_Teste()
    {
        // Arrange - Configura a URL do modal com o ID do autor
        string url = "/Autores/DetalheModal?id=" + autorObj.AutorId;

        // Act - Obtém a resposta da requisição
        var response = await GetResponseAsync(url);
        var responseString = await GetResponseAsStringAsync(url);

        // Assert - Verificações principais
        response.ShouldNotBeNull();
        responseString.ShouldNotBeNull();
        response.StatusCode.ShouldBe(System.Net.HttpStatusCode.OK);

        // Analisa o HTML retornado
        var htmlDocument = new HtmlDocument();
        htmlDocument.LoadHtml(responseString);

        // Verifica se os elementos principais existem
        var nomeElement = htmlDocument.DocumentNode.SelectSingleNode("//input[@id='ViewModel_Nome']");
        nomeElement.ShouldNotBeNull();
        nomeElement.Attributes["value"].Value.ShouldContain(autorObj.Nome);

        var sobrenomeElement = htmlDocument.DocumentNode.SelectSingleNode("//input[@id='ViewModel_Sobrenome']");
        sobrenomeElement.ShouldNotBeNull();
        sobrenomeElement.Attributes["value"].Value.ShouldContain(autorObj.Sobrenome);
    }
}

```

## Testes das telas de Livro

### Teste da Página de Índice

```csharp
using Shouldly;

namespace TRF3.SISPREC.Pages.Tests.Livros;

/// <summary>
/// Classe de testes para a página principal de listagem de livros.
/// Demonstra como testar páginas Razor que não dependem de dados específicos.
/// Namespace: TRF3.SISPREC.Pages.Tests.Livros
/// Diretório: .\test\TRF3.SISPREC.Web.Tests\Pages\Livros
/// </summary>
/// <remarks>
/// Esta classe herda de SISPRECWebTestBase que fornece:
/// - Ambiente web simulado para testes
/// - Métodos auxiliares para requisições HTTP
/// - Configuração do ABP Framework para testes
/// 
/// Características dos testes:
/// - Verifica o carregamento básico da página
/// - Valida o status HTTP da resposta
/// - Confirma a presença do conteúdo HTML
/// 
/// Obs: Testes mais específicos como validação do DataTable
/// são feitos via testes de JavaScript, pois a tabela é
/// carregada dinamicamente pelo cliente.
/// </remarks>
public class Index_Tests : SISPRECWebTestBase
{
    /// <summary>
    /// Testa se a página principal de livros carrega corretamente.
    /// Verifica:
    /// 1. Se a requisição retorna status 200 (OK)
    /// 2. Se o HTML da página está presente
    /// 
    /// Este é um teste básico de smoke test que garante
    /// que a rota está configurada e a página está acessível.
    /// </summary>
    [Fact]
    public async Task Index_Page_Test()
    {
        // Arrange
        // Define a URL da página principal de livros
        string url = "/Livros";

        // Act
        // Faz duas requisições: uma para o response e outra para o conteúdo
        var response = await GetResponseAsync(url);
        var responseString = await GetResponseAsStringAsync(url);

        //Assert
        // Verifica se as respostas não são nulas
        response.ShouldNotBeNull();
        responseString.ShouldNotBeNull();
        // Verifica se o status da resposta é 200 OK
        response.StatusCode.ShouldBe(System.Net.HttpStatusCode.OK);
    }
}

```

### Teste do Modal de Criação

Este teste verifica a estrutura e elementos do modal de criação de autores, garantindo que os botões e campos necessários estejam presentes:

```csharp
using HtmlAgilityPack;
using Shouldly;

namespace TRF3.SISPREC.Pages.Tests.Livros;

/// <summary>
/// Classe de testes para a modal de criação de livros.
/// Demonstra como testar páginas Razor e elementos HTML usando HtmlAgilityPack.
/// Namespace: TRF3.SISPREC.Pages.Tests.Livros
/// Diretório: .\test\TRF3.SISPREC.Web.Tests\Pages\Livros
/// </summary>
/// <remarks>
/// Esta classe herda de SISPRECWebTestBase que fornece:
/// - Ambiente web simulado para testes
/// - Métodos auxiliares para requisições HTTP
/// - Configuração do ABP Framework para testes
/// 
/// Características dos testes:
/// - Verificam a estrutura HTML da modal
/// - Validam elementos específicos como botões
/// - Confirmam o status HTTP das respostas
/// - Usam HtmlAgilityPack para parsing do HTML
/// </remarks>
public class CreateModalTests : SISPRECWebTestBase
{
    /// <summary>
    /// Testa se a modal de criação é carregada corretamente.
    /// Verifica:
    /// 1. Se a requisição retorna status 200 (OK)
    /// 2. Se o HTML da modal está presente
    /// 3. Se os botões de ação existem com os textos corretos
    /// </summary>
    [Fact]
    public async Task Create_Modal_Teste()
    {
        // Arrange
        // Define a URL da modal de criação
        var url = "/Livros/CreateModal";

        // Act
        // Faz duas requisições: uma para o conteúdo em string e outra para o response completo
        var responseAsString = await GetResponseAsStringAsync(url);
        var response = await GetResponseAsync(url);

        // Assert
        // Verifica se a resposta não é nula
        responseAsString.ShouldNotBeNull();
        var htmlDocument = new HtmlDocument();
        // Verifica se o status da resposta é 200 OK
        response.StatusCode.ShouldBe(System.Net.HttpStatusCode.OK);

        // Carrega o HTML para análise
        htmlDocument.LoadHtml(responseAsString);

        // Verifica existência do rodapé do modal usando XPath
        var footer = htmlDocument.DocumentNode.SelectSingleNode("//*[contains(@class, 'modal-footer')]");
        footer.ShouldNotBeNull();

        // Verifica se existem exatamente 2 botões (Cancelar e Salvar)
        var buttons = footer.SelectNodes(".//button");
        buttons.Count.ShouldBe(2);
        // Verifica os textos dos botões
        buttons[0].InnerText.Trim().ShouldBe("Cancelar");
        buttons[1].InnerText.Trim().ShouldBe("Salvar");
    }
}

```

### Teste do Modal de Edição


```csharp
using HtmlAgilityPack;
using Shouldly;
using TRF3.SISPREC.Autores;
using TRF3.SISPREC.Livros;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.Pages.Tests.Livros;

/// <summary>
/// Classe de testes para a modal de edição de livros.
/// Demonstra como testar páginas Razor que dependem de dados do banco
/// e validar o preenchimento correto dos campos do formulário.
/// Namespace: TRF3.SISPREC.Pages.Tests.Livros
/// Diretório: .\test\TRF3.SISPREC.Web.Tests\Pages\Livros
/// </summary>
/// <remarks>
/// Esta classe herda de SISPRECWebTestBase que fornece:
/// - Ambiente web simulado para testes
/// - Métodos auxiliares para requisições HTTP
/// - Configuração do ABP Framework para testes
/// - Acesso aos serviços via injeção de dependência
/// 
/// Características dos testes:
/// - Usa Bogus para gerar dados de teste
/// - Trabalha com Unit of Work para persistência
/// - Cria dados relacionados (Autor e Livro)
/// - Verifica respostas HTTP e elementos HTML específicos
/// </remarks>
public class EditModalTests : SISPRECWebTestBase
{
    /// <summary>
    /// Livro de teste criado no construtor e usado nos métodos de teste
    /// </summary>
    private readonly Livro livroObj;

    /// <summary>
    /// Construtor que prepara o ambiente de teste:
    /// 1. Obtém os gerenciadores necessários via injeção de dependência
    /// 2. Configura os fakers para gerar dados consistentes
    /// 3. Cria um autor e um livro no banco de teste
    /// </summary>
    public EditModalTests()
    {
        var livroManager = GetRequiredService<ILivroManager>();
        var autorManager = GetRequiredService<IAutorManager>();

        // Configurando o faker para autores com dados válidos
        var autorFaker = new Bogus.Faker<Autor>()
                    .RuleFor(a => a.Nome, a => a.Random.Hash(10))
                    .RuleFor(a => a.Sobrenome, a => a.Random.Hash(10))
                    .RuleFor(a => a.MunicipioId, SISPRECTestConsts.MunicipioId);

        // Configurando o faker para livros com dados válidos
        livroObj = new Bogus.Faker<Livro>()
            .RuleFor(p => p.Titulo, p => p.Random.Hash())
            .RuleFor(p => p.Categoria, p => ECategoriaLivro.INFANTIL)
            .RuleFor(p => p.DataPublicacao, p => p.Date.Past())
            .RuleFor(p => p.Preco, p => p.Random.Decimal(1, 999.99M))
            .RuleFor(p => p.Descricao, p => p.Random.Hash())
            .RuleFor(p => p.Quantidade, p => p.Random.Int(1, 100))
            .RuleFor(p => p.Disponivel, p => true)
            .Generate();

        /**
         * Geralmente não é necessário usar UnitOfWork, porém como são necessários dois inserts, 
         * é necessário garantir a instância do DbContext esteja disponível para ambos.
         */
        using (var uow = GetRequiredService<IUnitOfWorkManager>().Begin())
        {
            // Inserindo o autor e obtendo seu ID
            var autorId = autorManager.InserirAsync(autorFaker.Generate(), true).Result.AutorId;
            // Inserindo o livro e vinculando ao autor
            livroManager.InserirAsync(livroObj, new[] { autorId }, true).Wait();
            uow.CompleteAsync().Wait();
        }
    }

    /// <summary>
    /// Testa se a modal de edição carrega corretamente com os dados do livro.
    /// Verifica:
    /// 1. Se a requisição retorna status 200 (OK)
    /// 2. Se o ID do livro está no campo hidden
    /// 3. Se os campos estão preenchidos com os valores corretos
    /// 4. Se os botões de ação existem com os textos corretos
    /// </summary>
    [Fact]
    public async Task Edit_Modal_Teste()
    {
        // Arrange
        // Monta a URL com o ID do livro criado no construtor
        var url = "/Livros/EditModal?id=" + livroObj.LivroId;

        // Act
        // Faz duas requisições: uma para o conteúdo em string e outra para o response completo
        var responseAsString = await GetResponseAsStringAsync(url);
        var response = await GetResponseAsync(url);

        // Assert
        // Verifica se a resposta não é nula
        responseAsString.ShouldNotBeNull();
        var htmlDocument = new HtmlDocument();
        // Verifica se o status da resposta é 200 OK
        response.StatusCode.ShouldBe(System.Net.HttpStatusCode.OK);

        htmlDocument.LoadHtml(responseAsString);

        // Verifica se o ID do livro está no campo hidden
        var hiddenIdInput = htmlDocument.GetElementbyId("Id");
        hiddenIdInput.ShouldNotBeNull();

        // Verifica se o título está preenchido corretamente
        var TituloInput = htmlDocument.GetElementbyId("ViewModel_Titulo");
        TituloInput.ShouldNotBeNull();
        TituloInput.Attributes["value"].Value.ShouldBe(livroObj.Titulo);

        // Verifica existência do rodapé do modal
        var footer = htmlDocument.DocumentNode.SelectSingleNode("//*[contains(@class, 'modal-footer')]");
        footer.ShouldNotBeNull();

        // Verifica se existem exatamente 2 botões (Cancelar e Salvar)
        var buttons = footer.SelectNodes(".//button");
        buttons.Count.ShouldBe(2);
        buttons[0].InnerText.Trim().ShouldBe("Cancelar");
        buttons[1].InnerText.Trim().ShouldBe("Salvar");
    }
}

```

### Teste do Modal de Detalhes

```csharp
using HtmlAgilityPack;
using Shouldly;
using TRF3.SISPREC.Autores;
using TRF3.SISPREC.Livros;
using Volo.Abp.Uow;

namespace TRF3.SISPREC.Pages.Tests.Livros;

/// <summary>
/// Classe de testes para a modal de detalhes de livros.
/// Demonstra como testar páginas Razor que dependem de dados do banco.
/// Namespace: TRF3.SISPREC.Pages.Tests.Livros
/// Diretório: .\test\TRF3.SISPREC.Web.Tests\Pages\Livros
/// </summary>
/// <remarks>
/// Esta classe herda de SISPRECWebTestBase que fornece:
/// - Ambiente web simulado para testes
/// - Métodos auxiliares para requisições HTTP
/// - Configuração do ABP Framework para testes
/// - Acesso aos serviços via injeção de dependência
/// 
/// Características dos testes:
/// - Usa Bogus para gerar dados de teste
/// - Trabalha com Unit of Work para persistência
/// - Cria dados relacionados (Autor e Livro)
/// - Verifica respostas HTTP e conteúdo HTML
/// </remarks>
public class DetalheModalTests : SISPRECWebTestBase
{
    /// <summary>
    /// Livro de teste criado no construtor e usado nos métodos de teste
    /// </summary>
    private readonly Livro livroObj;

    /// <summary>
    /// Construtor que prepara o ambiente de teste:
    /// 1. Obtém os gerenciadores necessários via injeção de dependência
    /// 2. Configura os fakers para gerar dados consistentes
    /// 3. Cria um autor e um livro no banco de teste
    /// </summary>
    public DetalheModalTests()
    {
        var livroManager = GetRequiredService<ILivroManager>();
        var autorManager = GetRequiredService<IAutorManager>();

        // Configurando o faker para autores com dados válidos
        var autorFaker = new Bogus.Faker<Autor>()
                    .RuleFor(a => a.Nome, a => a.Random.Hash(10))
                    .RuleFor(a => a.Sobrenome, a => a.Random.Hash(10))
                    .RuleFor(a => a.MunicipioId, SISPRECTestConsts.MunicipioId);

        // Configurando o faker para livros com dados válidos
        livroObj = new Bogus.Faker<Livro>()
            .RuleFor(p => p.Titulo, p => p.Random.Hash())
            .RuleFor(p => p.Categoria, p => ECategoriaLivro.INFANTIL)
            .RuleFor(p => p.DataPublicacao, p => p.Date.Past())
            .RuleFor(p => p.Preco, p => p.Random.Decimal(1, 999.99M))
            .RuleFor(p => p.Descricao, p => p.Random.Hash())
            .RuleFor(p => p.Quantidade, p => p.Random.Int(1, 100))
            .RuleFor(p => p.Disponivel, p => true)
            .Generate();

        // Cria os registros dentro de uma transação
        using (var uow = GetRequiredService<IUnitOfWorkManager>().Begin())
        {
            // Inserindo o autor e obtendo seu ID
            var autorId = autorManager.InserirAsync(autorFaker.Generate(), true).Result.AutorId;
            // Inserindo o livro e vinculando ao autor
            livroManager.InserirAsync(livroObj, new[] { autorId }, true).Wait();
            uow.CompleteAsync().Wait();
        }
    }

    /// <summary>
    /// Testa se a modal de detalhes carrega corretamente os dados do livro.
    /// Verifica:
    /// 1. Se a requisição retorna status 200 (OK)
    /// 2. Se o HTML da modal está presente
    /// 3. Se os dados do livro são exibidos corretamente
    /// </summary>
    [Fact]
    public async Task Detalhe_Modal_Teste()
    {
        // Arrange
        // Monta a URL com o ID do livro criado no construtor
        string url = "/Livros/DetalheModal?id=" + livroObj.LivroId;

        // Act
        // Faz duas requisições: uma para o response e outra para o conteúdo
        var response = await GetResponseAsync(url);
        var responseString = await GetResponseAsStringAsync(url);

        //Assert
        // Verifica se as respostas não são nulas
        response.ShouldNotBeNull();
        responseString.ShouldNotBeNull();

        // Verifica se o status da resposta é 200 OK
        response.StatusCode.ShouldBe(System.Net.HttpStatusCode.OK);

        var htmlDocument = new HtmlDocument();
        htmlDocument.LoadHtml(responseString);

        // Verifica se os elementos principais existem
        var tituloElement = htmlDocument.DocumentNode.SelectSingleNode("//input[@id='ViewModel_Titulo']");
        tituloElement.ShouldNotBeNull();
        tituloElement.Attributes["value"].Value.ShouldBeEquivalentTo(livroObj.Titulo);
    }
}

```

## Boas Práticas para Testes Web

1. **Preparação de Dados**
   - Sempre use `Wait()` e `autoSave: true` ao inserir registros no construtor para garantir que:
     - os dados estejam disponíveis antes dos testes
     - se acontecer erro na inserção, seja exibido no resultado do teste
   - Exemplo:
     ```csharp
     _repository.InsertAsync(autorObj, autoSave: true).Wait(); // Garante sincronização
     ```

2. **Validações Essenciais**
   - Verifique pelo menos um campo preenchido para garantir que os dados foram carregados
   - Exemplo de validação mínima:
     ```csharp
        var nomeInput = htmlDocument.DocumentNode.SelectSingleNode("//input[@id='ViewModel_Nome']");
        nomeInput.ShouldNotBeNull();
        nomeInput.Attributes["value"].Value.ShouldContain(autorObj.Nome);
     ```
   - **Importante**: use SelectSingleNode buscando pelo input com o ID específico, como no exemplo acima.

3. **Rastreabilidade de Dados**
   - Use o mesmo objeto inserido no construtor para fazer as consultas
   - Mantenha referência ao objeto de teste como campo privado da classe

4. **Manutenibilidade**
   - Organize testes por funcionalidade
   - Use constantes para dados comuns
   - Documente cenários complexos

**[Voltar: Tutorial-de-Início](/Tutorial-de-Início.md)**