# ABP CLI: Comando install-libs

O comando `abp install-libs` é uma ferramenta essencial do ABP CLI que gerencia as bibliotecas de cliente (client-side libraries) em projetos ABP Framework.

## O que é?

O `abp install-libs` é um comando que:
- Instala as bibliotecas de cliente necessárias para o funcionamento do projeto
- Copia os arquivos das bibliotecas do diretório node_modules para a pasta wwwroot/libs
- Gerencia as dependências de front-end do projeto

## Quando Usar?

Você deve executar o comando `abp install-libs`:

1. Após instalar um novo pacote NPM que contenha bibliotecas de cliente
2. Depois de atualizar pacotes NPM existentes
3. Quando precisar restaurar as bibliotecas de cliente do projeto
4. Após clonar um projeto existente

## Como Usar

```bash
abp install-libs
```

O comando pode ser executado em dois locais:

1. **Na raiz da solution** (recomendado)
   - O comando identificará automaticamente o projeto web
   - Não é necessário especificar o caminho do projeto
   - Exemplo: `abp install-libs`

2. **No diretório do projeto web**
   - Onde está localizado o arquivo .csproj
   - Exemplo: `abp install-libs`

### Exemplos de Uso

1. Instalação na raiz da solution:
```bash
cd C:\meuprojeto
abp install-libs
```

2. Especificando o diretório do projeto (opcional):
```bash
abp install-libs --project-path "C:\meuprojeto\src\MeuProjeto.Web"
```

## O que o Comando Faz?

1. **Verifica Dependências**
   - Analisa o package.json do projeto
   - Identifica as bibliotecas necessárias

2. **Copia Arquivos**
   - Move os arquivos necessários de node_modules para wwwroot/libs
   - Mantém apenas os arquivos essenciais para produção

3. **Organiza Estrutura**
   - Cria uma estrutura organizada na pasta wwwroot/libs
   - Mantém apenas as versões minificadas quando disponíveis

## Bibliotecas Gerenciadas

O comando gerencia bibliotecas comumente usadas em projetos ABP, como:
- Bootstrap
- jQuery
- FontAwesome
- Select2
- DataTables
- Toastr
- SweetAlert
- E outras bibliotecas específicas do ABP

## Boas Práticas

1. **Execução Regular**
   - Execute após qualquer alteração em pacotes NPM
   - Inclua no processo de build/deploy

2. **Verificação**
   - Confirme se wwwroot/libs foi atualizada
   - Verifique se todas as bibliotecas necessárias estão presentes

3. **Versionamento**
   - Inclua wwwroot/libs no controle de versão
   - Mantenha package.json atualizado

## Solução de Problemas

Se encontrar problemas:

1. Verifique se está na raiz da solution ou no diretório correto do projeto web
2. Confirme se o package.json está presente e válido
3. Limpe a pasta wwwroot/libs e execute novamente
4. Verifique se o npm install foi executado corretamente

## Considerações de Segurança

- Mantenha o NPM e o ABP CLI atualizados
- Verifique as dependências por vulnerabilidades
- Use apenas pacotes confiáveis

## Integração com CI/CD

Em pipelines de CI/CD:

```yaml
steps:
  - script: |
      abp install-libs
    displayName: 'Install Client-Side Libraries'
```

## Veja Também

- [ABP CLI](../../CLI.md)
- [Bundling & Minification](/Guia-de-Desenvolvimento/Projeto-Web/Bundling-Minification.md)
- [Client-Side Package Management](/Guia-de-Desenvolvimento/Projeto-Web/Client-Side-Package-Management.md)
