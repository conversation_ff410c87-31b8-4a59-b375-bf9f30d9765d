# Guia de Views SQL nos Testes de Integração

Este guia explica como as views SQL são tratadas nos testes de integração do SISPREC, especialmente focando na compatibilidade entre SQL Server e SQLite.

## Visão Geral do Módulo de Testes

O módulo `SISPRECEntityFrameworkCoreTestModule` é responsável por configurar o ambiente de testes de integração, utilizando SQLite em memória como banco de dados. Este módulo:

- Configura um banco SQLite em memória para testes
- Cria as tabelas necessárias
- Executa a carga inicial de dados
- Cria as views SQL necessárias

## Ciclo de Vida do Banco de Dados em Memória

O módulo gerencia cuidadosamente o ciclo de vida do banco de dados em memória:

```csharp
private SqliteConnection? _sqliteConnection;

private static SqliteConnection CreateDatabaseAndGetConnection()
{
    var connection = new SqliteConnection("Data Source=:memory:");
    connection.Open();

    var options = new DbContextOptionsBuilder<SISPRECDbContext>()
        .UseSqlite(connection)
        .Options;

    using (var context = new SISPRECDbContext(options))
    {
        context.GetService<IRelationalDatabaseCreator>().CreateTables();
    }

    return connection;
}

public override void OnApplicationShutdown(ApplicationShutdownContext context)
{
    _sqliteConnection?.Dispose();
}
```

Este código garante que:
- A conexão permanece aberta durante todo o ciclo de testes
- As tabelas são criadas corretamente
- Os recursos são liberados ao finalizar

## Criação de Views no SQLite

As views são criadas através do método `CriarViewsFormatoSqlite`, que é chamado durante a inicialização da aplicação de testes. Veja um exemplo de implementação:

```csharp
private static void CriarViewsFormatoSqlite(IServiceProvider serviceProvider)
{
    using (var context = serviceProvider.GetRequiredService<SISPRECDbContext>())
    {
        if (context.Database.IsSqlite())
        {
            var resourceName = "TRF3.SISPREC.EntityFrameworkCore.Sql.VIEWS_FORMATO_SQLITE.sql";
            var assembly = typeof(SISPRECEntityFrameworkCoreTestModule).Assembly;
            using (var stream = assembly.GetManifestResourceStream(resourceName))
            {
                if (stream == null)
                {
                    throw new InvalidOperationException($"Resource {resourceName} not found.");
                }
                using (var reader = new StreamReader(stream))
                {
                    var sql = reader.ReadToEnd();
                    context.Database.ExecuteSqlRaw(sql);
                }
            }
        }
    }
}
```

## Recursos SQL Embutidos

O sistema utiliza recursos SQL embutidos (embedded resources) para armazenar os scripts SQL. Estes recursos são:

1. `VIEWS_FORMATO_SQLITE.sql`: Contém as definições das views adaptadas para SQLite
2. `CARGA_BASICA_SQLITE.sql`: Contém os scripts de carga inicial de dados

Para acessar estes recursos, eles devem estar configurados como "Embedded Resource" no projeto de testes.

## Execução de SQL Raw

Para executar os scripts SQL, o sistema utiliza o método `ExecuteSqlRaw` do Entity Framework Core. Este método permite:

- Executar comandos SQL diretos no banco de dados
- Criar views e outros objetos de banco
- Executar múltiplos comandos em sequência

Exemplo de uso:

```csharp
context.Database.ExecuteSqlRaw(sql);
```

## Integração com Entity Framework Core

O módulo de testes integra-se perfeitamente com o Entity Framework Core através de:

1. **Configuração do Contexto**
```csharp
services.Configure<AbpDbContextOptions>(options =>
{
    options.Configure(context =>
    {
        context.DbContextOptions.UseSqlite(_sqliteConnection);
    });
});
```

2. **Desativação de Transações**
```csharp
context.Services.AddAlwaysDisableUnitOfWorkTransaction();
```

3. **Seed de Dados**
```csharp
private static void SeedTestData(ApplicationInitializationContext context)
{
    AsyncHelper.RunSync(async () =>
    {
        using (var scope = context.ServiceProvider.CreateScope())
        {
            await dataSeeder.SeedAsync();
        }
    });

    CriarViewsFormatoSqlite(context.ServiceProvider);
    InserirCargaBasicaSqlite(context.ServiceProvider);
}
```

## Dicas e Boas Práticas

1. **Compatibilidade SQLite/SQL Server**
   - Mantenha os scripts de views compatíveis com ambos os bancos
   - Use funções equivalentes quando necessário
   - Evite recursos específicos de um SGBD

2. **Organização dos Scripts**
   - Mantenha os scripts SQL em arquivos separados
   - Use comentários para documentar partes complexas
   - Divida scripts longos em seções lógicas

3. **Tratamento de Erros**
   - Sempre verifique se o recurso existe antes de tentar carregá-lo
   - Use try-catch para tratar erros de execução SQL
   - Forneça mensagens de erro claras

## Exemplo Prático

Aqui está um exemplo de como uma view seria definida no arquivo `VIEWS_FORMATO_SQLITE.sql`:

```sql
CREATE VIEW vw_RequisicaoCompleta AS
SELECT 
    r.Id,
    r.Numero,
    r.DataAjuizamento,
    p.Nome as NomeBeneficiario,
    v.Valor as ValorTotal
FROM Requisicoes r
JOIN Pessoas p ON r.BeneficiarioId = p.Id
JOIN Valores v ON r.Id = v.RequisicaoId;
```

Este exemplo mostra uma view que:
- Une informações de requisições, pessoas e valores
- É compatível tanto com SQLite quanto SQL Server
- Usa apenas funções comuns a ambos os SGBDs
