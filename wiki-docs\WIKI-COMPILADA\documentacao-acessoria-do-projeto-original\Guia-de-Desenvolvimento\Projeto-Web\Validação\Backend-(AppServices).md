# Validações em Application Services

## Tipos de Validação

### 1. Data Annotations

São atributos que decoram as propriedades dos DTOs para definir regras de validação básicas. O framework executa estas validações automaticamente antes de chamar os métodos do Application Service.

Exemplo prático (baseado em `CreateUpdateLivroDto`):

```csharp
public class CreateUpdateLivroDto
{
    [Required(ErrorMessage = "O título do livro é obrigatório")]
    [StringLength(LivroConsts.TITULO_TAMANHO_MAX,
        ErrorMessage = "O título não pode exceder {1} caracteres")]
    [Display(Name = "Titulo")]
    public string Titulo { get; set; }

    [Required(ErrorMessage = "O preço é obrigatório")]
    [Range(0, 9999999.99, 
        ErrorMessage = "O preço deve estar entre {1} e {2}")]
    [DataType(DataType.Currency)]
    [Display(Name = "Preço")]
    public decimal Preco { get; set; }
}
```

#### Principais Data Annotations:

- `[Required]`: Campo obrigatório
- `[StringLength]`: Limite de caracteres
- `[Range]`: Intervalo de valores numéricos
- `[DataType]`: Tipo específico (Date, Currency, etc.)
- `[Display]`: Nome de exibição amigável
- `[EmailAddress]`: Validação de e-mail
- `[Phone]`: Validação de telefone
- `[Compare]`: Comparação entre campos
- `[RegularExpression]`: Validação por regex

### 2. Interface IValidatableObject

Permite implementar validações customizadas mais complexas que envolvem múltiplos campos ou regras de negócio específicas.  A interface `IValidatableObject` exige a implementação do método `Validate`, que retorna uma coleção de `ValidationResult`.  Cada `ValidationResult` representa uma violação de regra de negócio, permitindo que você especifique a mensagem de erro e os membros afetados.

Exemplo prático:

```csharp
public class CreateUpdateLivroDto : IValidatableObject
{
    [Required]
    public DateTime DataPublicacao { get; set; }

    // ... outras propriedades

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if (DataPublicacao.Date > DateTime.Today)
        {
            yield return new ValidationResult(
                "Data de Publicação não pode ser maior que a data atual!",
                new[] { nameof(DataPublicacao) }
            );
        }

         // Exemplo de validação cruzada entre campos
        if (Titulo == "Proibido" && Preco > 100)
        {
            yield return new ValidationResult(
                "Livros com título 'Proibido' não podem custar mais de R$ 100,00",
                new[] { nameof(Titulo), nameof(Preco) }
            );
        }
    }
}
```

### 3. ValidadorHelper (src\TRF3.SISPREC.Domain.Shared\Apoio\ValidadorHelper.cs)

O `ValidadorHelper` oferece métodos auxiliares para realizar validações comuns, centralizando a lógica e facilitando a reutilização em diferentes partes da aplicação.  Ele simplifica a implementação de validações complexas, encapsulando regras de negócio em métodos estáticos.

Exemplo de uso:

```csharp
using TRF3.SISPREC.Domain.Shared.Apoio;

// ... dentro de um Application Service ou Domain Service

if (!ValidadorHelper.ValidarCpf(cpf))
{
    throw new AbpValidationException("CPF inválido!");
}

if (!ValidadorHelper.ValidarCnpj(cnpj))
{
    throw new AbpValidationException("CNPJ inválido!");
}

// ... outras validações
```

## Implementação no Application Service

O ABP Framework integra automaticamente as validações com os Application Services:

```csharp
public class LivroAppService :
    BaseCrudAppService<Livro, LivroDto, int, LivroGetListInput,
        CreateUpdateLivroDto, CreateUpdateLivroDto>,
    ILivroAppService
{
    // As validações são executadas automaticamente antes de
    // CreateAsync e UpdateAsync
    public override async Task<LivroDto> CreateAsync(CreateUpdateLivroDto input)
    {
        // Se chegou aqui, input já foi validado pelo framework
        await CheckCreatePolicyAsync();
        var livro = await MapToEntityAsync(input);
        // ... resto da implementação
    }


    public override async Task<LivroDto> UpdateAsync(CreateUpdateLivroDto input)
    {
        // Se chegou aqui, input já foi validado pelo framework
        await CheckUpdatePolicyAsync(input.Id);
        var livro = await MapToEntityAsync(input);
        // ... resto da implementação
    }
}
```

## Boas Práticas

1. **Separação de Responsabilidades**:
   - Use Data Annotations para validações simples e diretas em propriedades individuais.
   - Use `IValidatableObject` para validações complexas que envolvem múltiplos campos ou regras de negócio específicas do DTO.
   - Centralize validações de domínio reutilizáveis no `ValidadorHelper` ou em Domain Services.
   - Mantenha validações de domínio na camada de domínio para garantir a consistência dos dados em toda a aplicação.

2. **Mensagens de Erro Claras**:
   - Seja claro, conciso e específico nas mensagens de erro, informando ao usuário o que está errado e como corrigir.
   - Use recursos de localização para internacionalizar as mensagens de erro, adaptando-as ao idioma do usuário.
   - Indique precisamente qual campo falhou na validação, facilitando a correção pelo usuário.

3. **Manutenção e Documentação**:
   - Mantenha constantes para valores máximos, mínimos e outros parâmetros de validação, facilitando a atualização e evitando inconsistências.
   - Documente as validações com comentários XML (`/// <summary>...</summary>`), explicando o propósito e as regras de cada validação.
   - Atualize as validações sempre que as regras de negócio mudarem, garantindo que o código e a documentação estejam sincronizados.

4. **Testes Unitários e de Integração**:
   ```csharp
   [Fact]
   public async Task Should_Not_Create_Livro_With_Future_Date()
   {
       var dto = new CreateUpdateLivroDto
       {
           Titulo = "Teste",
           DataPublicacao = DateTime.Today.AddDays(1)
       };

       await Assert.ThrowsAsync<AbpValidationException>(
           () => _livroAppService.CreateAsync(dto)
       );
   }
   ```

## Validações em DTOs de Consulta

Para DTOs usados em consultas (como `LivroGetListInput`), considere:

- Faça as propriedades nullable para filtros opcionais
- Use ranges adequados para filtros numéricos
- Implemente validações de datas quando necessário
- Documente o propósito de cada filtro

```csharp
public class LivroGetListInput : PagedAndSortedResultRequestDto
{
    [Display(Name = "Dt. Publicação Início")]
    public DateTime? DataPublicacaoInicio { get; set; }

    [Display(Name = "Dt. Publicação Final")]
    public DateTime? DataPublicacaoFinal { get; set; }
}
```

## Recomendações Finais

1. **Documentação**
   - Mantenha os summaries XML atualizados
   - Documente regras de negócio complexas
   - Use exemplos práticos nos comentários

2. **Consistência**
   - Siga o mesmo padrão em todos os DTOs
   - Mantenha mensagens de erro consistentes
   - Use constantes para valores compartilhados

3. **Performance**
   - Evite validações desnecessariamente complexas
   - Use cache quando apropriado
   - Considere o impacto em operações em lote

4. **Segurança**
   - Valide sempre os dados de entrada
   - Não confie apenas em validações client-side
   - Implemente autorização adequada