# Padrões de Codificação e Convenções do Projeto TRF3.SISPREC

Este documento centraliza os padrões de codificação, convenções e boas práticas a serem seguidas no desenvolvimento do projeto TRF3.SISPREC.

## 1. Controle de Versão

-   O projeto utiliza o **Gitflow Workflow**. Consultar a documentação específica do Gitflow para detalhes sobre branches (master, develop, feature, release, hotfix).

## 2. Estrutura de Projetos e Arquivos

### 2.1. Geral

-   **Um arquivo por tipo**: Cada classe, enum, interface, etc., deve residir em seu próprio arquivo.
-   **Nomenclatura Clara**: Utilizar nomes claros e descritivos para arquivos, classes, métodos e variáveis.

### 2.2. Domain.Shared

-   **Organização**: Agrupar tipos relacionados em pastas por entidades (ex: `TRF3.SISPREC.Autores`).
-   **Namespaces**: Seguir o padrão do ABP Framework para namespaces (ex: `TRF3.SISPREC.Autores` para a entidade Autor).

### 2.3. Projeto Web (Exemplo: `TRF3.SISPREC.Web`)

-   **Páginas**: `Pages/{Entidade}/` (ex: `Pages/Livros/`)
    -   `Index.cshtml`: Listagem principal.
    -   `CreateModal.cshtml`: Modal de criação.
    -   `EditModal.cshtml`: Modal de edição.
    -   `DetalheModal.cshtml`: Modal de detalhes.
    -   `index.css`: Estilos específicos da página.
    -   `index.js`: JavaScript da listagem.
    -   `CreateModal.js`: JavaScript do modal de criação.
    -   `EditModal.js`: JavaScript do modal de edição.
    -   `ViewModels/`: ViewModels específicos da entidade/página.
-   **Menus**: Configuração de menus em `Menus/`.

### 2.4. Testes

-   **Projetos de Teste**:
    -   Testes de Domínio: `test/TRF3.SISPREC.Domain.Tests/`
    -   Testes de Aplicação: `test/TRF3.SISPREC.Application.Tests/`
    -   Classes Base e Utilitários de Teste: `TRF3.SISPREC.TestBase/`
-   **Localização Específica de Testes**:
    -   Application Services: `test/TRF3.SISPREC.EntityFrameworkCore.Tests/EntityFrameworkCore/Applications/`
    -   Domain Services: `TRF3.SISPREC.Domain.Tests` (diretamente no projeto)
    -   Web: `test/TRF3.SISPREC.Web.Tests/Pages/`

### 2.5. Templates de PDF

-   Localização: `src\TRF3.SISPREC.Infraestrutura\PDFServices\Templates\`
-   Formato: Arquivos `.html`.

## 3. Boas Práticas de Código (Específico para `Domain.Shared`, mas aplicável em geral)

### 3.1. Documentação

-   **Comentários XML (XML Comments)**: Utilizar comentários XML para documentar todos os tipos públicos e membros (classes, métodos, propriedades).
-   **Exemplos**: Incluir exemplos de uso nos comentários quando relevante.
-   **Atualização**: Manter a documentação sempre atualizada com as mudanças no código.

### 3.2. Constantes

-   **Reuso**: Definir constantes no `Domain.Shared` para promover o reuso em todo o sistema.
-   **Nomenclatura**: Usar nomes descritivos e autoexplicativos em `PascalCase`.
-   **Agrupamento**: Agrupar constantes relacionadas em classes estáticas específicas (ex: `LivroConsts`).

### 3.3. Enums

-   **Atributo `Description`**: Utilizar o atributo `[Description("Texto Amigável")]` para fornecer uma representação textual amigável para os valores do enum, útil para exibição na UI (Interface do Usuário).
-   **Valores**: Manter os nomes dos membros do enum em `PascalCase`.
    _Nota: A documentação original do TRF3.SISPREC.Wiki sugere valores em MAIÚSCULAS para Enums. Em C#, PascalCase é a convenção padrão. Seguir `PascalCase` para novos Enums, a menos que haja uma forte razão histórica ou de consistência interna no projeto para manter `MAIÚSCULAS`._

## 4. Padrões de Implementação (Exemplos)

-   **BaseAtivaDesativaEntity**: Classe base para entidades que necessitam de controle de ativação/desativação (campo booleano como `IsAtivo` ou similar).

## 5. Instruções para Modos Customizados (Agentes BMAD)

-   Todos os agentes devem consultar e seguir estritamente os padrões definidos neste documento (`sisprec-coding-standards.md`).
-   As regras específicas para cada modo (arquiteto, backend, frontend, tester, orchestrator) devem estar alinhadas com estes padrões gerais.
