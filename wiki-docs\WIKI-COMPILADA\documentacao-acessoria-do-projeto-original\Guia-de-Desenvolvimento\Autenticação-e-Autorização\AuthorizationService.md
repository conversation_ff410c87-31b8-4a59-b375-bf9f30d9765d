[[_TOC_]]
# SISPRECAuthorizationService

A classe SISPRECAuthorizationService é responsável por gerenciar de forma centralizada toda a lógica de autorização no SISPREC, utilizando os recursos nativos do ABP Framework e do ASP.NET Core.

## Responsabilidade

-   Verificar e conceder permissões de acesso a recursos internos, mantendo um padrão consistente de segurança.
-   Integrar com o mecanismo de políticas de autorização, permitindo um controle refinado sobre cada requisito.

## Customizações

-   Override dos métodos de autorização para contemplar a role “AdminTI”, concedendo privilégios adicionais a usuários com esse perfil.
-   Ao detectar a role “AdminTI”, a autorização é aprovada automaticamente (AuthorizationResult.Success), permitindo um fluxo de trabalho mais ágil para funções administrativas.

## Definição da Classe

```csharp
//usings...

namespace TRF3.SISPREC.Permissions;

[ExcludeFromCodeCoverage]
[Dependency(ReplaceServices = true)]
[ExposeServices(typeof(IAuthorizationService))]
[ExposeServices(typeof(IAbpAuthorizationService))]
public class SISPRECAuthorizationService : AbpAuthorizationService
{
     public SISPRECAuthorizationService(
        IAuthorizationPolicyProvider policyProvider,
        IAuthorizationHandlerProvider handlers,
        ILogger<DefaultAuthorizationService> logger,
        IAuthorizationHandlerContextFactory contextFactory,
        IAuthorizationEvaluator evaluator,
        IOptions<AuthorizationOptions> options,
        ICurrentPrincipalAccessor currentPrincipalAccessor,
        IServiceProvider serviceProvider
    ) : base(policyProvider, handlers, logger, contextFactory, evaluator, options, currentPrincipalAccessor, serviceProvider
    )
    {
    }

    public override async Task<AuthorizationResult> AuthorizeAsync(ClaimsPrincipal user, object? resource, IEnumerable<IAuthorizationRequirement> requirements)
    {
        if (user.IsInRole(SISPRECPermissoes.Perfil.AdminTI))
            return AuthorizationResult.Success();

        return await base.AuthorizeAsync(user, resource, requirements);
    }

    public override async Task<AuthorizationResult> AuthorizeAsync(ClaimsPrincipal user, object? resource, string policyName)
    {
        if (user.IsInRole(SISPRECPermissoes.Perfil.AdminTI))
            return AuthorizationResult.Success();

        return await base.AuthorizeAsync(user, resource, policyName);
    }
}

```
