---
description: Base de Conhecimento principal do sistema TRF3.SISPREC, contendo informações sobre o domínio de negócio, arquitetura, entidades, integrações, regras de negócio críticas e padrões de desenvolvimento, interface, segurança, performance, testes e operação.
globs: ['**/*']
alwaysApply: true
---

# Base de Conhecimento SISPREC

## Visão Geral do Sistema

### O que é o SISPREC

O TRF3.SISPREC é um sistema de gestão de precatórios e RPVs (Requisições de Pequeno Valor) desenvolvido para o Tribunal Regional Federal da 3ª Região. O sistema gerencia todo o ciclo de vida dos precatórios, desde a requisição inicial até o pagamento final.

### Domínio de Negócio

-   **Precatórios**: Débitos judiciais de valor superior ao limite de RPV
-   **RPVs**: Requisições de Pequeno Valor (até 60 salários mínimos)
-   **Beneficiários**: Pessoas físicas ou jurídicas que têm direito ao recebimento
-   **Processos**: Processos judiciais que originam os precatórios
-   **Propostas**: Agrupamentos de precatórios para pagamento

## Arquitetura Técnica

### Framework e Tecnologias

-   **ABP Framework 8**: Framework base com .NET 8
-   **Entity Framework Core**: ORM (Object-Relational Mapper) para acesso a dados
-   **SQL Server**: Banco de dados principal
-   **Bootstrap 5**: Framework CSS para interface
-   **jQuery**: Biblioteca JavaScript
-   **Quartz.NET**: Agendamento de jobs (trabalhos)

### Estrutura de Projetos

```
src/
├── TRF3.SISPREC.Domain.Shared/          # Enums, Constantes, Helpers (Utilitários)
├── TRF3.SISPREC.Domain/                 # Entities (Entidades - Entity/BaseEntidadeDominio), Domain Services (Serviços de Domínio)
├── TRF3.SISPREC.Application.Contracts/  # DTOs (Data Transfer Objects), Service Interfaces (Interfaces de Serviço)
├── TRF3.SISPREC.Application/            # Application Services (Serviços de Aplicação), AutoMapper
├── TRF3.SISPREC.EntityFrameworkCore/    # DbContext, Repositories (Repositórios), Configurations (Configurações)
├── TRF3.SISPREC.HttpApi/                # NÃO É USADO - Não mexer neste projeto
├── TRF3.SISPREC.Web/                    # Razor Pages, MVC (Model-View-Controller)
├── TRF3.SISPREC.Infraestrutura/         # Integração com Serviços Externos
├── TRF3.SISPREC.ProcessaPrecatorio.*/   # Módulos de Fluxo de Trabalho de Negócio
├── TRF3.SISPREC.SincronizaDominio.*/    # Módulos de Sincronização de Dados
└── TRF3.SISPREC.SincronizacaoLegado/    # Integração com Sistema Legado
```

**Nota Importante**: O projeto `TRF3.SISPREC.HttpApi` não é utilizado no sistema. O SISPREC usa o recurso de Auto API Controllers do ABP Framework, que gera automaticamente endpoints REST baseados nos Application Services. Não deve ser feita nenhuma implementação neste projeto.

### Padrões Arquiteturais

-   **Clean Architecture (Arquitetura Limpa)**: Separação clara de responsabilidades
-   **DDD (Domain-Driven Design) Adaptado (SISPREC)**: Modelagem rica do domínio usando Entity/BaseEntidadeDominio (NUNCA Aggregates/Value Objects - Agregados/Objetos de Valor)
-   **Repository Pattern (Padrão de Repositório)**: Abstração para acesso a dados
-   **CQRS (Command Query Responsibility Segregation) Implícito**: Separação via Application Services
-   **Modular Monolith (Monólito Modular)**: Organização em módulos ABP

### Classes Base de Domínio

#### Entidades Base

-   **BaseAtivaDesativaEntity**: Classe base para entidades com controle de ativação/desativação
-   **BaseEntidadeDominio**: Classe base principal que herda de `BaseAtivaDesativaEntity` e implementa `ISincronizavelCjfDataFim`
    -   Controla ativação com data de fim de utilização
    -   Integra sincronização com CJF (Conselho da Justiça Federal)
    -   Lógica automática entre Ativo e DataUtilizacaoFim

#### Interfaces de Entidades

-   **IEntidadeSincronizavelCjf**: Interface para entidades sincronizáveis com CJF
-   **ISincronizavelCjfDataFim**: Interface que adiciona controle de data de fim de utilização

#### Serviços de Domínio Base

-   **BaseDomainManager<TEntity>**: Classe base para gerenciadores de domínio com operações CRUD (Create, Read, Update, Delete)
-   **BaseSincronizavelManager<TEntity>**: Classe base para gerenciadores de entidades sincronizáveis
-   **IBaseDomainManager<TEntity>**: Interface base para gerenciadores de domínio
-   **ISincronizavelManager<TEntity>**: Interface para gerenciadores de entidades sincronizáveis

## Entidades Principais

### RequisicaoProtocolo

-   **Entidade Principal**: Principal entidade do sistema (usando Entity, ISoftDelete)
-   **Responsabilidades**: Controlar ciclo de vida da requisição
-   **Estados**: Rascunho, EmAnalise, Aprovada, Rejeitada, Paga
-   **Relacionamentos**: Processos, Beneficiários, Propostas

### Processo

-   **Descrição**: Processo judicial que origina o precatório
-   **Atributos**: Número, Vara, Assunto, Valor
-   **Relacionamentos**: RequisicaoProtocolo, Partes

### Beneficiario

-   **Descrição**: Pessoa física ou jurídica beneficiária
-   **Atributos**: CPF/CNPJ, Nome, Dados bancários
-   **Relacionamentos**: RequisicaoProtocolo, ContaBancaria

### Proposta

-   **Descrição**: Agrupamento de requisições para pagamento
-   **Estados**: Aberta, Fechada, Enviada, Paga
-   **Relacionamentos**: RequisicaoProtocolo, Parcelas

## Integrações Externas

### CJF (Conselho da Justiça Federal)

-   **Propósito**: Sincronização de dados de processos
-   **Protocolo**: REST API
-   **Frequência**: Diária via background job (trabalho em segundo plano)
-   **Dados**: Processos, partes, movimentações

### SEI (Sistema Eletrônico de Informações)

-   **Propósito**: Gestão documental
-   **Protocolo**: SOAP/REST
-   **Funcionalidades**: Criação de processos, anexo de documentos
-   **Autenticação**: OAuth 2.0

### MinIO

-   **Propósito**: Armazenamento de arquivos
-   **Protocolo**: API Compatível com S3
-   **Tipos de Arquivo**: PDFs, documentos, imagens
-   **Organização**: Buckets por tipo de documento

### Sistemas Legados UFEP

-   **Propósito**: Migração de dados históricos
-   **Protocolo**: Conexão direta ao banco
-   **Frequência**: Sob demanda
-   **Dados**: Precatórios antigos, beneficiários

## Regras de Negócio Críticas

### Validação de Fases

```csharp
// Métodos que modificam fases devem validar se a fase atual está finalizada
if (!faseAtual.EstaFinalizada)
{
    throw new UserFriendlyException("A fase atual deve estar finalizada antes de prosseguir");
}
```

### Controle de Propostas

```csharp
// RequisicaoVerificacao não deve ser inserida para propostas fechadas
if (proposta.Status == PropostaStatus.Fechada)
{
    throw new UserFriendlyException("Não é possível criar verificação para proposta fechada");
}
```

### Validações de Integridade

-   CPF/CNPJ devem ser válidos
-   Valores monetários devem ser positivos
-   Datas devem ser consistentes
-   Relacionamentos devem existir

## Padrões de Desenvolvimento

### Convenções de Nomenclatura (Naming Conventions)

-   **Entidades**: PascalCase (RequisicaoProtocolo)
-   **DTOs**: Sufixo Dto (RequisicaoProtocoloDto)
-   **Serviços**: Sufixo AppService (RequisicaoProtocoloAppService)
-   **Repositórios**: Prefixo I + Sufixo Repository (IRequisicaoProtocoloRepository)

### Padrão de Application Services

#### Classes Base de AppService Disponíveis

##### 1. BaseAppService

```csharp
/// <summary>
/// Classe base abstrata que fornece funcionalidades comuns para todos os serviços de aplicação.
/// Herda de ApplicationService do ABP Framework e adiciona configurações padrão.
/// </summary>
[DisableAuditing]
[Authorize]
public abstract class BaseAppService : ApplicationService
{
    // Configuração padrão de autorização e auditoria
}
```

**Casos de Uso:**

-   Serviços simples que não seguem padrão CRUD
-   Serviços de coordenação ou orquestração
-   Serviços que implementam operações específicas de negócio

##### 2. BaseReadOnlyAppService

```csharp
/// <summary>
/// Classe base para serviços de aplicação que fornecem apenas operações de leitura.
/// Herda de AbstractKeyReadOnlyAppService do ABP Framework.
/// </summary>
[DisableAuditing]
[Authorize]
public abstract class BaseReadOnlyAppService<TEntity, TEntityDto, TKey, TGetListInput>
    : AbstractKeyReadOnlyAppService<TEntity, TEntityDto, TKey, TGetListInput>
    where TEntity : class, IEntity
{
    protected virtual string? VisualizarPolicyName { get; set; }

    protected BaseReadOnlyAppService(IRepository<TEntity> repository) : base(repository)
    {
        GetPolicyName = VisualizarPolicyName;
        GetListPolicyName = VisualizarPolicyName;
    }
}
```

**Casos de Uso:**

-   Entidades que permitem apenas consulta (tabelas de lookup)
-   Relatórios e consultas complexas
-   Dados de referência que não devem ser modificados via API

##### 3. BaseCrudAppService

```csharp
/// <summary>
/// Classe base para serviços de aplicação que implementam operações CRUD completas.
/// Utiliza DomainManager para operações de inserção, alteração e exclusão.
/// </summary>
[DisableAuditing]
[Authorize]
public abstract class BaseCrudAppService<TEntity, TGetOutputDto, TGetListOutputDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    : AbstractKeyCrudAppService<TEntity, TGetOutputDto, TGetListOutputDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    where TEntity : class, IEntity
{
    protected virtual string? VisualizarPolicyName { get; set; }
    protected virtual string? GravarPolicyName { get; set; }
    protected virtual IBaseDomainManager<TEntity> Manager { get; }

    protected BaseCrudAppService(IRepository<TEntity> repository, IBaseDomainManager<TEntity> manager)
        : base(repository)
    {
        Manager = manager;
        GetPolicyName = VisualizarPolicyName;
        GetListPolicyName = VisualizarPolicyName;
        CreatePolicyName = GravarPolicyName;
        UpdatePolicyName = GravarPolicyName;
        DeletePolicyName = GravarPolicyName;
    }

    public override async Task<TGetOutputDto> CreateAsync(TCreateInput input)
    {
        await CheckCreatePolicyAsync();
        var entity = await MapToEntityAsync(input);
        TryToSetTenantId(entity);
        await Manager.InserirAsync(entity, autoSave: true);
        return await MapToGetOutputDtoAsync(entity);
    }

    public override async Task<TGetOutputDto> UpdateAsync(TKey id, TUpdateInput input)
    {
        await CheckUpdatePolicyAsync();
        var entity = await GetEntityByIdAsync(id);
        await MapToEntityAsync(input, entity);
        await Manager.AlterarAsync(entity, autoSave: true);
        return await MapToGetOutputDtoAsync(entity);
    }

    protected override async Task DeleteByIdAsync(TKey id)
    {
        var entity = await GetEntityByIdAsync(id);
        await Manager.ExcluirAsync(k => k.Equals(entity));
    }
}
```

**Casos de Uso:**

-   Entidades de domínio que precisam de operações CRUD completas
-   Entidades que possuem regras de negócio complexas (via DomainManager)
-   Entidades que requerem validações específicas

##### 4. BaseCrudNoDeleteAppService

```csharp
/// <summary>
/// Classe base para serviços de aplicação CRUD que não permitem operações de exclusão.
/// Herda de BaseCrudAppService e sobrescreve os métodos de exclusão para lançar exceções.
/// </summary>
[DisableAuditing]
[Authorize]
public abstract class BaseCrudNoDeleteAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    : BaseCrudAppService<TEntity, TEntityDto, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    where TEntity : class, IEntity
{
    protected BaseCrudNoDeleteAppService(IRepository<TEntity> repository, IBaseDomainManager<TEntity> manager)
        : base(repository, manager) { }

    [RemoteService(false)]
    public override Task DeleteAsync(TKey id)
    {
        throw new InvalidOperationException("Não é possível excluir essa entidade!");
    }

    [RemoteService(false)]
    protected override Task DeleteByIdAsync(TKey id)
    {
        throw new InvalidOperationException("Não é possível excluir essa entidade!");
    }
}
```

**Casos de Uso:**

-   Entidades críticas que não devem ser excluídas
-   Dados históricos que devem ser preservados
-   Entidades de referência importantes para integridade

##### 5. BaseAtivaDesativaAppService

```csharp
/// <summary>
/// Classe base para serviços de aplicação que gerenciam entidades com estado ativo/inativo.
/// Herda de BaseCrudAppService e adiciona funcionalidade de ativação/desativação.
/// </summary>
[DisableAuditing]
public abstract class BaseAtivaDesativaAppService<TEntity, TGetOutputDto, TGetListOutputDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    : BaseCrudAppService<TEntity, TGetOutputDto, TGetListOutputDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    where TEntity : BaseAtivaDesativaEntity, IEntity
{
    protected BaseAtivaDesativaAppService(IRepository<TEntity> repository, IBaseDomainManager<TEntity> manager)
        : base(repository, manager) { }

    public virtual async Task AtivarDesativarAsync(TKey id)
    {
        var entity = await GetEntityByIdAsync(id);
        entity.Ativo = !entity.Ativo;
        await Manager.AlterarAsync(entity, autoSave: true);
    }
}
```

**Casos de Uso:**

-   Entidades que possuem estado ativo/inativo
-   Cadastros que podem ser temporariamente desabilitados
-   Entidades que requerem desativação lógica (soft disable) ao invés de exclusão

##### 6. BaseSincronizavelAppService

```csharp
/// <summary>
/// Classe base para serviços de aplicação que gerenciam entidades sincronizáveis com sistemas externos.
/// Herda de BaseAtivaDesativaAppService e adiciona controle de sincronização.
/// </summary>
[DisableAuditing]
public abstract class BaseSincronizavelAppService<TEntity, TGetOutputDto, TGetListOutputDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    : BaseAtivaDesativaAppService<TEntity, TGetOutputDto, TGetListOutputDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    where TEntity : BaseEntidadeDominio, IEntity
{
    protected override ISincronizavelManager<TEntity> Manager { get; }

    protected BaseSincronizavelAppService(IRepository<TEntity> repository, ISincronizavelManager<TEntity> manager)
        : base(repository, manager)
    {
        Manager = manager;
    }

    public override async Task<TGetOutputDto> CreateAsync(TCreateInput input)
    {
        await CheckCreatePolicyAsync();
        var entidade = await MapToEntityAsync(input);
        entidade.FoiSincronizadoCjf = false;
        await Manager.InserirAsync(entidade);
        return await MapToGetOutputDtoAsync(entidade);
    }

    public override async Task<TGetOutputDto> UpdateAsync(TKey id, TUpdateInput input)
    {
        await CheckUpdatePolicyAsync();
        var entidade = await GetEntityByIdAsync(id);

        if (await Manager.RegistroFoiSincronizadoCjf(entidade))
            throw new UserFriendlyException("Não é permitido alterar registros sincronizados com o CJF.");

        await MapToEntityAsync(input, entidade);
        entidade.FoiSincronizadoCjf = false;
        await Manager.AlterarAsync(entidade, autoSave: true);
        return await MapToGetOutputDtoAsync(entidade);
    }
}
```

**Casos de Uso:**

-   Entidades que são sincronizadas com sistemas externos (CJF)
-   Dados que possuem origem externa e não devem ser alterados após sincronização
-   Entidades que requerem controle de origem dos dados

##### 7. SISPRECBaseSettingsAppService

```csharp
/// <summary>
/// Classe base para serviços de aplicação que gerenciam configurações do sistema.
/// </summary>
public abstract class SISPRECBaseSettingsAppService : BaseAppService
{
    protected readonly ISettingManager SettingManager;
    protected readonly IBackgroundJobsService BackgroundJobsService;

    public SISPRECBaseSettingsAppService(ISettingManager settingManager, IBackgroundJobsService backgroundJobsService)
    {
        SettingManager = settingManager;
        BackgroundJobsService = backgroundJobsService;
    }

    public bool IsConfiguracaoAtiva(string configKeyName)
    {
        return SettingManager.GetGlobalBool(configKeyName);
    }
}
```

**Casos de Uso:**

-   Serviços que gerenciam configurações do sistema
-   Serviços que interagem com background jobs
-   Operações de configuração e administração

#### Exemplo de Implementação

```csharp
public class RequisicaoProtocoloAppService :
    BaseCrudAppService<RequisicaoProtocolo, RequisicaoProtocoloDto, Guid>
{
    // Implementação específica
}
```

### Padrão de Configuração de Entidade (Entity Configuration Pattern)

```csharp
public class RequisicaoProtocoloConfiguration : IEntityTypeConfiguration<RequisicaoProtocolo>
{
    public void Configure(EntityTypeBuilder<RequisicaoProtocolo> builder)
    {
        builder.ToTable("RequisicaoProtocolo");
        builder.HasKey(x => x.Id);
        // Outras configurações
    }
}
```

## Padrões de Interface

### Formulários

-   Preferir formulários normais sobre `abp-dynamic-form`
-   Usar Bootstrap 5 + ABP Tag Helpers
-   Controles de filtro em uma linha
-   Validações client-side e server-side

### Listagens

-   DataTables com paginação server-side
-   Filtros avançados
-   Exportação para Excel/PDF
-   Ações em lote quando apropriado

### Navegação

-   Breadcrumbs (Trilha de navegação) para contexto
-   Menus hierárquicos com permissões
-   Notificações para feedback
-   Estados de loading (carregamento) para operações assíncronas

## Permissões e Segurança

### Sistema de Permissões SISPREC-CAU

```csharp
public static class SISPRECPermissoes
{
    private const string PermissaoVisualizar = ".Visualizar";
    private const string PermissaoGravar = ".Gravar";

    public static class RequisicaoProtocolo
    {
        public const string Visualizar = nameof(RequisicaoProtocolo) + PermissaoVisualizar;
        public const string Gravar = nameof(RequisicaoProtocolo) + PermissaoGravar;
    }

    public static class Perfil
    {
        public const string AdminTI = "AdminTI";
    }
}
```

### Convenções de Permissões

-   **Visualizar**: Permissão básica que dá acesso ao menu e páginas de listagem/detalhes
-   **Gravar**: Permissão para operações de criação, edição e exclusão
-   **AdminTI**: Perfil com acesso total ao sistema
-   Integração com CAU (servidor OIDC do TRF3)

### Auditoria

-   Entidades auditadas usam anotação `[Audited]`
-   Propriedades de navegação devem usar `[DisableAuditing]`
-   `AuditedEfCoreRepository` para auditoria garantida em qualquer contexto
-   Visualização de histórico via componente "Historico" em modais

### SoftDelete (Exclusão Lógica)

-   Implementar interface `ISoftDelete` na entidade
-   Propriedade `IsDeleted` é automaticamente gerenciada
-   Usar `IDataFilter<ISoftDelete>` para incluir registros deletados quando necessário

## Performance e Otimização

### Entity Framework

-   Usar `Include()` para carregamento eager (antecipado)
-   Evitar queries N+1
-   Índices apropriados no banco
-   Paginação server-side

### Cache

-   Redis para dados frequentemente acessados
-   Cache de consultas complexas
-   Invalidação automática em mudanças
-   TTL (Time To Live - Tempo de Vida) apropriado por tipo de dado

### Background Jobs (Trabalhos em Segundo Plano)

-   Processamento assíncrono com Quartz
-   Jobs resilientes com retry (nova tentativa)
-   Monitoramento de execução
-   Logs detalhados

## Testes e Qualidade SISPREC

### Estratégia de Testes SISPREC

-   **Testes Unitários (Unit Tests)**: Camada de Domínio (`SISPRECDomainTestBase`)
-   **Testes de Integração (Integration Tests)**: Application Services (`BaseAppServiceTests`) e EntityFrameworkCore
-   **Testes Web (Web Tests)**: Razor Pages (`SISPRECWebTestBase`)
-   **Testes de Performance (Performance Tests)**: Queries e APIs críticas

### Estrutura de Projetos de Teste

-   **Domain**: `test/TRF3.SISPREC.Domain.Tests/`
-   **Application**: `test/TRF3.SISPREC.EntityFrameworkCore.Tests/EntityFrameworkCore/Applications/`
-   **Repository**: `test/TRF3.SISPREC.EntityFrameworkCore.Tests/EntityFrameworkCore/`
-   **Web**: `test/TRF3.SISPREC.Web.Tests/Pages/`

### Cobertura Esperada

-   Camada de Domínio (Domain Layer): 90%+
-   Camada de Aplicação (Application Layer): 85%+
-   Camada de Infraestrutura (Infrastructure Layer): 70%+
-   Camada Web (Web Layer): 60%+

### Ferramentas SISPREC (OBRIGATÓRIAS)

-   **xUnit**: Framework de testes principal
-   **NSubstitute**: Para mocking (simulação) (NÃO usar Moq)
-   **Shouldly**: Para assertions (afirmações) (NÃO usar FluentAssertions)
-   **Bogus**: Para geração de dados (usar `Random.Hash()` para strings)
-   **SQLite**: Banco em memória para testes de integração
-   **HtmlAgilityPack**: Para parsing (análise) HTML em testes web

### Regras Específicas SISPREC

-   Sempre usar `.Wait()` após `WithUnitOfWorkAsync` no construtor
-   Usar `autoSave: true` ao inserir registros
-   Usar constantes de `SISPRECTestConsts` para IDs pré-carregados
-   Usar `Random.Hash()` para strings, não `Random.String()`
-   Usar `IsDelete = true` como padrão no Bogus

## Implantação (Deployment) e Operação

### Ambiente

-   Windows Server + IIS
-   SQL Server 2019+
-   Redis para cache
-   MinIO para arquivos

### Monitoramento

-   Logs estruturados com Serilog
-   Métricas de performance
-   Health checks (Verificações de saúde)
-   Alertas automáticos

### Backup e Recuperação (Recovery)

-   Backup diário do banco
-   Replicação de arquivos
-   Testes de recuperação
-   Documentação de procedimentos

