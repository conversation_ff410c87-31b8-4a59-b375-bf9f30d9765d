# Extensões de Coluna de Tabela de Dados para ASP.NET Core UI

## Introdução

O sistema de extensão de coluna de tabela de dados permite adicionar uma **nova coluna de tabela** na interface do usuário. O exemplo abaixo adiciona uma nova coluna com o título "Social security no":

![user-action-extension-click-me](/ABP-Docs/images/table-column-extension-example.png)

Você pode usar as opções padrão de coluna para controlar a coluna da tabela.

> Note que esta é uma API de baixo nível para controlar a coluna da tabela. Se você quiser mostrar uma propriedade de extensão na tabela, veja o documento de [extensão de entidade do módulo](../../Module-Entity-Extensions.md).

## Como Configurar

### Criar um Arquivo JavaScript

Primeiro, adicione um novo arquivo JavaScript à sua solução. Nós adicionamos dentro da pasta `/Pages/Identity/Users` do projeto `.Web`:

![user-action-extension-on-solution](/ABP-Docs/images/user-action-extension-on-solution.png)

Aqui está o conteúdo deste arquivo JavaScript:

```js
abp.ui.extensions.tableColumns
    .get('identity.user')
    .addContributor(function (columnList) {
        columnList.addTail({ //add as the last column
            title: 'Social security no',
            data: 'extraProperties.SocialSecurityNumber',
            orderable: false,
            render: function (data, type, row) {
                if (row.extraProperties.SocialSecurityNumber) {
                    return '<strong>' + 
                        row.extraProperties.SocialSecurityNumber + 
                        '<strong>';
                } else {
                    return '<i class="text-muted">undefined</i>';
                }
            }
        });
    });
```

Este exemplo define uma função de `render` personalizada para retornar um HTML personalizado para renderizar na coluna.

### Adicionar o Arquivo à Página de Gerenciamento de Usuários

Em seguida, você precisa adicionar este arquivo JavaScript à página de gerenciamento de usuários. Você pode aproveitar o poder do [sistema de Agrupamento e Minificação](https://docs.abp.io/en/abp/latest/UI/AspNetCore/Bundling-Minification).

Escreva o seguinte código dentro do `ConfigureServices` da sua classe de módulo:

```csharp
Configure<AbpBundlingOptions>(options =>
{
    options.ScriptBundles.Configure(
        typeof(Volo.Abp.Identity.Web.Pages.Identity.Users.IndexModel).FullName,
        bundleConfiguration =>
        {
            bundleConfiguration.AddFiles(
                "/Pages/Identity/Users/<USER>"
            );
        });
});
```

Esta configuração adiciona `my-user-extensions.js` à página de gerenciamento de usuários do Módulo de Identidade. `typeof(Volo.Abp.Identity.Web.Pages.Identity.Users.IndexModel).FullName` é o nome do pacote na página de gerenciamento de usuários. Esta é uma convenção comum usada para todos os módulos comerciais do ABP.

### Renderizando a Coluna

Este exemplo assume que você definiu uma propriedade extra `SocialSecurityNumber` usando o sistema de [extensão de entidade do módulo](../../Module-Entity-Extensions.md). No entanto;

* Você pode adicionar uma nova coluna que está relacionada a uma propriedade existente do usuário (que não foi adicionada à tabela por padrão). Exemplo:

````js
abp.ui.extensions.tableColumns
    .get('identity.user')
    .addContributor(function (columnList) {
        columnList.addTail({
            title: 'Phone confirmed?',
            data: 'phoneNumberConfirmed',
            render: function (data, type, row) {
                if (row.phoneNumberConfirmed) {
                    return '<strong style="color: green">YES<strong>';
                } else {
                    return '<i class="text-muted">NO</i>';
                }
            }
        });
    });
````

* Você pode adicionar uma nova coluna personalizada que não está relacionada a nenhuma propriedade da entidade, mas uma informação completamente personalizada. Exemplo:

````js
abp.ui.extensions.tableColumns
    .get('identity.user')
    .addContributor(function (columnList) {
        columnList.addTail({
            title: 'Custom column',
            data: {},
            orderable: false,
            render: function (data) {
                if (data.phoneNumber) {
                    return "call: " + data.phoneNumber;
                } else {
                    return '';
                }
            }
        });
    });
````

## API

Esta seção explica os detalhes da API JavaScript `abp.ui.extensions.tableColumns`.

### abp.ui.extensions.tableColumns.get(entityName)

Este método é usado para acessar as colunas da tabela para uma entidade de um módulo específico. Ele leva um parâmetro:

* **entityName**: O nome da entidade definida pelo módulo relacionado.

### abp.ui.extensions.tableColumns.get(entityName).columns

A propriedade `columns` é usada para recuperar uma [lista duplamente ligada](../Common/Utils/Linked-List.md) de colunas previamente definidas para uma tabela. Todos os contribuidores são executados em ordem para preparar a lista final de colunas. Isso normalmente é chamado pelos módulos para mostrar as colunas na tabela. No entanto, você pode usá-lo se estiver construindo suas próprias interfaces de usuário extensíveis.

### abp.ui.extensions.tableColumns.get(entityName).addContributor(contributeCallback [, order])

O método `addContributor` cobre todos os cenários, por exemplo, você deseja adicionar sua coluna em uma posição diferente na lista, alterar ou remover uma coluna existente. `addContributor` tem os seguintes parâmetros:

* **contributeCallback**: Uma função de retorno de chamada que é chamada sempre que a lista de colunas deve ser criada. Você pode modificar livremente a lista de colunas dentro deste método de retorno de chamada.
* **order** (opcional): A ordem do retorno de chamada na lista de retornos de chamada. Seu retorno de chamada é adicionado ao final da lista (então, você tem a oportunidade de modificar as colunas adicionadas pelos contribuintes anteriores). Você pode definir como `0` para adicionar seu contribuinte como o primeiro item.

#### Exemplo

```js
var myColumnDefinition = {
    title: 'Custom column',
    data: {},
    orderable: false,
    render: function(data) {
        if (data.phoneNumber) {
            return "call: " + data.phoneNumber;
        } else {
            return '';
        }
    }
};

abp.ui.extensions.tableColumns
    .get('identity.user')
    .addContributor(function (columnList) {
        // Remove um item da actionList
        columnList.dropHead();

        // Adicione um novo item à actionList
        columnList.addHead(myColumnDefinition);
    });
```

> `columnList` é uma [lista ligada](../Common/Utils/Linked-List.md). Você pode usar seus métodos para construir uma lista de colunas conforme necessário.
