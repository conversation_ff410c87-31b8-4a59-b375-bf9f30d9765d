[[_TOC_]]

# Projeto Web

O projeto Web contém a interface de usuário (UI) implementada usando ASP.NET Core MVC com Razor Pages.

## Entidades

### [Livros](/Tutorial-de-Início/Projeto-Web/Livros.md)

Documentação da implementação dos componentes de UI relacionados à entidade Livro:
- Página de listagem com DataTables
- Modal de criação
- Modal de edição
- Modal de detalhes
- ViewModels e validações
- JavaScript e integrações

### [Autores](/Tutorial-de-Início/Projeto-Web/Autores.md)

Documentação da implementação dos componentes de UI relacionados à entidade Autor:
- Página de listagem com DataTables
- Modal de criação
- Modal de edição
- Modal de detalhes
- ViewModels e validações
- JavaScript e integrações

**[Próximo: Projeto Web - Livros](/Tutorial-de-Início/Projeto-Web/Livros.md)**