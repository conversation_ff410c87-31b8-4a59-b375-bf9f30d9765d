# Padrões do ABP Framework - SISPREC

## Padrões Específicos do ABP Framework 8 para SISPREC

### 1. Padrões de Sistema de Módulos (Module System Patterns)

#### 1.1 Definição de Módulo (Module Definition)

```csharp
[DependsOn(
    typeof(SISPRECDomainSharedModule),
    typeof(AbpAuditingModule),
    typeof(AbpBackgroundJobsQuartzModule)
)]
public class SISPRECDomainModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        // Configurações específicas do Módulo
    }
}
```

#### 1.2 Dependências de Módulo (Module Dependencies)

-   **Domain.Shared** → Módulo base com enums e constants
-   **Domain** → Lógica de negócio e entities
-   **Application.Contracts** → DTOs e service interfaces
-   **Application** → Implementação de Application Services
-   **EntityFrameworkCore** → Camada de acesso a dados
-   **HttpApi** → REST API controllers
-   **Web** → Camada de apresentação

### 2. Padrões de Entidade (Entity Patterns)

#### 2.1 Padrão de Entidade (Entity Pattern) (Padrão SISPREC)

```csharp
[Audited]
public class RequisicaoProtocolo : Entity, ISoftDelete
{
    public override object[] GetKeys()
    {
        return new object[] { RequisicaoProtocoloId };
    }

    public Guid RequisicaoProtocoloId { get; set; }

    // Properties com encapsulamento adequado
    public string Numero { get; private set; }
    public StatusRequisicao Status { get; private set; }

    // ISoftDelete implementation
    public bool IsDeleted { get; set; }

    // Construtor protected para EF Core
    protected RequisicaoProtocolo() { }

    // Construtor public com regras de negócio
    public RequisicaoProtocolo(string numero, TipoRequisicao tipo)
    {
        Numero = Check.NotNullOrWhiteSpace(numero, nameof(numero));
        Tipo = tipo;
        Status = StatusRequisicao.Rascunho;
    }

    // Business methods
    public void AvancarParaProximaFase()
    {
        // Lógica de negócio com validação
        if (!FaseAtual.EstaFinalizada)
        {
            throw new UserFriendlyException("A fase atual deve estar finalizada");
        }
        // Implementação
    }
}
```

#### 2.2 Padrão BaseEntidadeDominio (Padrão SISPREC Customizado)

```csharp
public class ValorTipo : BaseEntidadeDominio
{
    public override object[] GetKeys()
    {
        return new object[] { Seq_Valor_Tipo };
    }

    public int Seq_Valor_Tipo { get; set; }
    public required string Codigo { get; set; }
    public required string Descricao { get; set; }

    // Business methods
    public void AlterarCodigo(string novoCodigo)
    {
        Codigo = Check.NotNullOrWhiteSpace(novoCodigo, nameof(novoCodigo));
    }
}
```

### 3. Padrões de Serviço de Aplicação (Application Service Patterns)

#### 3.1 Hierarquia de Classes Base de AppService

##### BaseAppService

```csharp
/// <summary>
/// Classe base abstrata que fornece funcionalidades comuns para todos os serviços de aplicação.
/// Herda de ApplicationService do ABP Framework e adiciona configurações padrão.
/// </summary>
[DisableAuditing]
[Authorize]
public abstract class BaseAppService : ApplicationService
{
    // Configuração padrão de autorização e auditoria
}
```

##### BaseReadOnlyAppService

```csharp
/// <summary>
/// Classe base para serviços de aplicação que fornecem apenas operações de leitura.
/// </summary>
[DisableAuditing]
[Authorize]
public abstract class BaseReadOnlyAppService<TEntity, TEntityDto, TKey, TGetListInput>
    : AbstractKeyReadOnlyAppService<TEntity, TEntityDto, TKey, TGetListInput>
    where TEntity : class, IEntity
{
    protected virtual string? VisualizarPolicyName { get; set; }

    protected BaseReadOnlyAppService(IRepository<TEntity> repository) : base(repository)
    {
        GetPolicyName = VisualizarPolicyName;
        GetListPolicyName = VisualizarPolicyName;
    }
}
```

##### BaseCrudAppService

```csharp
/// <summary>
/// Classe base para serviços de aplicação que implementam operações CRUD completas.
/// Utiliza DomainManager para operações de inserção, alteração e exclusão.
/// </summary>
[DisableAuditing]
[Authorize]
public abstract class BaseCrudAppService<TEntity, TGetOutputDto, TGetListOutputDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    : AbstractKeyCrudAppService<TEntity, TGetOutputDto, TGetListOutputDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    where TEntity : class, IEntity
{
    protected virtual string? VisualizarPolicyName { get; set; }
    protected virtual string? GravarPolicyName { get; set; }
    protected virtual IBaseDomainManager<TEntity> Manager { get; }

    protected BaseCrudAppService(IRepository<TEntity> repository, IBaseDomainManager<TEntity> manager)
        : base(repository)
    {
        Manager = manager;
        GetPolicyName = VisualizarPolicyName;
        GetListPolicyName = VisualizarPolicyName;
        CreatePolicyName = GravarPolicyName;
        UpdatePolicyName = GravarPolicyName;
        DeletePolicyName = GravarPolicyName;
    }

    public override async Task<TGetOutputDto> CreateAsync(TCreateInput input)
    {
        await CheckCreatePolicyAsync();
        var entity = await MapToEntityAsync(input);
        TryToSetTenantId(entity);
        await Manager.InserirAsync(entity, autoSave: true);
        return await MapToGetOutputDtoAsync(entity);
    }

    public override async Task<TGetOutputDto> UpdateAsync(TKey id, TUpdateInput input)
    {
        await CheckUpdatePolicyAsync();
        var entity = await GetEntityByIdAsync(id);
        await MapToEntityAsync(input, entity);
        await Manager.AlterarAsync(entity, autoSave: true);
        return await MapToGetOutputDtoAsync(entity);
    }

    protected override async Task DeleteByIdAsync(TKey id)
    {
        var entity = await GetEntityByIdAsync(id);
        await Manager.ExcluirAsync(k => k.Equals(entity));
    }
}
```

##### BaseCrudNoDeleteAppService

```csharp
/// <summary>
/// Classe base para serviços de aplicação CRUD que não permitem operações de exclusão.
/// </summary>
[DisableAuditing]
[Authorize]
public abstract class BaseCrudNoDeleteAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    : BaseCrudAppService<TEntity, TEntityDto, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    where TEntity : class, IEntity
{
    protected BaseCrudNoDeleteAppService(IRepository<TEntity> repository, IBaseDomainManager<TEntity> manager)
        : base(repository, manager) { }

    [RemoteService(false)]
    public override Task DeleteAsync(TKey id)
    {
        throw new InvalidOperationException("Não é possível excluir essa entidade!");
    }

    [RemoteService(false)]
    protected override Task DeleteByIdAsync(TKey id)
    {
        throw new InvalidOperationException("Não é possível excluir essa entidade!");
    }
}
```

##### BaseAtivaDesativaAppService

```csharp
/// <summary>
/// Classe base para serviços de aplicação que gerenciam entidades com estado ativo/inativo.
/// </summary>
[DisableAuditing]
public abstract class BaseAtivaDesativaAppService<TEntity, TGetOutputDto, TGetListOutputDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    : BaseCrudAppService<TEntity, TGetOutputDto, TGetListOutputDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    where TEntity : BaseAtivaDesativaEntity, IEntity
{
    protected BaseAtivaDesativaAppService(IRepository<TEntity> repository, IBaseDomainManager<TEntity> manager)
        : base(repository, manager) { }

    public virtual async Task AtivarDesativarAsync(TKey id)
    {
        var entity = await GetEntityByIdAsync(id);
        entity.Ativo = !entity.Ativo;
        await Manager.AlterarAsync(entity, autoSave: true);
    }
}
```

##### BaseSincronizavelAppService

```csharp
/// <summary>
/// Classe base para serviços de aplicação que gerenciam entidades sincronizáveis com sistemas externos.
/// </summary>
[DisableAuditing]
public abstract class BaseSincronizavelAppService<TEntity, TGetOutputDto, TGetListOutputDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    : BaseAtivaDesativaAppService<TEntity, TGetOutputDto, TGetListOutputDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    where TEntity : BaseEntidadeDominio, IEntity
{
    protected override ISincronizavelManager<TEntity> Manager { get; }

    protected BaseSincronizavelAppService(IRepository<TEntity> repository, ISincronizavelManager<TEntity> manager)
        : base(repository, manager)
    {
        Manager = manager;
    }

    public override async Task<TGetOutputDto> CreateAsync(TCreateInput input)
    {
        await CheckCreatePolicyAsync();
        var entidade = await MapToEntityAsync(input);
        entidade.FoiSincronizadoCjf = false;
        await Manager.InserirAsync(entidade);
        return await MapToGetOutputDtoAsync(entidade);
    }

    public override async Task<TGetOutputDto> UpdateAsync(TKey id, TUpdateInput input)
    {
        await CheckUpdatePolicyAsync();
        var entidade = await GetEntityByIdAsync(id);

        if (await Manager.RegistroFoiSincronizadoCjf(entidade))
            throw new UserFriendlyException("Não é permitido alterar registros sincronizados com o CJF.");

        await MapToEntityAsync(input, entidade);
        entidade.FoiSincronizadoCjf = false;
        await Manager.AlterarAsync(entidade, autoSave: true);
        return await MapToGetOutputDtoAsync(entidade);
    }
}
```

##### SISPRECBaseSettingsAppService

```csharp
/// <summary>
/// Classe base para serviços de aplicação que gerenciam configurações do sistema.
/// </summary>
public abstract class SISPRECBaseSettingsAppService : BaseAppService
{
    protected readonly ISettingManager SettingManager;
    protected readonly IBackgroundJobsService BackgroundJobsService;

    public SISPRECBaseSettingsAppService(ISettingManager settingManager, IBackgroundJobsService backgroundJobsService)
    {
        SettingManager = settingManager;
        BackgroundJobsService = backgroundJobsService;
    }

    public bool IsConfiguracaoAtiva(string configKeyName)
    {
        return SettingManager.GetGlobalBool(configKeyName);
    }
}
```

#### 3.2 Exemplo de Implementação CRUD Completa

```csharp
[Authorize(SISPRECPermissions.RequisicaoProtocolo.Default)]
public class RequisicaoProtocoloAppService :
    CrudAppService<RequisicaoProtocolo, RequisicaoProtocoloDto, Guid,
                   GetRequisicaoProtocoloListDto, CreateRequisicaoProtocoloDto,
                   UpdateRequisicaoProtocoloDto>,
    IRequisicaoProtocoloAppService
{
    public RequisicaoProtocoloAppService(IRepository<RequisicaoProtocolo, Guid> repository)
        : base(repository)
    {
        GetPolicyName = SISPRECPermissions.RequisicaoProtocolo.Default;
        CreatePolicyName = SISPRECPermissions.RequisicaoProtocolo.Create;
        UpdatePolicyName = SISPRECPermissions.RequisicaoProtocolo.Edit;
        DeletePolicyName = SISPRECPermissions.RequisicaoProtocolo.Delete;
    }

    protected override async Task<IQueryable<RequisicaoProtocolo>> CreateFilteredQueryAsync(
        GetRequisicaoProtocoloListDto input)
    {
        var query = await ReadOnlyRepository.GetQueryableAsync();

        return query
            .WhereIf(!input.Filtro.IsNullOrWhiteSpace(), x => x.Numero.Contains(input.Filtro))
            .WhereIf(input.Status.HasValue, x => x.Status == input.Status);
    }

    // Custom business methods
    public async Task<RequisicaoProtocoloDto> AvancarFaseAsync(Guid id)
    {
        var entity = await GetEntityByIdAsync(id);
        entity.AvancarParaProximaFase();
        await Repository.UpdateAsync(entity);
        return await MapToGetOutputDtoAsync(entity);
    }
}
```

#### 3.3 Guia de Seleção de Classe Base

| Cenário                            | Classe Base Recomendada         | Justificativa                         |
| ---------------------------------- | ------------------------------- | ------------------------------------- |
| Entidade apenas para consulta      | `BaseReadOnlyAppService`        | Operações de leitura apenas           |
| Entidade com CRUD completo         | `BaseCrudAppService`            | Operações completas com DomainManager |
| Entidade que não pode ser excluída | `BaseCrudNoDeleteAppService`    | Preservação de dados críticos         |
| Entidade com estado ativo/inativo  | `BaseAtivaDesativaAppService`   | Controle de ativação                  |
| Entidade sincronizada com CJF      | `BaseSincronizavelAppService`   | Controle de sincronização             |
| Configurações do sistema           | `SISPRECBaseSettingsAppService` | Gerenciamento de settings             |
| Operações específicas de negócio   | `BaseAppService`                | Flexibilidade total                   |

#### 3.2 Custom Application Service

```csharp
public class ProcessamentoPrecatorioAppService : ApplicationService, IProcessamentoPrecatorioAppService
{
    private readonly IRequisicaoProtocoloRepository _repository;
    private readonly ProcessamentoDomainService _domainService;

    public ProcessamentoPrecatorioAppService(
        IRequisicaoProtocoloRepository repository,
        ProcessamentoDomainService domainService)
    {
        _repository = repository;
        _domainService = domainService;
    }

    [Authorize(SISPRECPermissions.Processamento.Execute)]
    public async Task<ProcessamentoResultDto> ProcessarAsync(ProcessarRequisicaoDto input)
    {
        var requisicao = await _repository.GetAsync(input.RequisicaoId);

        var resultado = await _domainService.ProcessarAsync(requisicao, input.Observacao);

        return ObjectMapper.Map<ProcessamentoResult, ProcessamentoResultDto>(resultado);
    }
}
```

### 4. Padrões de Sistema de Permissões (Permission System Patterns)

#### 4.1 Definição de Permissão (Permission Definition)

```csharp
public static class SISPRECPermissoes
{
    private const string PermissaoVisualizar = ".Visualizar";
    private const string PermissaoGravar = ".Gravar";

    public static class RequisicaoProtocolo
    {
        public const string Visualizar = nameof(RequisicaoProtocolo) + PermissaoVisualizar;
        public const string Gravar = nameof(RequisicaoProtocolo) + PermissaoGravar;
    }

    public static class MenuAnalises
    {
        public const string Visualizar = nameof(MenuAnalises) + PermissaoVisualizar;
    }

    public static class MenuExpedienteAdministrativo
    {
        public const string Visualizar = nameof(MenuExpedienteAdministrativo) + PermissaoVisualizar;
    }

    public static class MenuGerencial
    {
        public const string Visualizar = nameof(MenuGerencial) + PermissaoVisualizar;
    }

    public static class Perfil
    {
        public const string AdminTI = "AdminTI";
    }
}
```

#### 4.2 Registro de Permissão (Permission Registration)

```csharp
public static void RegistrarPermissoes(AuthorizationOptions options)
{
    RegistrarPermissao(options, RequisicaoProtocolo.Visualizar);
    RegistrarPermissao(options, RequisicaoProtocolo.Gravar);

    RegistrarPermissao(options, MenuAnalises.Visualizar);
    RegistrarPermissao(options, MenuExpedienteAdministrativo.Visualizar);
    RegistrarPermissao(options, MenuGerencial.Visualizar);
}

private static void RegistrarPermissao(AuthorizationOptions options, string permissao)
{
    options.AddPolicy(permissao, policy => policy.RequireClaim(NomeClaimTypePermissoes, permissao));
}
```

### 5. Padrões de Repositório (Repository Patterns)

#### 5.1 Interface de Repositório (Repository Interface) (Domain)

```csharp
public interface IRequisicaoProtocoloRepository : IRepository<RequisicaoProtocolo, Guid>
{
    Task<List<RequisicaoProtocolo>> GetByStatusAsync(
        StatusRequisicao status,
        CancellationToken cancellationToken = default);

    Task<RequisicaoProtocolo> FindByNumeroAsync(
        string numero,
        CancellationToken cancellationToken = default);

    Task<List<RequisicaoProtocolo>> GetWithProcessosAsync(
        Guid[] ids,
        CancellationToken cancellationToken = default);
}
```

#### 5.2 Implementação de Repositório (Repository Implementation) (Infrastructure)

```csharp
public class EfRequisicaoProtocoloRepository :
    EfCoreRepository<SISPRECDbContext, RequisicaoProtocolo, Guid>,
    IRequisicaoProtocoloRepository
{
    public EfRequisicaoProtocoloRepository(IDbContextProvider<SISPRECDbContext> dbContextProvider)
        : base(dbContextProvider) { }

    public async Task<List<RequisicaoProtocolo>> GetByStatusAsync(
        StatusRequisicao status,
        CancellationToken cancellationToken = default)
    {
        var query = await GetQueryableAsync();
        return await query
            .Where(x => x.Status == status)
            .ToListAsync(GetCancellationToken(cancellationToken));
    }

    public async Task<List<RequisicaoProtocolo>> GetWithProcessosAsync(
        Guid[] ids,
        CancellationToken cancellationToken = default)
    {
        var query = await GetQueryableAsync();
        return await query
            .Where(x => ids.Contains(x.Id))
            .Include(x => x.Processos)
            .ToListAsync(GetCancellationToken(cancellationToken));
    }
}
```

### 6. Padrões de Background Job (Background Job Patterns)

#### 6.1 Definição de Background Job (Background Job Definition)

```csharp
[BackgroundJobName("sincronizacao-cjf")]
public class SincronizacaoCjfJob : AsyncBackgroundJob<SincronizacaoCjfArgs>
{
    private readonly ICjfService _cjfService;
    private readonly ILogger<SincronizacaoCjfJob> _logger;

    public SincronizacaoCjfJob(ICjfService cjfService, ILogger<SincronizacaoCjfJob> logger)
    {
        _cjfService = cjfService;
        _logger = logger;
    }

    public override async Task ExecuteAsync(SincronizacaoCjfArgs args)
    {
        _logger.LogInformation("Iniciando sincronização CJF para data: {Data}", args.DataSincronizacao);

        try
        {
            await _cjfService.SincronizarProcessosAsync(args.DataSincronizacao);
            _logger.LogInformation("Sincronização CJF concluída com sucesso");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro na sincronização CJF");
            throw;
        }
    }
}
```

#### 6.2 Agendamento de Job (Job Scheduling)

```csharp
public class SincronizacaoBackgroundService : BackgroundService
{
    private readonly IBackgroundJobManager _backgroundJobManager;

    public SincronizacaoBackgroundService(IBackgroundJobManager backgroundJobManager)
    {
        _backgroundJobManager = backgroundJobManager;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        // Agendar job diário
        await _backgroundJobManager.EnqueueAsync(
            new SincronizacaoCjfArgs { DataSincronizacao = DateTime.Today },
            delay: TimeSpan.FromHours(1)
        );
    }
}
```

### 7. Padrões de Auditoria e SoftDelete (Audit and SoftDelete Patterns)

#### 7.1 Entidade Auditada (Audited Entity)

```csharp
[Audited]
public class RequisicaoProtocolo : Entity, ISoftDelete
{
    public Guid Id { get; set; }
    public string Numero { get; set; }

    // ISoftDelete implementation
    public bool IsDeleted { get; set; }

    // Navigation properties devem desabilitar auditing
    [DisableAuditing]
    public virtual UnidadeJudicial UnidadeJudicial { get; set; }
}
```

#### 7.2 Uso de SoftDelete com DataFilter (SoftDelete Usage with DataFilter)

```csharp
public class RequisicaoService : ITransientDependency
{
    private readonly IRepository<RequisicaoProtocolo, Guid> _repository;
    private readonly IDataFilter _dataFilter;

    public RequisicaoService(IRepository<RequisicaoProtocolo, Guid> repository, IDataFilter dataFilter)
    {
        _repository = repository;
        _dataFilter = dataFilter;
    }

    public async Task<List<RequisicaoProtocolo>> GetAllIncludingDeletedAsync()
    {
        using (_dataFilter.Disable<ISoftDelete>())
        {
            return await _repository.GetListAsync();
        }
    }
}
```

#### 7.3 AuditedEfCoreRepository para Auditoria Garantida (AuditedEfCoreRepository for Guaranteed Auditing)

```csharp
public class RequisicaoRepository : AuditedEfCoreRepository<SISPRECDbContext, RequisicaoProtocolo, Guid>
{
    public RequisicaoRepository(IDbContextProvider<SISPRECDbContext> dbContextProvider)
        : base(dbContextProvider) { }
}
```

### 8. Padronização de Nomenclatura e Textos (Exclusivamente pt-BR)

// O SISPREC utiliza exclusivamente o idioma Português (Brasil).
// Todas as strings e textos devem ser diretamente em pt-BR.
// Não há necessidade de classes de recurso ou mecanismos de localização.

```csharp
public class RequisicaoProtocoloAppService : ApplicationService
{
    public RequisicaoProtocoloAppService()
    {
        LocalizationResource = typeof(SISPRECResource);
    }

    public async Task<string> GetStatusDescriptionAsync(StatusRequisicao status)
    {
        return $"Status da Requisição: {status}";
    }
}
```

### 9. Padrões de Tratamento de Exceções (Exception Handling Patterns)

#### 9.1 Exceções de Regra de Negócio (Business Rule Exceptions)

```csharp
public void AvancarFase()
{
    if (!FaseAtual.EstaFinalizada)
    {
        throw new UserFriendlyException(
            "Fase não finalizada",
            code: "SISPREC:FASE_NAO_FINALIZADA"
        );
    }
}
```

#### 9.2 Filtro de Exceção Global (Global Exception Filter)

```csharp
public class SISPRECExceptionFilter : IExceptionFilter, ITransientDependency
{
    public void OnException(ExceptionContext context)
    {
        if (context.Exception is SISPRECBusinessException businessEx)
        {
            context.Result = new ObjectResult(new
            {
                error = new
                {
                    code = businessEx.Code,
                    message = businessEx.Message,
                    details = businessEx.Details
                }
            })
            {
                StatusCode = 400
            };

            context.ExceptionHandled = true;
        }
    }
}
```

### 10. Padrões de Serviço de Integração (Integration Service Patterns)

#### 10.1 Interface de Serviço Externo (External Service Interface)

```csharp
public interface ICjfService
{
    Task<ProcessoCjfDto> ConsultarProcessoAsync(string numeroProcesso);
    Task<List<ProcessoCjfDto>> SincronizarProcessosAsync(DateTime dataSincronizacao);
}
```

#### 10.2 Implementação Resiliente (Resilient Implementation)

```csharp
public class CjfService : ICjfService, ITransientDependency
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<CjfService> _logger;

    public async Task<ProcessoCjfDto> ConsultarProcessoAsync(string numeroProcesso)
    {
        var policy = Policy
            .Handle<HttpRequestException>()
            .WaitAndRetryAsync(3, retryAttempt =>
                TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)));

        return await policy.ExecuteAsync(async () =>
        {
            var response = await _httpClient.GetAsync($"/processos/{numeroProcesso}");
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<ProcessoCjfDto>(content);
        });
    }
}
```

Estes padrões garantem que o desenvolvimento no SISPREC siga as melhores práticas do ABP Framework 8, mantendo consistência, qualidade e manutenibilidade do código.
