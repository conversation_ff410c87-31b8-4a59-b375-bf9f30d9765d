[[_TOC_]]
##Migrations
### Estrutura e Carga Iniciais
A utilização das migrations vai partir de uma estrutura base, que será aplicada automaticamente pelas migrations `20241114201748_MigracaoInicial.cs` e `20241114201856_CargaInicial.cs`, respectivamente. 
- Observação: Ao fazer merge da branch desenvolvimento na branch de trabalho atual, ou ao criar uma nova branch a partir da desenvolvimento, será necessário dropar o banco SISPREC_DEV, e as migrations irão criar o banco e aplicar a estrutura e carga iniciais, ao inicializar o projeto Web.
- Nos ambiente de Desenvolvimento e Staging, as migrations serão aplicadas automaticamente, na inicialização da aplicação.
  - Caso ocorra algum erro, visualize o log de inicialização para identificar o problema.

##Criação das Configurations

Para facilitar a criação dos scripts SQL a serem enviados para a DIAD posteriormente, será necessário uma atenção maior ao criar as configurations, de maneira a usar os métodos de configuração das propriedades e chaves, para nomeá-las conforme as seguintes regras, tendo em vista que os script serão criados por meio das migrations.
- Declarar todos os nomes em maiúsculo (exceto o "i" do nome dos índices).
  - Apesar de ter um código no método OnModelCreating do SISPRECDbContext, para criar nomes em caixa alta, esse código funciona para as configurations de entidades de bibliotecas do Abp. 
Como os nomes das tabelas e colunas específicas do SISPREC não coincidem com os nomes das estruturas de banco, esse código não se aplica. Por isso, a declaração de todo nome de estruturua de banco na configuration deve ser em maiúsculo.
- Omitir o tipo int na declaração das chaves autoincrementais, por questões de compatibilidade com testes de integração. Exemplo `PropostaConfiguration.cs`:
  - `builder.Property(p => p.PropostaId).HasColumnName("SEQ_PROPOS");`. 
- Nomear a Primary Key (PK): NOME_TABELA_ + P01. Exemplo: APP_AGENCIA_P01. Exemplo:
  - `builder.HasKey(x => new { x.AgenciaId, x.BancoId }).HasName("APP_AGENCIA_P01");`
- Nomear as Foreign Keys (FKs): NOME_TABELA_ + R + {Número da FK com dois dígitos}. Exemplo: APP_AGENCIA_R01, APP_AGENCIA_R02. Exemplos: 
  - `builder.HasOne(x => x.Banco).WithMany(x => x.Agencias).HasForeignKey(x => x.BancoId).HasConstraintName("APP_AGENCIA_R01");`
  - `builder.HasOne(x => x.Municipio).WithMany(x => x.Agencias).HasForeignKey(x => x.MunicipioId).HasConstraintName("APP_AGENCIA_R02");`
- Nomear as chaves únicas: NOME_TABELA_ + U + {Número da chave única da tabela com 2 digitos}. Exemplo: REQ_PROPOSTA_U01:
  - `builder.HasIndex(p => new { p.UnidadeId, p.TipoProcedimentoId, p.AnoProposta, p.MesProposta }).IsUnique().HasDatabaseName("REQ_PROPOSTA_U01");`
- Nomear Índices: NOME_TABELA_ + i + {Número do índice da tabela com 2 digitos}. Exemplo: 
  - `builder.HasIndex(b => b.NumeroCnpjCpf).HasDatabaseName("REQ_PESSOA_i01");`

Será importante usar os métodos para indicar se a propriedade/coluna pode ser nulas, se tem valor default, entre outras, como os itens a seguir:
- `.HasDefaultValue(true)`
- Indicar o tipo e tamanho, usando tipos no padrão SQL ANSI (para ser compatível com o SQLite dos testes). Exemplo: `.HasColumnType("varchar(25)")`
- **Observações:** 
  - Caso a coluna deva ser varchar(max), NÃO COLOQUE O MÉTODO `HasColumnType("varchar(max)")`, APENAS OMITA, deixe sem a chamada do método `HasColumnType()`, desta forma, a propriedade string será considerada como varchar(max). 
  - Ao criar os nomes conforme as regras citadas acima, a parte do nome correspondente ao nome da tabela deve ser o nome completo (NOME_TABELA => nome completo da tabela).
  - **Nomes de FKs**: O nome da FK deve ser declarado conforme a tabela em que possua a FK, e o nome deve ser repetido na outra configuration, caso tenha o mapeamento nos dois lados da relação. Exemplo: RequisicaoProtocoloConfiguration e RequisicaoPlanoOrcamentoConfiguration, onde RequisicaoPlanoOrcamento tem uma FK para RequisicaoProtocolo e ambas nomeam a FK com mesmo valor, REQ_REQUISICAO_PLANO_ORCAMENTO_R06:
     - RequisicaoPlanoOrcamentoConfiguration:
      ```chsharp
		builder.HasOne(r => r.RequisicaoProtocolo)
		           .WithOne(r => r.RequisicaoPlanoOrcamento)
		           .HasForeignKey<RequisicaoPlanoOrcamento>(r => r.NumeroProtocoloRequisicaoId)
                   .OnDelete(DeleteBehavior.NoAction)
                   .HasConstraintName("REQ_REQUISICAO_PLANO_ORCAMENTO_R06");
      ```

     - RequisicaoProtocoloConfiguration:
      ```chsharp
            builder.HasOne(r => r.RequisicaoPlanoOrcamento)
                   .WithOne(r => r.RequisicaoProtocolo)
                   .HasForeignKey<RequisicaoPlanoOrcamento>(r => r.NumeroProtocoloRequisicaoId)
                   .OnDelete(DeleteBehavior.NoAction)
                   .HasConstraintName("REQ_REQUISICAO_PLANO_ORCAMENTO_R06");
      ```


##Geração da classe de migration
- Crie migrations no Package Manager Console:
  - Startup Projet: `TRF3.SISPREC.Web`
  - Default Projet: `TRF3.SISPREC.EntityFrameworkCore`
  - Execute o comando: `Add-Migration {NomeDaMigrationEmPascalCase}`
    - O nome da migration deve ser em PascalCase conforme a [convenção da Microsoft](https://learn.microsoft.com/en-us/ef/core/managing-schemas/migrations/?tabs=dotnet-core-cli)
- **Importante:** 
  - Verifique o conteúdo da migration e o conteúdo alterado no SISPRECDbContextModelSnapshot, para garantir que a estrutura a ser alterada é a esperada. Exemplo: garantir que as FKs, índices, chaves únicas foram geradas corretamente. 
    - Também é possível checar se a estrutura do banco será alterada de forma correta por meio da análise de um script SQL gerado pelo comando (executar na raiz da solution):
      - `dotnet ef migrations script -o script.sql --project=.\src\TRF3.SISPREC.EntityFrameworkCore\TRF3.SISPREC.EntityFrameworkCore.csproj` 

        **Obs.:** Esse `script.sql` não deve ser commitado. É só para conferência mesmo.