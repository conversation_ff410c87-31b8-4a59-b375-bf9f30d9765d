# Breadcrumbs

## Introdução

`abp-breadcrumb` é o container principal para os itens de breadcrumb.

Uso básico:

````html
<abp-breadcrumb>
    <abp-breadcrumb-item href="#" title="Home" />
    <abp-breadcrumb-item href="#" title="Library"/>
    <abp-breadcrumb-item title="Page"/>
</abp-breadcrumb>
````

## Demonstração

Veja a [página de demonstração de breadcrumbs](https://bootstrap-taghelpers.abp.io/Components/Breadcrumbs) para vê-lo em ação.

## Atributos do `abp-breadcrumb-item`

-   **title**: Define o texto do item do breadcrumb.
-   **active**: Define o item de breadcrumb ativo. O último item está ativo por padrão, se nenhum outro item estiver ativo.
-   **href**: Um valor indica se um `abp-breadcrumb-item` possui um link. Deve ser um valor de link string.
