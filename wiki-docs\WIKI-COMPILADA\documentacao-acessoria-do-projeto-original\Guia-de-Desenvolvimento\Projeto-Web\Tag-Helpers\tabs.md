# Abas

## Introdução

`abp-tab` é o container básico de conteúdo de navegação por abas, derivado do elemento de abas do bootstrap.

Uso básico:

```xml
<abp-tabs>
    <abp-tab title="Home">
             Content_Home
    </abp-tab>
    <abp-tab-link title="Link" href="#" />
    <abp-tab title="profile">
            Content_Profile
    </abp-tab>
    <abp-tab-dropdown title="Contact" name="ContactDropdown">
        <abp-tab title="Contact 1" parent-dropdown-name="ContactDropdown">
            Content_1_Content
        </abp-tab>
        <abp-tab title="Contact 2" parent-dropdown-name="ContactDropdown">
            Content_2_Content
        </abp-tab>
    </abp-tab-dropdown>
</abp-tabs>
```

## Demonstração

Veja a [página de demonstração de abas](https://bootstrap-taghelpers.abp.io/Components/Tabs) para vê-las em ação.

## Atributos de `abp-tab`

-   **title**: Define o texto do menu da aba.
-   **name:** Define o atributo "id" dos elementos gerados. O valor padrão é um Guid. Não é necessário, a menos que as abas sejam alteradas ou modificadas com Jquery.
-   **active**: Define a aba ativa.

Exemplo:

```xml
<abp-tabs name="TabId">
    <abp-tab name="nav-home" title="Home">
        Content_Home
    </abp-tab>   
    <abp-tab name="nav-profile" active="true" title="profile">
        Content_Profile
    </abp-tab>
    <abp-tab name="nav-contact" title="Contact">
        Content_Contact
    </abp-tab>
</abp-tabs>
```

### Pills

Exemplo:

```xml
<abp-tabs tab-style="Pill">
    <abp-tab title="Home">
         Content_Home
    </abp-tab>
    <abp-tab title="profile">
         Content_Profile
    </abp-tab>
    <abp-tab title="Contact">
         Content_Contact
    </abp-tab>
</abp-tabs>
```

### Vertical

**vertical-header-size**: Define a largura da coluna dos cabeçalhos das abas.

Exemplo:

```xml
<abp-tabs tab-style="PillVertical" vertical-header-size="_2" >
    <abp-tab active="true" title="Home">
        Content_Home
    </abp-tab>   
    <abp-tab title="profile">
        Content_Profile
    </abp-tab>
    <abp-tab title="Contact">
        Content_Contact
    </abp-tab>
</abp-tabs>
```
