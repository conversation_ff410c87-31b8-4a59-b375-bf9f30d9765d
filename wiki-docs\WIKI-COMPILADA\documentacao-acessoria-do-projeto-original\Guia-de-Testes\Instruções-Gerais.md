[[_TOC_]]
# Instruções Gerais para Testes

Este guia fornece instruções gerais sobre como criar testes eficazes para aplicações ABP Framework 8. Para informações mais detalhadas sobre tipos específicos de testes, consulte as páginas correspondentes no guia de testes. Acesse o [Guia de Testes](/Guia-de-Testes) para mais informações.

## 1. Princípios Gerais

### 1.1 Não usar biblioteca Moq
- Caso encontre algum lugar usando a biblioteca Moq, tente refatorar os testes para usar NSubstitute em vez de Moq. Caso não consiga, entre em contato para buscarmos uma solução.
- Consulte a documentação [NSubstitute](https://nsubstitute.github.io/help.html) se necessário.

### 1.2 Padrão Arrange-Act-Assert
- Sempre coloque comentários de Arrange-Act-Assert nos métodos de teste para melhor organização e clareza.

## 2. Testes Unitários

### 2.1 Estrutura de Testes
- Organize os testes em projetos específicos:
  - Application Services: `test/TRF3.SISPREC.EntityFrameworkCore.Tests/EntityFrameworkCore/Applications/`
  - Domain Services: `TRF3.SISPREC.Domain.Tests`
  - Web: `test/TRF3.SISPREC.Web.Tests/Pages/`

### 2.2 Bibliotecas Utilizadas
- **xUnit**: Framework de testes
- **Shouldly**: Biblioteca para asserções mais legíveis e expressivas
- **NSubstitute**: Framework para criação de mocks
- **Bogus**: Biblioteca para geração de dados fictícios
- **SQLite**: Banco de dados em memória para testes de integração

### 2.3 Configuração do Ambiente
- Use uma classe base para testes (`BaseAppServiceTests<TStartupModule>`) que herda de `SISPRECTestBase<TStartupModule>`
- Use o método `WithUnitOfWorkAsync` para garantir que as operações de banco de dados sejam executadas dentro de uma transação
- **IMPORTANTE**: Sempre use `.Wait()` após o `WithUnitOfWorkAsync` no construtor para garantir que qualquer erro durante a inserção dos registros seja lançado imediatamente

### 2.4 Casos de Uso Comuns
- Teste operações CRUD (Create, Read, Update, Delete)
- Teste validações e exceções
- Teste consultas e filtros
- Teste regras de negócio complexas

### 2.5 Boas Práticas
- Use nomes descritivos para os testes, seguindo o padrão `Metodo_Cenario_ResultadoEsperado`
- Divida os testes em Arrange, Act e Assert
- Use comentários para explicar partes complexas
- Mantenha os testes focados e concisos
- Use Bogus com `Random.Hash()` para strings
- Use Shouldly para asserções mais claras
- Trate exceções de forma adequada

## 3. Testes de Application Services

### 3.1 Estrutura Básica
```csharp
public class MeuAppServiceTests : BaseAppServiceTests<SISPRECTestBaseModule>
{
    private readonly IMeuAppService _appService;
    private readonly IRepository<MinhaEntidade> _repository;

    public MeuAppServiceTests()
    {
        _appService = GetRequiredService<IMeuAppService>();
        _repository = GetRequiredService<IRepository<MinhaEntidade>>();
    }

    [Fact]
    public async Task Criar_Deve_Retornar_Sucesso()
    {
        // Arrange
        var input = new CriarMinhaEntidadeDto
        {
            Nome = "Teste",
            Descricao = "Descrição teste"
        };

        // Act
        var result = await _appService.CriarAsync(input);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBe(Guid.Empty);
    }
}
```



## 4. Testes de Domain Services

### 4.1 Estrutura Básica
```csharp
public class MeuDomainServiceTests : SISPRECDomainTestBase<SISPRECTestBaseModule>
{
    private readonly IRepository<MinhaEntidade> _repository;
    private readonly MeuDomainService _domainService;

    public MeuDomainServiceTests()
    {
        _repository = Substitute.For<IRepository<MinhaEntidade>>();
        _domainService = new MeuDomainService(_repository);
    }

    [Fact]
    public async Task Validar_Deve_Retornar_Erro_Quando_Dados_Invalidos()
    {
        // Arrange
        var input = new ValidarMinhaEntidadeDto
        {
            Nome = string.Empty
        };

        // Act & Assert
        await Should.ThrowAsync<ValidationException>(() => 
            _domainService.ValidarAsync(input));
    }
}
```

### 4.2 Ferramentas e Bibliotecas
- **NSubstitute**: Para criar mocks de dependências
- **Shouldly**: Para asserções mais legíveis
- **Bogus**: Para gerar dados de teste consistentes

### 4.3 Padrões de Testes
- Siga o padrão Arrange-Act-Assert (AAA)
- Use nomes descritivos para os testes, seguindo o padrão `Nome_Do_Metodo_Deve_Fazer_Algo_Quando_Algo`

### 4.4 Casos de Uso e Exemplos
- Teste validação de dados
- Teste regras de negócio
- Teste fluxos completos
- Teste tratamento de exceções
- Use `Compile()` para testar expressões lambda
- Use `When()` para configurar comportamentos condicionais em mocks

## 5. Testes Web

### 5.1 Estrutura de Testes
- Organize os testes no projeto `test/TRF3.SISPREC.Web.Tests/Pages/`
- Teste páginas Razor e modais

### 5.2 Testes de Páginas
- Teste a página de índice para verificar se ela carrega corretamente e contém os elementos essenciais
- Teste os modais de criação, edição e detalhes para verificar a estrutura, elementos e carregamento de dados

### 5.3 Exemplo de Teste de Página
```csharp
public class Index_Tests : SISPRECWebTestBase
{
    [Fact]
    public async Task Index_Page_Test()
    {
        // Arrange
        string url = "/SuaEntidade";

        // Act
        var response = await GetResponseAsync(url);
        var responseString = await GetResponseAsStringAsync(url);

        // Assert
        response.ShouldNotBeNull();
        responseString.ShouldNotBeNull();
        response.StatusCode.ShouldBe(System.Net.HttpStatusCode.OK);

        var htmlDocument = new HtmlDocument();
        htmlDocument.LoadHtml(responseString);

        var tableElement = htmlDocument.GetElementbyId("SuaEntidadeTable");
        tableElement.ShouldNotBeNull();
    }
}
```

### 5.4 Boas Práticas
- Use `Wait()` e `autoSave: true` ao inserir registros no construtor
- Use `WithUnitOfWorkAsync` para garantir que as operações de banco de dados sejam executadas dentro de uma transação
- Verifique pelo menos um campo preenchido para garantir que os dados foram carregados
- Use o mesmo objeto inserido no construtor para fazer as consultas
- Organize testes por funcionalidade
- Use constantes para dados comuns

## 6. Mocks, Carga de Dados e InlineData

### 6.1 Mocks
- Use `NSubstitute` para criar mocks de dependências
- Configure os retornos dos métodos mockados conforme necessário
- Use `Arg.Any<>()` para argumentos genéricos
- Use `Arg.Is<Expression<Func<T, bool>>>()` com `Compile()` para argumentos específicos em expressões lambda
- Use `Returns()` com múltiplos argumentos para simular diferentes retornos em chamadas idênticas

### 6.2 Carga de Dados
- Use a classe `SISPRECTestConsts` para acessar os IDs dos registros pré-carregados
- Consulte o arquivo `test\TRF3.SISPREC.EntityFrameworkCore.Tests\EntityFrameworkCore\Sql\CARGA_BASICA_SQLITE.sql` para entender a estrutura da carga de dados
- Evite gerar IDs aleatórios quando existir um registro adequado na carga básica

### 6.3 InlineData
- Use `[InlineData]` para parametrizar testes no xUnit, permitindo executar o mesmo teste com diferentes conjuntos de dados

### 6.4 MockQueryable
- Use `MockQueryable.NSubstitute` para simular `DbSet` e `IQueryable` em mocks de repositórios

## 7. Testes de Integração

### 7.1 Application Services
- Teste a integração entre diferentes serviços
- Verifique o comportamento do sistema como um todo

## 8. Testes Web

### 8.1 Testes de Interface
- Verifique a renderização correta das páginas

## 9. Boas Práticas Gerais

### 9.1 Organização
- Mantenha métodos auxiliares em região separada
- Documente cenários complexos

### 9.2 Manutenibilidade
- Use constantes para dados comuns
- Evite duplicação de código
- Mantenha os testes atualizados com as mudanças no código

### 9.3 Cobertura
- Verifique a cobertura de código
- Priorize testes para funcionalidades críticas

## 10. Referências

- [Documentação NSubstitute](https://nsubstitute.github.io/help.html)
- [Documentação xUnit](https://xunit.net/)
- [Documentação Shouldly](https://docs.shouldly.org/)
- [Documentação Bogus](https://github.com/bchavez/Bogus)