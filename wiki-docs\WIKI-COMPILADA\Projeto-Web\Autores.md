[[_TOC_]]

# Projeto Web - Autores

O projeto Web contém a interface de usuário (UI) implementada usando ASP.NET Core MVC com Razor Pages. Este projeto é responsável por fornecer a interface gráfica para interação com os usuários, incluindo páginas, modais, formulários e interações JavaScript.

## Estrutura do Projeto

```
TRF3.SISPREC.Web/
├── Pages/
│   └── Autores/                # Páginas relacionadas a Autores
│       ├── Index.cshtml        # Página principal (listagem)
│       ├── CreateModal.cshtml  # Modal de criação
│       ├── EditModal.cshtml    # Modal de edição
│       ├── DetalheModal.cshtml # Modal de detalhes
│       ├── index.css          # Estilos específicos
│       ├── index.js           # JavaScript da listagem
│       ├── CreateModal.js     # JavaScript do modal de criação
│       ├── EditModal.js       # JavaScript do modal de edição
│       └── ViewModels/        # ViewModels específicos
└── Menus/                     # Configuração de menus
```

## Páginas Razor

### Página de Listagem (Index.cshtml)

A página principal que exibe a lista de autores em uma grid DataTables com funcionalidades de paginação, ordenação e filtros.

```csharp
@page
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using TRF3.SISPREC.Web.Pages.Autores
@using TRF3.SISPREC.Web.Menus
@model IndexModel
@inject IPageLayout PageLayout
@{
    PageLayout.Content.Title = "Autor";
    PageLayout.Content.BreadCrumb.Add("Autores");
    PageLayout.Content.MenuItemName = SISPRECMenus.Autor;
}

@* 
    Página Razor para listagem e filtragem de Autores.
    Utiliza componentes do ABP Framework para criação de tabela e filtros.
    A página permite visualizar, filtrar e criar novos Autores.
*@
@section scripts
{
    <abp-script src="/Pages/Autores/index.js" />
    <abp-script src="/libs/jquery-mask-plugin/jquery.mask.js" />
}
@section styles
{
    <abp-style src="/Pages/Autores/index.css" />
}

<abp-card>
    @* Cabeçalho do card com título e botão de novo Autor *@
    <abp-card-header>
        <abp-row class="justify-content-between align-items-center">
            <abp-column>
                <a abp-collapse-id="AutorCollapse" class="text-secondary">Filtrar</a>
            </abp-column>
            <abp-column class="text-end">
                <abp-button id="NewAutorButton"
                            text="Novo"
                            icon="plus"
                            button-type="Primary" />
            </abp-column>
        </abp-row>
    </abp-card-header>

    @* Corpo do card com filtros e tabela *@
    <abp-card-body>
        @* Seção de filtros colapsável *@
        <abp-collapse-body id="AutorCollapse">
            <form abp-model="AutorFilter" id="AutorFilter" required-symbols="false" column-size="_3">
                <abp-row>
                    @* Filtro por ID do Autor *@
                    <abp-column size="_1">
                        <abp-input asp-for="AutorFilter.AutorId" />
                    </abp-column>
                    
                    @* Filtro por Nome *@
                    <abp-column size="_3">
                        <abp-input asp-for="AutorFilter.Nome" />
                    </abp-column>
                    
                    @* Filtro por Sobrenome *@
                    <abp-column size="_3">
                        <abp-input asp-for="AutorFilter.Sobrenome" />
                    </abp-column>
                    
                    @* Filtro por Gênero Biológico *@
                    <abp-column size="_2">
                        <abp-select asp-for="AutorFilter.GeneroBiologico"/>
                    </abp-column>
                    
                    @* Filtro por Telefone *@
                    <abp-column size="_3">
                        <abp-input asp-for="AutorFilter.Telefone" />
                    </abp-column>
                </abp-row>
            </form>
        </abp-collapse-body>

        @* Tabela de listagem de Autores *@
        <abp-table striped-rows="true" id="AutorTable" class="nowrap" />
    </abp-card-body>
</abp-card>

```

### Modal de Criação (CreateModal.cshtml)

Modal para criar um novo autor.

```csharp
@page
@using TRF3.SISPREC.Autores
@using TRF3.SISPREC.Enums
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal
@model TRF3.SISPREC.Web.Pages.Autores.CreateModalModel
@{
    Layout = null;
}

@* 
    <summary>
        Página Razor para criação de Autores via modal.
        Utiliza componentes do ABP Framework para formulário e validações.
        O formulário é submetido via AJAX e utiliza o ViewModel para binding dos dados.
    </summary>
*@
<form abp-model="ViewModel" id="NovoAutorForm" data-ajaxForm="true" asp-page="CreateModal">
    @* 
        <summary>
            Modal de tamanho ExtraLarge com scroll para melhor visualização dos campos.
            Utiliza componentes ABP para header, body e footer.
        </summary>
    *@
    <abp-modal scrollable="true" size="ExtraLarge" id="NovoAutorFormModal">
        <abp-modal-header title="Cadastrar Novo Autor"></abp-modal-header>
        <abp-modal-body>
            @* 
                <summary>
                    Linha com campos de Nome, Sobrenome e Gênero Biológico.
                    Utiliza componentes ABP para layout e inputs.
                </summary>
            *@
            <abp-row>
                <abp-column size="_4">
                    <abp-input asp-for="ViewModel.Nome" />
                </abp-column>
                <abp-column size="_4">
                    <abp-input asp-for="ViewModel.Sobrenome" placeholder="Digite o sobrenome" />
                </abp-column>
                <abp-column size="_4">
                    <label class="form-label" for="ViewModel_GeneroBiologico">Gênero Biológico</label>
                    <abp-radio asp-for="ViewModel.GeneroBiologico"
                                asp-items="@EnumExtensions.GetEnumSelectList<TRF3.SISPREC.Enums.EGeneroBiologico>()"
                                inline="true" />
                </abp-column>
            </abp-row>

            @* 
                <summary>
                    Linha com campos de CPF e Naturalidade.
                    Utiliza componentes ABP para layout e inputs.
                </summary>
            *@
            <abp-row>
                <abp-column size="_4">
                    <abp-input asp-for="ViewModel.Cpf" placeholder="999.999.999-99" />
                </abp-column>
                <abp-column size="_8">
                    <label class="form-label">Naturalidade</label>
                    <select class="form-control" maxlength="200" asp-for="ViewModel.MunicipioId"></select>
                </abp-column>
            </abp-row>

            @* 
                <summary>
                    Linha com campo de Biografia.
                    Utiliza componente ABP para input.
                </summary>
            *@
            <abp-row>
                <abp-column size="_12">
                    <abp-input asp-for="ViewModel.Biografia" />
                </abp-column>
            </abp-row>

            @* 
                <summary>
                    Linha com campos de Email, Telefone e Website.
                    Utiliza componentes ABP para layout e inputs.
                </summary>
            *@
            <abp-row>
                <abp-column size="_4">
                    <abp-input asp-for="ViewModel.Email" placeholder="<EMAIL>" />
                </abp-column>
                <abp-column size="_4">
                    <abp-input asp-for="ViewModel.Telefone" placeholder="(99) 99999-9999" />
                </abp-column>
                <abp-column size="_4">
                    <abp-input asp-for="ViewModel.Website" type="url" placeholder="https://www.exemplo.com" />
                </abp-column>
            </abp-row>
        </abp-modal-body>
        <abp-modal-footer>
            <abp-modal-footer buttons="@(AbpModalButtons.Cancel|AbpModalButtons.Save)"></abp-modal-footer>
        </abp-modal-footer>
    </abp-modal>
</form>

@* 
    <summary>
        Scripts necessários para funcionamento do modal:
        - jQuery Mask Plugin para máscaras de campos
        - Script específico da página com lógica JavaScript
    </summary>
*@
<abp-script src="/libs/jquery-mask-plugin/jquery.mask.js" />
<abp-script src="/Pages/Autores/CreateModal.js" />

```

### Modal de Edição (EditModal.cshtml)

Modal para editar um autor existente, similar ao modal de criação.

```csharp
@page
@using TRF3.SISPREC.Autores
@using TRF3.SISPREC.Enums
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal
@model TRF3.SISPREC.Web.Pages.Autores.EditModalModel
@{
    Layout = null;
}

@* 
    Página Razor para edição de Autores via modal.
    Utiliza componentes do ABP Framework para criação de formulário de edição.
    O formulário possui validações e máscaras para os campos.
*@
<form abp-model="ViewModel" id="EditarAutorForm" data-ajaxForm="true" asp-page="EditModal">
    <abp-modal scrollable="true" size="ExtraLarge" id="EditarAutorFormModal">
        <abp-modal-header title="Alterar Autor"></abp-modal-header>
        <abp-modal-body>
            @* Campo oculto para armazenar o ID do Autor *@
            <abp-input asp-for="Id" />

            @* 
                Linha com campos de Nome, Sobrenome e Gênero Biológico:
                - Nome: Campo de texto simples
                - Sobrenome: Campo de texto com placeholder
                - Gênero Biológico: Radio buttons com opções do enum
            *@
            <abp-row>
                <abp-column size="_4">
                    <abp-input asp-for="ViewModel.Nome" />
                </abp-column>
                <abp-column size="_4">
                    <abp-input asp-for="ViewModel.Sobrenome" placeholder="Digite o sobrenome" />
                </abp-column>
                <abp-column size="_4">
                    <label class="form-label" for="ViewModel_GeneroBiologico">Gênero Biológico</label>
                    <abp-radio asp-for="ViewModel.GeneroBiologico"
                                asp-items="@Model.ViewModel.GeneroBiologico.GetEnumSelectListForRadio<TRF3.SISPREC.Enums.EGeneroBiologico>()"
                                inline="true" />
                </abp-column>
            </abp-row>

            @* 
                Linha com campos de CPF e Naturalidade:
                - CPF: Campo de texto com máscara
                - Naturalidade: Select com busca de municípios
            *@
            <abp-row>
                <abp-column size="_4">
                    <abp-input asp-for="ViewModel.Cpf" placeholder="999.999.999-99" />
                </abp-column>
                <abp-column size="_8">
                    <label class="form-label">Naturalidade</label>
                    <select class="form-control" maxlength="200" asp-for="ViewModel.MunicipioId"></select>
                    <input type="hidden" id="ViewModel_Municipio" value='@(Model.ViewModel.Municipio == null ? "" : System.Text.Json.JsonSerializer.Serialize(Model.ViewModel.Municipio))' disabled/>
                </abp-column>
            </abp-row>

            @* 
                Linha com campo de Biografia:
                - Biografia: Campo de texto com múltiplas linhas
            *@
            <abp-row>
                <abp-column size="_12">
                    <abp-input asp-for="ViewModel.Biografia" />
                </abp-column>
            </abp-row>

            @* 
                Linha com campos de contato:
                - Email: Campo de texto com validação de email
                - Telefone: Campo de texto com máscara
                - Website: Campo de texto com validação de URL
            *@
            <abp-row>
                <abp-column size="_4">
                    <abp-input asp-for="ViewModel.Email" placeholder="<EMAIL>" />
                </abp-column>
                <abp-column size="_4">
                    <abp-input asp-for="ViewModel.Telefone" placeholder="(99) 99999-9999" />
                </abp-column>
                <abp-column size="_4">
                    <abp-input asp-for="ViewModel.Website" type="url" placeholder="https://www.exemplo.com" />
                </abp-column>
            </abp-row>
        </abp-modal-body>
        <abp-modal-footer buttons="@(AbpModalButtons.Cancel|AbpModalButtons.Save)"></abp-modal-footer>
    </abp-modal>
</form>

@* 
    Scripts utilizados na página:
    - jQuery Mask Plugin: Para máscaras de campos
    - EditModal.js: Script específico para a página
*@
<abp-script src="/libs/jquery-mask-plugin/jquery.mask.js" />
<abp-script src="/Pages/Autores/EditModal.js" />

```

### Modal de Detalhes (DetalheModal.cshtml)

Modal para visualizar os detalhes de um autor em modo somente leitura.

```csharp
@page
@using TRF3.SISPREC.Localization
@using TRF3.SISPREC.Autores
@using TRF3.SISPREC.Enums
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal
@model TRF3.SISPREC.Web.Pages.Autores.DetalheModalModel
@{
    Layout = null;
}

@* 
    <summary>
        Página Razor para visualização de detalhes de Autores via modal.
        Utiliza componentes do ABP Framework para exibição de informações.
        O modal possui duas abas: Detalhe e Histórico.
    </summary>
*@
<abp-modal scrollable="true" size="ExtraLarge" id="DetalheAutorModal">
    <abp-modal-header title="Detalhes do Autor"></abp-modal-header>
    <abp-modal-body>
        @* 
            <summary>
                Abas para organização das informações:
                - Detalhe: Exibe informações básicas do Autor
                - Histórico: Mostra o histórico de alterações
            </summary>
        *@
        <abp-tabs>
            @* 
                <summary>
                    Aba de Detalhes:
                    Exibe informações básicas do Autor em formato de formulário desabilitado.
                    Utiliza componentes ABP para layout responsivo.
                </summary>
            *@
            <abp-tab title="Detalhe">
                <abp-row>
                    <abp-column size="_1">
                        <abp-input asp-for="ViewModel.AutorId" disabled="true" />
                    </abp-column>
                    <abp-column size="_3">
                        <abp-input asp-for="ViewModel.Nome" disabled="true" />
                    </abp-column>
                    <abp-column size="_4">
                        <abp-input asp-for="ViewModel.Sobrenome" disabled="true" />
                    </abp-column>
                    <abp-column size="_4">
                        <abp-input asp-for="ViewModel.GeneroBiologico" disabled="true"  />
                    </abp-column>
                </abp-row>

                <abp-row>
                    <abp-column size="_4">
                        <abp-input asp-for="ViewModel.Cpf" disabled="true" />
                    </abp-column>
                    <abp-column size="_8">
                        <abp-input asp-for="ViewModel.Municipio" label="Naturalidade" disabled="true" />
                    </abp-column>
                </abp-row>

                <abp-row>
                    <abp-column size="_12">
                        <abp-input asp-for="ViewModel.Biografia" disabled="true" />
                    </abp-column>
                </abp-row>

                <abp-row>
                    <abp-column size="_4">
                        <abp-input asp-for="ViewModel.Email" disabled="true" />
                    </abp-column>
                    <abp-column size="_4">
                        <abp-input asp-for="ViewModel.Telefone" disabled="true" />
                    </abp-column>
                    <abp-column size="_4">
                        <abp-input asp-for="ViewModel.Website" disabled="true" />
                    </abp-column>
                </abp-row>
            </abp-tab>

            @* 
                <summary>
                    Aba de Histórico:
                    Exibe o histórico de alterações do Autor.
                    Utiliza componente específico para histórico.
                </summary>
            *@
            <abp-tab title="Histórico">
                @await Component.InvokeAsync("Historico", new { nomeEntidade = typeof(Autor).FullName!, idEntidade = Model.ViewModel.AutorId.ToString() })
            </abp-tab>
        </abp-tabs>
    </abp-modal-body>
    <abp-modal-footer buttons="@(AbpModalButtons.Close)"></abp-modal-footer>
</abp-modal>

```

## JavaScript

### Configuração do DataTables (index.js)

Configuração da grid DataTables com suporte a paginação server-side, ordenação, filtros e ações CRUD.

```javascript
$(function () {
    // Configura o evento de input para atualizar a tabela ao filtrar
    $("#AutorFilter :input").on('input', function () {
        dataTable.ajax.reload();
    });

    // Função para obter os valores dos filtros
    const getFilter = function () {
        const input = {};
        $("#AutorFilter")
            .serializeArray()
            .forEach(function (data) {
                if (data.value != '') {
                    input[abp.utils.toCamelCase(data.name.replace(/AutorFilter./g, ''))] = data.value;
                }
            })
        return input;
    };

    // Inicializa os serviços e modais
    const service = tRF3.sISPREC.autores.autor;
    const detalheModal = new abp.ModalManager(abp.appPath + 'Autores/DetalheModal');
    const createModal = new abp.ModalManager(abp.appPath + 'Autores/CreateModal');
    const editModal = new abp.ModalManager(abp.appPath + 'Autores/EditModal');

    // Configuração da tabela DataTables
    const dataTable = $('#AutorTable').DataTable(abp.libs.datatables.normalizeConfiguration({
        processing: true,
        serverSide: true,
        paging: true,
        searching: false, // Desabilita a busca padrão
        autoWidth: false,
        scrollCollapse: true,
        order: [[1, "asc"]],
        ajax: abp.libs.datatables.createAjax(service.getList, getFilter),
        columnDefs: [
            {
                rowAction: {
                    items: [
                        {
                            text: "Detalhe",
                            action: function (data) {
                                detalheModal.open({ id: data.record.autorId });
                            }
                        },
                        {
                            text: "Alterar",
                            action: function (data) {
                                editModal.open({ id: data.record.autorId });
                            }
                        },
                        {
                            text: "Excluir",
                            confirmMessage: function (data) {
                                return "Tem certeza de que deseja excluir?"
                            },
                            action: function (data) {
                                abp.ui.block({ elm: 'body', busy: true });
                                service.delete(data.record.autorId)
                                    .then(function () {
                                        abp.notify.info('Registro excluído com sucesso!');
                                        dataTable.ajax.reload();
                                    }).always(function () { abp.ui.unblock(); });
                            }
                        }
                    ]
                },
                width: '0.1%',
                sorting: false
            },
            {
                title: "Id",
                data: "autorId",
                width: '5%'
            },
            {
                title: "Nome",
                data: "nome",
                width: '15%'
            },
            {
                title: "Sobrenome",
                data: "sobrenome",
                width: '15%'
            },
            {
                title: "Gênero Biológico",
                data: "generoBiologico",
                width: '10%'
            },
            {
                title: "CPF",
                data: "cpf",
                width: '10%'
            },
            {
                title: "Email",
                data: "email",
                width: '10%'
            },
            {
                title: "Telefone",
                data: "telefone",
                width: '10%'
            },
            {
                title: "Naturalidade",
                data: "municipio.nome",
                width: '20%'
            },
        ]
    }));

    // Atualiza a tabela após criar um novo autor
    createModal.onResult(function () {
        dataTable.ajax.reload();
    });

    // Atualiza a tabela após editar um autor
    editModal.onResult(function () {
        dataTable.ajax.reload();
    });

    // Configura o botão de novo autor
    $('#NewAutorButton').click(function (e) {
        e.preventDefault();
        createModal.open();
    });

    // Aplica máscara ao campo de telefone
    $("#AutorFilter_Telefone").mask("(99) 99999-9999");
});

```

## Configuração de Menu

Configuração do item de menu para a página de autores.

```csharp
public class SISPRECMenuContributor : IMenuContributor
{
    public async Task ConfigureMenuAsync(MenuConfigurationContext context)
    {
        if (context.Menu.Name == StandardMenus.Main)
        {
            await ConfigureMainMenuAsync(context);
        }
    }

    private Task ConfigureMainMenuAsync(MenuConfigurationContext context)
    {
        var administration = context.Menu.GetAdministration();

        administration.AddItem(
            new ApplicationMenuItem(
                SISPRECMenus.Autor,
                "Autores",
                "/Autores",
                icon: "fas fa-users"
            )
        );

        return Task.CompletedTask;
    }
}
```

### CreateModal.js

 Script de inicialização e configuração do modal de criação de Autores.
 Responsável por:
    - Configurar o componente Select2 para seleção de municípios
    - Aplicar máscaras aos campos de CPF e telefone
    - Integrar com API de cidades via AJAX


```javascript
$(function () {
    /**
     * Configuração do Select2 para campo de município.
     * Utiliza AJAX para buscar cidades a partir de 3 caracteres digitados.
     * Integra com API de cidades do sistema.
     * 
     *  - dropdownParent: Define o elemento pai do dropdown do Select2, garantindo que ele seja exibido corretamente dentro do modal.
     *  - theme: Define o tema do Select2 como 'bootstrap-5' para compatibilidade com o Bootstrap 5.
     *  - minimumInputLength: Define o número mínimo de caracteres que o usuário deve digitar para iniciar a busca de cidades.
     *  - placeholder: Define o texto exibido no campo Select2 quando nenhuma cidade está selecionada.
     *  - allowClear: Permite que o usuário limpe a seleção de cidade.
     *  - ajax: Configura a busca de cidades via AJAX.
     *      - url: Define a URL da API para buscar cidades.
     *      - dataType: Define o tipo de dados esperado da API como JSON.
     *      - data: Função que define os parâmetros da requisição AJAX.
     *      - processResults: Função que processa os resultados da API para o formato esperado pelo Select2.
     */
    $('#ViewModel_MunicipioId').select2({
        dropdownParent: $('#NovoAutorFormModal'),
        theme: 'bootstrap-5',
        minimumInputLength: 3,
        placeholder: "Selecione uma cidade...",
        allowClear: true,
        ajax: {
            url: abp.appPath + 'api/app/endereco/cidade-por-nome',
            dataType: 'json',
            data: function (params) {
                let query = {
                    nomeCidade: params.term
                };
                return query;
            },
            processResults: function (data) {
                let result = $.map(data, function (obj) {
                    obj.id = obj.municipioId;
                    obj.text = obj.nome;
                    return obj;
                });

                return {
                    results: result
                };
            }
        }
    });

    /**
     * Aplica máscara para campo de CPF.
     * Utiliza o plugin jQuery Mask para formatar o campo de CPF.
     */
    $("#ViewModel_Cpf").mask("999.999.999-99");

    /**
     * Aplica máscara para campo de telefone.
     * Utiliza o plugin jQuery Mask para formatar o campo de telefone.
     */
    $("#ViewModel_Telefone").mask("(99) 99999-9999");
});
```

### EditModal.js

Script de inicialização e configuração do modal de edição de Autores.
Responsável por:
    - Configurar o componente Select2 para seleção de municípios
    - Aplicar máscaras aos campos de CPF e telefone
    - Recarregar a página após fechar o modal

```javascript

$(function () {
    // Obtém o valor do município do campo hidden
    const municipioVal = $('#ViewModel_Municipio').val();
    console.log('municipioVal', municipioVal)
    
    // Converte o valor para JSON
    const municipioJson = JSON.parse(municipioVal || '{}');
    console.log('municipioJson', municipioJson)

    // Configura as opções iniciais do select2
    const options = [{
        id: municipioJson.MunicipioId,
        text: municipioJson.Nome,
        selected: true
    }];
    console.log('options', options)

    // Inicializa o select2 para seleção de municípios
    $('#ViewModel_MunicipioId').select2({
        dropdownParent: $('#EditarAutorFormModal'), // Define o modal como parent
        theme: 'bootstrap-5', // Usa tema do Bootstrap 5
        minimumInputLength: 3, // Mínimo de 3 caracteres para busca
        placeholder: "Selecione uma cidade...", // Placeholder do campo
        allowClear: true, // Permite limpar a seleção
        data: options, // Opções iniciais
        ajax: {
            url: abp.appPath + 'api/app/endereco/cidade-por-nome', // Endpoint para busca
            dataType: 'json',
            data: function (params) {
                let query = {
                    nomeCidade: params.term // Termo de busca
                };
                return query;
            },
            processResults: function (data) {
                // Mapeia os resultados para o formato esperado pelo select2
                let result = $.map(data, function (obj) {
                    obj.id = obj.municipioId;
                    obj.text = obj.nome;
                    return obj;
                });

                return {
                    results: result
                };
            }
        }
    });

    // Define o valor inicial do select2
    $('#ViewModel_MunicipioId').val(municipioJson.MunicipioId).trigger('change');

    // Adiciona evento para recarregar a página quando o modal for fechado
    $('#EditarAutorFormModal').on('hidden.bs.modal', function () {
        window.location.reload();
    });

    // Aplica máscaras aos campos de CPF e telefone
    $("#ViewModel_Cpf").mask("999.999.999-99");
    $("#ViewModel_Telefone").mask("(99) 99999-9999");
});
```

## Code-Behind das Páginas Razor

### Página de Listagem (Index.cshtml.cs)

A página principal que gerencia a listagem de autores. Responsável por:
    - Definir a ViewModel usada no formulário de filtro do grid

```csharp
using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Autores;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;

namespace TRF3.SISPREC.Web.Pages.Autores;

/// <summary>
/// Classe de code-behind para a página de listagem de Autores.
/// Gerencia a lógica de exibição e filtragem de Autores.
/// Herda de SISPRECPageModel para compartilhar funcionalidades comuns.
/// </summary>
public class IndexModel : SISPRECPageModel
{
    /// <summary>
    /// Modelo de filtro para a listagem de Autores.
    /// Contém os campos disponíveis para filtragem na interface.
    /// </summary>
    public AutorFilterInput AutorFilter { get; set; } = new();

    /// <summary>
    /// Método executado ao carregar a página.
    /// Pode ser usado para inicializar dados ou realizar operações assíncronas.
    /// </summary>
    public virtual async Task OnGetAsync()
    {
        await Task.CompletedTask;
    }
}

/// <summary>
/// Classe que define os campos disponíveis para filtragem de Autores.
/// Cada propriedade corresponde a um campo na interface de filtros.
/// </summary>
public class AutorFilterInput
{
    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Id")]
    public int? AutorId { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Nome")]
    public string? Nome { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Sobrenome")]
    public string? Sobrenome { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Gênero Biológico")]
    public TRF3.SISPREC.Enums.EGeneroBiologico? GeneroBiologico { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Biografia")]
    public string? Biografia { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Email")]
    public string? Email { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Telefone")]
    public string? Telefone { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "CPF")]
    public string? Cpf { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Website")]
    public string? Website { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Naturalidade")]
    public int? MunicipioId { get; set; }
}

```

### Modal de Criação (CreateModal.cshtml.cs)

Modal para criar um novo autor. Responsável por:
- Binding do formulário com o ViewModel
- Acionar a validação da ViewModel de criação
- Mapeamento entre ViewModel e DTO
- Consumir os métodos do AutorAppService

```csharp
using Microsoft.AspNetCore.Mvc;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Autores;
using TRF3.SISPREC.Autores.Dtos;
using TRF3.SISPREC.Web.Pages.Autores.ViewModels;

namespace TRF3.SISPREC.Web.Pages.Autores;

/// <summary>
/// Classe de code-behind para a página de criação de Autores.
/// Gerencia a lógica de criação de Autores via modal, integrando-se com o ABP Framework.
/// Herda de SISPRECPageModel para compartilhar funcionalidades comuns.
/// </summary>
public class CreateModalModel : SISPRECPageModel
{
    /// <summary>
    /// ViewModel utilizado para binding com o formulário de criação.
    /// Mapeado automaticamente para CreateUpdateAutorDto durante o post.
    /// </summary>
    [BindProperty]
    public CreateEditAutorViewModel ViewModel { get; set; } = new();

    private readonly IAutorAppService _service;

    public CreateModalModel(IAutorAppService service)
    {
        _service = service;
    }

    /// <summary>
    /// Método que processa o post do formulário de criação de Autor.
    /// Realiza validações, mapeia o ViewModel para DTO e chama o serviço de aplicação.
    /// Excluído da cobertura de testes devido a limitações na infraestrutura de testes.
    /// </summary>
    /// <returns>
    /// Retorna NoContent() para indicar sucesso na criação.
    /// O modal é fechado automaticamente pelo JavaScript após o post.
    /// </returns>
    [ExcludeFromCodeCoverage]
    public virtual async Task<IActionResult> OnPostAsync()
    {
        // Valida o model com base nas Data Annotations e IValidatableObject
        ValidateModel();
        // Mapeia o ViewModel para o DTO usando o ObjectMapper do ABP Framework
        var dto = ObjectMapper.Map<CreateEditAutorViewModel, CreateUpdateAutorDto>(ViewModel);
        // Chama o serviço de aplicação para criar o Autor
        await _service.CreateAsync(dto);
        // Retorna NoContent para indicar sucesso
        return NoContent();
    }
}
```

### Modal de Edição (EditModal.cshtml.cs)

Modal para editar um autor existente. Responsável por:
- Receber o ID do autor via query string
- Carregar os dados do autor para edição
- Binding do formulário com o ViewModel
- Validação do modelo
- Mapeamento entre ViewModel e DTO
- Comunicação com o AutorAppService

```csharp
using Microsoft.AspNetCore.Mvc;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Autores;
using TRF3.SISPREC.Autores.Dtos;
using TRF3.SISPREC.Web.Pages.Autores.ViewModels;

namespace TRF3.SISPREC.Web.Pages.Autores;

/// <summary>
/// Classe de code-behind para a página de edição de Autores.
/// Gerencia a lógica de edição de Autores via modal, integrando-se com o ABP Framework.
/// Herda de SISPRECPageModel para compartilhar funcionalidades comuns.
/// </summary>
public class EditModalModel : SISPRECPageModel
{
    /// <summary>
    /// ID do Autor obtido via parâmetro de rota.
    /// Utilizado para buscar os dados do Autor no serviço de aplicação.
    /// </summary>
    [HiddenInput]
    [BindProperty(SupportsGet = true)]
    public int Id { get; set; }

    /// <summary>
    /// ViewModel utilizado para edição dos dados do Autor.
    /// Mapeado automaticamente a partir do AutorDto retornado pelo serviço.
    /// </summary>
    [BindProperty]
    public CreateEditAutorViewModel ViewModel { get; set; } = new();

    private readonly IAutorAppService _service;

    public EditModalModel(IAutorAppService service)
    {
        _service = service;
    }

    /// <summary>
    /// Método que carrega os dados do Autor ao abrir o modal de edição.
    /// Busca os dados no serviço de aplicação e mapeia para o ViewModel.
    /// </summary>
    public virtual async Task OnGetAsync()
    {
        var dto = await _service.GetAsync(Id);
        ViewModel = ObjectMapper.Map<AutorDto, CreateEditAutorViewModel>(dto);
    }

    /// <summary>
    /// Método que processa a submissão do formulário de edição.
    /// Valida o modelo, mapeia os dados para DTO e atualiza no serviço.
    /// Retorna NoContent para fechar o modal após a atualização.
    /// </summary>
    //Não há infraestrutura ainda para testar métodos post que gere relatório de cobertura no VS2022 para sonarqube
    [ExcludeFromCodeCoverage]
    public virtual async Task<IActionResult> OnPostAsync()
    {
        ValidateModel();
        var dto = ObjectMapper.Map<CreateEditAutorViewModel, CreateUpdateAutorDto>(ViewModel);
        await _service.UpdateAsync(Id, dto);
        return NoContent();
    }
}
```

### Modal de Detalhes (DetalheModal.cshtml.cs)

Modal para visualizar os detalhes de um autor. Responsável por:
- Receber o ID do autor via query string
- Carregar os dados do autor através do AutorAppService
- Mapear o DTO para o ViewModel de detalhes

```csharp
using Microsoft.AspNetCore.Mvc;
using TRF3.SISPREC.Autores;
using TRF3.SISPREC.Autores.Dtos;
using TRF3.SISPREC.Web.Pages.Autores.ViewModels;

namespace TRF3.SISPREC.Web.Pages.Autores;

/// <summary>
/// Classe de code-behind para a página de detalhes de Autores.
/// Gerencia a lógica de exibição de detalhes de Autores via modal, integrando-se com o ABP Framework.
/// Herda de SISPRECPageModel para compartilhar funcionalidades comuns.
/// </summary>
public class DetalheModalModel : SISPRECPageModel
{
    /// <summary>
    /// ID do Autor obtido via parâmetro de rota.
    /// Utilizado para buscar os detalhes do Autor no serviço de aplicação.
    /// </summary>
    [HiddenInput]
    [BindProperty(SupportsGet = true)]
    public int Id { get; set; }

    /// <summary>
    /// ViewModel utilizado para exibição dos detalhes do Autor.
    /// Mapeado automaticamente a partir do AutorDto retornado pelo serviço.
    /// </summary>
    [BindProperty]
    public DetalheAutorViewModel ViewModel { get; set; } = new();

    private readonly IAutorAppService _service;

    public DetalheModalModel(IAutorAppService service)
    {
        _service = service;
    }

    /// <summary>
    /// Método que carrega os dados do Autor ao abrir o modal.
    /// Busca os dados no serviço de aplicação e mapeia para o ViewModel.
    /// </summary>
    /// <remarks>
    /// Fluxo de execução:
    /// 1. Recebe o ID do Autor via parâmetro de rota
    /// 2. Busca os dados do Autor no serviço de aplicação
    /// 3. Mapeia os dados do DTO para o ViewModel
    /// 4. Retorna os dados para a view
    /// </remarks>
    public virtual async Task OnGetAsync()
    {
        // Busca os dados do Autor no serviço de aplicação
        var dto = await _service.GetAsync(Id);

        // Mapeia os dados do DTO para o ViewModel
        ViewModel = ObjectMapper.Map<AutorDto, DetalheAutorViewModel>(dto);
    }
}
```

## ViewModels

Os ViewModels são classes que representam os dados necessários para as views. No caso dos autores, temos dois ViewModels principais:

### CreateEditAutorViewModel

Este ViewModel é compartilhado entre os modais de criação e edição de autores. Ele contém todas as propriedades necessárias para o formulário, com suas respectivas validações e atributos de exibição.

Características principais:
- Usa Data Annotations para validação client e server-side
- Inclui placeholders e mensagens de erro amigáveis
- Suporta campos obrigatórios e opcionais
- Validações específicas para email, telefone, CPF e website
- Integração com Select2 para seleção de município

```csharp
/// <summary>
/// Classe que representa o modelo de criação/edição de Autores.
/// </summary>
public class CreateEditAutorViewModel
{
    [Placeholder("Digite o nome do autor")]
    [Required(ErrorMessage = "O Nome é obrigatório")]
    [StringLength(AutorConsts.NOME_TAMANHO_MAX)]
    [Display(Name = "Nome")]
    public string Nome { get; set; }

    [Required(ErrorMessage = "O Sobrenome é obrigatório")]
    [StringLength(AutorConsts.SOBRENOME_TAMANHO_MAX)]
    [Display(Name = "Sobrenome")]
    public string Sobrenome { get; set; }

    [Required(ErrorMessage = "O Gênero Biológico é obrigatório")]
    [Display(Name = "Gênero Biológico")]
    public EGeneroBiologico GeneroBiologico { get; set; }

    [StringLength(AutorConsts.BIOGRAFIA_TAMANHO_MAX)]
    [Display(Name = "Biografia")]
    [TextArea]
    public string? Biografia { get; set; }

    [EmailAddress(ErrorMessage = "Email em formato inválido")]
    [StringLength(AutorConsts.EMAIL_TAMANHO_MAX)]
    [Display(Name = "Email")]
    public string? Email { get; set; }

    [Phone(ErrorMessage = "Telefone em formato inválido")]
    [StringLength(AutorConsts.TELEFONE_TAMANHO_MAX)]
    [Display(Name = "Telefone")]
    public string? Telefone { get; set; }

    [RegularExpression(@"^\d{3}\.?\d{3}\.?\d{3}\-?\d{2}$")]
    [StringLength(AutorConsts.CPF_TAMANHO_MAX)]
    [Display(Name = "CPF")]
    public string? Cpf { get; set; }

    [Url(ErrorMessage = "Website em formato inválido")]
    [StringLength(AutorConsts.WEBSITE_TAMANHO_MAX)]
    [Display(Name = "Website")]
    public string? Website { get; set; }

    [Display(Name = "Naturalidade")]
    public int? MunicipioId { get; set; }

    public MunicipioDto? Municipio { get; set; }
}
```

### DetalheAutorViewModel

Este ViewModel é usado para exibição dos detalhes de um autor na modal de detalhes. Similar ao CreateEditAutorViewModel, mas sem validações pois é somente leitura.

Características principais:
- Usa Data Annotations apenas para exibição (Display)
- Inclui propriedade calculada para nome completo
- Formatação amigável para exibição de dados
- Suporte a campos opcionais

```csharp
/// <summary>
/// Classe que representa o modelo de visualização de detalhes de Autores.
/// </summary>
public class DetalheAutorViewModel
{
    [Display(Name = "Id")]
    public int AutorId { get; set; }

    [Display(Name = "Nome")]
    public string Nome { get; set; }

    [Display(Name = "Sobrenome")]
    public string Sobrenome { get; set; }

    [Display(Name = "Gênero Biológico")]
    public EGeneroBiologico GeneroBiologico { get; set; }

    [Display(Name = "Biografia")]
    public string? Biografia { get; set; }

    [Display(Name = "Email")]
    public string? Email { get; set; }

    [Display(Name = "Telefone")]
    public string? Telefone { get; set; }

    [Display(Name = "CPF")]
    public string? Cpf { get; set; }

    [Display(Name = "Website")]
    public string? Website { get; set; }

    [Display(Name = "Naturalidade")]
    public string Municipio { get; set; }
}
```

## Mapeamento - AutoMapper

Configuração dos mapeamentos entre DTOs e ViewModels.
Caso alguma propriedade do DTO não seja mapeada, o AutoMapper irá gerar um erro de mapeamento.
Para mapeamentos mais complexos, é possível usar expressões lambda ou métodos personalizados. Como no exemplo abaixo, onde é feito um mapeamento personalizado para o campo "Municipio".

```csharp
public class SISPRECWebAutoMapperProfile : Profile
{
    public SISPRECWebAutoMapperProfile()
    {
        //Mapeamento de Autor
        CreateMap<AutorDto, CreateEditAutorViewModel>();
        CreateMap<CreateUpdateAutorDto, CreateEditAutorViewModel>().ReverseMap();
        CreateMap<AutorDto, DetalheAutorViewModel>().ForMember(dest => dest.Municipio, opt => opt.MapFrom(src => src.Municipio.Nome));
    }
}
```

## Boas Práticas

1. **Validações**
   - Use Data Annotations nos ViewModels
   - Implemente IValidatableObject para validações complexas
   - Mantenha validações de negócio na camada de domínio, como validações que precisam de verificar registros no banco de dados, ou que dependem de regras de negócio complexas.
   - As validações no ViewModel são para garantir que os dados estão corretos NA FORMA/FORMATAÇÂO, e não necessariamente que são válidos para o negócio.

2. **JavaScript**
   - Organize código por funcionalidade
   - Use módulos para evitar poluição do escopo global
   - Aproveite as bibliotecas fornecidas pelo ABP

3. **Modais**
   - Use o sistema de modais do ABP

4. **Formulários**
   - Aproveite o sistema de formulários dinâmicos quando possível. Veja mais em: [Formulários Dinâmicos](/ABP-Docs/Razor-Pages-Frontend/Tag-Helpers/dynamic-forms.md)
   - Implemente validação client-side quando necessário

5. **Grids**
   - Use paginação server-side
   - Implemente filtros relevantes
   - Otimize consultas no backend



**[Próximo: Testes Domain e AppService](/Tutorial-de-Início/Testes)**