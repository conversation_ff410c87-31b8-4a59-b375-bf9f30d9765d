# Domain.Shared

O projeto Domain.Shared contém tipos compartilhados entre todas as camadas do projeto. Aqui são definidas constantes, enums e outros objetos que precisam ser acessíveis por todas as camadas.

## Estrutura do Projeto

O projeto Domain.Shared está organizado em namespaces que agrupam os tipos relacionados:

```
src/TRF3.SISPREC.Domain.Shared/
├── Autores/
│   └── AutorConsts.cs
├── Livros/
│   └── LivroConsts.cs
└── Enums/
    ├── EGeneroBiologico.cs
    └── ECategoriaLivro.cs
```

## Constantes

As constantes definem valores que são utilizados em várias partes do sistema, como tamanhos máximos de campos. Estas constantes são usadas tanto na configuração do Entity Framework Core quanto nas validações dos ViewModels.

AutorConsts.cs
```csharp
using System.ComponentModel;

namespace TRF3.SISPREC.Autores;

/// <summary>
/// Classe AutorConsts no namespace TRF3.SISPREC.Autores.
/// Contém constantes que definem tamanhos máximos para propriedades de Autor.
/// Estes valores são utilizados tanto na configuração do EF Core (AutorConfiguration)
/// quanto nas validações dos ViewModels relacionados a autores.
/// </summary>
public class AutorConsts
{
    /// <summary>
    /// Tamanho máximo para o nome do autor (usado em configurações e validações)
    /// </summary>
    public const int NOME_TAMANHO_MAX = 150;

    /// <summary>
    /// Tamanho máximo para o sobrenome do autor (usado em configurações e validações)
    /// </summary>
    public const int SOBRENOME_TAMANHO_MAX = 150;

    /// <summary>
    /// Tamanho máximo para a biografia do autor (usado em configurações e validações)
    /// </summary>
    public const int BIOGRAFIA_TAMANHO_MAX = 2000;

    /// <summary>
    /// Tamanho máximo para o email do autor (usado em configurações e validações)
    /// </summary>
    public const int EMAIL_TAMANHO_MAX = 50;

    /// <summary>
    /// Tamanho máximo para o telefone do autor (usado em configurações e validações)
    /// </summary>
    public const int TELEFONE_TAMANHO_MAX = 20;

    /// <summary>
    /// Tamanho máximo para o website do autor (usado em configurações e validações)
    /// </summary>
    public const int WEBSITE_TAMANHO_MAX = 250;

    /// <summary>
    /// Tamanho máximo para o gênero do autor (usado em configurações e validações)
    /// </summary>
    public const int GENERO_TAMANHO_MAX = 50;

    /// <summary>
    /// Tamanho máximo para o CPF do autor (usado em configurações e validações)
    /// </summary>
    public const int CPF_TAMANHO_MAX = 500;
}
```
LivroConsts.cs
```csharp
namespace TRF3.SISPREC.Livros;

/// <summary>
/// Classe LivroConsts no namespace TRF3.SISPREC.Livros.
/// Diretório relativo: src/TRF3.SISPREC.Domain.Shared/Livros
/// 
/// Contém constantes que definem tamanhos máximos para propriedades de Livro.
/// Estes valores são utilizados tanto na configuração do EF Core (LivroConfiguration)
/// quanto nas validações dos ViewModels relacionados a livros.
/// </summary>
public class LivroConsts
{
    /// <summary>
    /// Tamanho máximo para o título do livro (usado em configurações e validações)
    /// </summary>
    public const int TITULO_TAMANHO_MAX = 250;

    /// <summary>
    /// Tamanho máximo para a categoria do livro (usado em configurações e validações)
    /// </summary>
    public const int CATEGORIA_TAMANHO_MAX = 100;

    /// <summary>
    /// Tamanho máximo para a descrição do livro (usado em configurações e validações)
    /// </summary>
    public const int DESCRICAO_TAMANHO_MAX = 1000;
}
```

## Enums

Os enums definem conjuntos de valores possíveis para determinadas propriedades. Utilizamos o atributo `Description` para fornecer uma exibição amigável na interface do usuário.

EGeneroBiologico.cs
```csharp
using System.ComponentModel;

namespace TRF3.SISPREC.Enums;

/// <summary>
/// Enum EGeneroBiologico no namespace TRF3.SISPREC.Enums.
/// Define as opções de gênero biológico disponíveis para autores,
/// utilizando attributes de Description para exibição amigável na UI.
/// </summary>
public enum EGeneroBiologico
{
    /// <summary>
    /// Representa o gênero biológico feminino
    /// </summary>
    [Description("Feminino")]
    FEMININO,

    /// <summary>
    /// Representa o gênero biológico masculino
    /// </summary>
    [Description("Masculino")]
    MASCULINO
}
```

ECategoriaLivro.cs
```csharp
using System.ComponentModel;

namespace TRF3.SISPREC.Enums;

/// <summary>
/// Enumeração que representa as categorias de livros.
/// Diretório: src/TRF3.SISPREC.Domain.Shared/Enums/ECategoriaLivro.cs
/// Namespace: TRF3.SISPREC.Enums
/// </summary>
public enum ECategoriaLivro
{
    [Description("Infantil")]
    INFANTIL,

    [Description("Acadêmicos")]
    ACADEMICO,

    [Description("Ficção")]
    FICCAO,

    [Description("Romance")]
    ROMANCE,

    [Description("Terror")]
    TERROR,

    [Description("Aventura")]
    AVENTURA,

    [Description("Biografias")]
    BIOGRAFIA,

    [Description("Tecnologia")]
    TECNOLOGIA
}
```

## Boas Práticas

1. **Organização do Código**
   - Agrupe tipos relacionados pastas por entidades, com namespace no padrão ABP Framework. Exemplo: `TRF3.SISPREC.Autores`
   - Mantenha um arquivo por tipo (classe, enum, etc.)
   - Use nomes claros e descritivos

2. **Documentação**
   - Use XML comments para documentar todos os tipos e membros
   - Inclua exemplos de uso quando relevante
   - Mantenha a documentação atualizada

3. **Constantes**
   - Defina constantes no Domain.Shared para reuso
   - Use nomes descritivos e auto-explicativos
   - Agrupe constantes relacionadas em classes específicas

4. **Enums**
   - Use o atributo Description para exibição amigável
   - Mantenha valores em MAIÚSCULAS por convenção

5. **Compartilhamento**
   - Coloque no Domain.Shared apenas o que realmente precisa ser compartilhado
   - Evite dependências desnecessárias
   - Mantenha o projeto o mais simples possível

**[Próximo: Domain](/Tutorial-de-Início/Projeto-Domain.md)**