[[_TOC_]]

# Testes Automatizados

Esta seção aborda a implementação de testes automatizados no SISPREC.

## Tipos de Testes

### [Testes de Domain e Application Services](/Tutorial-de-Início/Testes/Testes-Domain-e-AppService.md)

Documentação dos testes das camadas de domínio e aplicação:
- Testes de unidade para Domain Services
- Testes de integração para Application Services
- Uso de NSubstitute para mocks
- Uso de Bogus para dados de teste
- Padrões e boas práticas

### [Testes de Interface Web](/Tutorial-de-Início/Testes/Testes-Web.md)

Documentação dos testes da camada web:
- Testes de Razor Pages
- Testes de ViewModels
- Validações de formulários
- Integração com serviços mockados
- Padrões e boas práticas

**[Próximo: Testes de Domain e Application Services](/Tutorial-de-Início/Testes/Testes-Domain-e-AppService)**