[[_TOC_]]
# FLUXO DE IMPLEMENTAÇÃO

O fluxo abaixo exemplifica a forma de implementar e reutilizar o serviço de geração de PDF em uma tela do SISPREC:

**1. Inclusão de um botão no cshtml da página onde deseja exibir a função:**

![image.png](/.attachments/image-09775db8-346e-4cdc-8c54-47f0d7882119.png)

<br>

- **_Código Exemplo (Inclusão do Botão na página):_**
`<abp-column class="col-2">
    <abp-button button-type="Outline_Secondary" id="btnExportarPDF" class="col-8">Exportar PDF</abp-button>
</abp-column>`

<br>

- **_Caminho_:**
"C:\TRF3.SISPREC\src\TRF3.SISPREC.Web\Pages\OcorrenciaMotivos\Index.cshtml"

<br>

![image.png](/.attachments/image-f4e93a0b-b604-4454-a397-7b057b5965e8.png)

## **2. **Inclusão da referência do util.js**, onde possui a função responsável por transformar BASE64 em PDF, essa inclusão deve ficar acima do index.js porque a referência do .JS é lida e carregada de cima para baixo, o util.js precisa estar carregado antes do index.js utilizá-lo.**

<br>

- _**Código Exemplo:**_
`<abp-script src="/js/exportacao-pdf.js" />`

<br>

- **_Caminho_:**
"C:\TRF3.SISPREC\src\TRF3.SISPREC.Web\Pages\OcorrenciaMotivos\Index.cshtml"

![image.png](/.attachments/image-0910c7aa-4ee4-4fa0-9040-09af382587a6.png)

## **3. Incluir no index.js o evento do botão "Exportar PDF" para que o mesmo utilize a função do util.js, a função do util.js está sendo utilizada dentro da marcação em azul**

- **_Observação_:** é possível identificar que estou passando o "getFilter()" como parãmetro para o .gerarPDF, para que seja possível exportar um PDF utilizando filtro

- **_Código Exemplo:_**
`downloadPDF('btnExportarPDF', formatarNomeArquivoPdf('ocorrencia-motivo'), service, getFilter);`

- **_Caminho_:**
C:\TRF3.SISPREC\src\TRF3.SISPREC.Web\Pages\OcorrenciaMotivos\index.js

![image.png](/.attachments/image-95fdcc1c-4031-44f9-919f-a1415431fe04.png)

**_Observação:_** no print abaixo, o script utiliza a API com o "service." buscando um endpoint que tenha um método chamado "GerarPDF", a nomenclatura do método no JS deve iniciar com a primeira letra minúscula.

![image.png](/.attachments/image-376e92cf-277c-49e1-bc80-bd6290301f11.png)

## **4. Por fim, é necessário criar o endpoint da API para que o index.js consiga realizar requisições para o endpoint, o nome definido no metodo(endpoint) deve ser utilizado no index.js, que no caso é "GerarPDF" e também é necessário injetar na API a interface do serviço de Gerador de PDF**

Em azul a injeção e em vermelho o metodo/endpoint criado na classe exemplo: OcorrenciaMotivoAppService.cs

- **_Caminho:_**
C:\TRF3.SISPREC\src\TRF3.SISPREC.Application\OcorrenciaMotivos\OcorrenciaMotivoAppService.cs

![image.png](/.attachments/image-0da433ff-6740-4b8a-b84f-8784e3b8b670.png)

o projeto está configurado para utilizar o gerador de PDF.

<br>

## **5. Configurar o template que será utilizado como base para geração do PDF, criar um template HTML é necessário defini-lo no caminho a seguir:**

<br>

- **_Caminho_:**
C:\TRF3.SISPREC\src\TRF3.SISPREC.Infraestrutura\PDFServices\Templates\

<br>

o arquivo deve conter a extensão .html, como no exemplo abaixo:

<br>

![image.png](/.attachments/image-d3f70f15-3595-4b5c-84c5-3f7cab4feba2.png)

neste arquivo HTML as informaçõe são divididas em tabelas, a ideia é mantar um Layout de HTML basico para melhor visualização, como no exemplo a seguir:





