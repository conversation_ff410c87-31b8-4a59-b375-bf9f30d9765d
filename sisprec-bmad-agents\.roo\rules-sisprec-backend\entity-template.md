# Template de Entidade SISPREC

## Classes Base Disponíveis

### 1. BaseAtivaDesativaEntity
Para entidades simples com controle de ativação/desativação:

```csharp
using TRF3.SISPREC;

namespace TRF3.SISPREC.[Modulo]
{
    public class [NomeEntidade] : BaseAtivaDesativaEntity
    {
        // Propriedades específicas da entidade
        // Herda: Ativo (bool)
    }
}
```

### 2. BaseEntidadeDominio (Recomendado)
Para entidades de domínio completas com sincronização CJF:

```csharp
using TRF3.SISPREC;

namespace TRF3.SISPREC.[Modulo]
{
    public class [NomeEntidade] : BaseEntidaDominio
    {
        // Propriedades específicas da entidade
        // Herda: Ativo (bool), DataUtilizacaoFim (DateTime?), FoiSincronizadoCjf (bool)
    }
}
```

### 3. Entity Customizada
Para casos específicos que não se adequam às bases:

```csharp
using System.ComponentModel;
using Volo.Abp.Domain.Entities;

namespace TRF3.SISPREC.[Modulo]
{
    public class [NomeEntidade] : Entity<int>
    {
        // Propriedades da entidade
    }
}
```

## 1. Domain.Shared - Enums e Constants

### Arquivo: `src/TRF3.SISPREC.Domain.Shared/{Modulo}/{NomeEntidade}Enums.cs`

```csharp
namespace TRF3.SISPREC.{Modulo}
{
    public enum {NomeEntidade}Status
    {
        Ativo = 1,
        Inativo = 2,
        // Outros status específicos
    }

    public enum {NomeEntidade}Tipo
    {
        Tipo1 = 1,
        Tipo2 = 2,
        // Outros tipos específicos
    }
}
```

### Arquivo: `src/TRF3.SISPREC.Domain.Shared/{Modulo}/{NomeEntidade}Constants.cs`

```csharp
namespace TRF3.SISPREC.{Modulo}
{
    public static class {NomeEntidade}Constants
    {
        public const int MaxNomeLength = 255;
        public const int MaxDescricaoLength = 1000;
        // Outras constantes
    }
}
```

## 2. Domain - Entidade

### Arquivo: `src/TRF3.SISPREC.Domain/{Modulo}/{NomeEntidade}.cs`

```csharp
using System;
using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.{Modulo};
using Volo.Abp.Domain.Entities;
using Volo.Abp.Auditing;

namespace TRF3.SISPREC.{Modulo}
{
    [Audited]
    public class {NomeEntidade} : Entity, ISoftDelete
    {
        public override object[] GetKeys()
        {
            return new object[] { {NomeEntidade}Id };
        }

        public int {NomeEntidade}Id { get; set; }

        [Required]
        [StringLength({NomeEntidade}Constants.MaxNomeLength)]
        public string Nome { get; set; }

        [StringLength({NomeEntidade}Constants.MaxDescricaoLength)]
        public string Descricao { get; set; }

        public {NomeEntidade}Status Status { get; set; }

        public {NomeEntidade}Tipo Tipo { get; set; }

        // ISoftDelete implementation
        public bool IsDeleted { get; set; }

        // Relacionamentos - Disable auditing for navigation properties
        public Guid? {EntidadeRelacionada}Id { get; set; }

        [DisableAuditing]
        public virtual {EntidadeRelacionada} {EntidadeRelacionada} { get; set; }

        // Construtor protegido para EF Core
        protected {NomeEntidade}()
        {
        }

        // Construtor público
        public {NomeEntidade}(
            string nome,
            {NomeEntidade}Tipo tipo,
            string descricao = null)
        {
            Nome = Check.NotNullOrWhiteSpace(nome, nameof(nome), {NomeEntidade}Constants.MaxNomeLength);
            Tipo = tipo;
            Descricao = descricao;
            Status = {NomeEntidade}Status.Ativo;
        }

        // Métodos de negócio
        public void Ativar()
        {
            Status = {NomeEntidade}Status.Ativo;
        }

        public void Desativar()
        {
            Status = {NomeEntidade}Status.Inativo;
        }

        public void AlterarNome(string nome)
        {
            Nome = Check.NotNullOrWhiteSpace(nome, nameof(nome), {NomeEntidade}Constants.MaxNomeLength);
        }
    }
}
```

### Arquivo Alternativo: `src/TRF3.SISPREC.Domain/{Modulo}/{NomeEntidade}.cs` (BaseEntidadeDominio)

```csharp
using System;
using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.{Modulo};

namespace TRF3.SISPREC.{Modulo}
{
    public class {NomeEntidade} : BaseEntidadeDominio
    {
        public override object[] GetKeys()
        {
            return new object[] { Seq_{NomeEntidade} };
        }

        public int Seq_{NomeEntidade} { get; set; }

        [Required]
        [StringLength({NomeEntidade}Constants.MaxNomeLength)]
        public required string Nome { get; set; }

        [StringLength({NomeEntidade}Constants.MaxDescricaoLength)]
        public string Descricao { get; set; }

        public {NomeEntidade}Status Status { get; set; }

        public {NomeEntidade}Tipo Tipo { get; set; }

        // Relacionamentos
        public int? {EntidadeRelacionada}Id { get; set; }
        public virtual {EntidadeRelacionada} {EntidadeRelacionada} { get; set; }

        // Métodos de negócio
        public void Ativar()
        {
            Status = {NomeEntidade}Status.Ativo;
        }

        public void Desativar()
        {
            Status = {NomeEntidade}Status.Inativo;
        }

        public void AlterarNome(string nome)
        {
            Nome = Check.NotNullOrWhiteSpace(nome, nameof(nome), {NomeEntidade}Constants.MaxNomeLength);
        }
    }
}
```

### Arquivo: `src/TRF3.SISPREC.Domain/{Modulo}/I{NomeEntidade}Repository.cs`

```csharp
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace TRF3.SISPREC.{Modulo}
{
    public interface I{NomeEntidade}Repository : IRepository<{NomeEntidade}, Guid>
    {
        Task<List<{NomeEntidade}>> GetListAsync(
            string filtro = null,
            {NomeEntidade}Status? status = null,
            {NomeEntidade}Tipo? tipo = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            string sorting = null,
            CancellationToken cancellationToken = default);

        Task<long> GetCountAsync(
            string filtro = null,
            {NomeEntidade}Status? status = null,
            {NomeEntidade}Tipo? tipo = null,
            CancellationToken cancellationToken = default);

        Task<{NomeEntidade}> FindByNomeAsync(
            string nome,
            CancellationToken cancellationToken = default);
    }
}
```

## 3. Application.Contracts - DTOs e Interfaces

### Arquivo: `src/TRF3.SISPREC.Application.Contracts/{Modulo}/Dtos/{NomeEntidade}Dto.cs`

```csharp
using System;
using TRF3.SISPREC.{Modulo};
using Volo.Abp.Application.Dtos;

namespace TRF3.SISPREC.{Modulo}.Dtos
{
    public class {NomeEntidade}Dto : AuditedEntityDto<Guid>
    {
        public string Nome { get; set; }
        public string Descricao { get; set; }
        public {NomeEntidade}Status Status { get; set; }
        public {NomeEntidade}Tipo Tipo { get; set; }

        // Propriedades de relacionamento
        public Guid? {EntidadeRelacionada}Id { get; set; }
        public string {EntidadeRelacionada}Nome { get; set; }
    }
}
```

### Arquivo: `src/TRF3.SISPREC.Application.Contracts/{Modulo}/Dtos/Create{NomeEntidade}Dto.cs`

```csharp
using System;
using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.{Modulo};

namespace TRF3.SISPREC.{Modulo}.Dtos
{
    public class Create{NomeEntidade}Dto
    {
        [Required(ErrorMessage = "Nome é obrigatório")]
        [StringLength({NomeEntidade}Constants.MaxNomeLength, ErrorMessage = "Nome deve ter no máximo {1} caracteres")]
        public string Nome { get; set; }

        [StringLength({NomeEntidade}Constants.MaxDescricaoLength, ErrorMessage = "Descrição deve ter no máximo {1} caracteres")]
        public string Descricao { get; set; }

        [Required(ErrorMessage = "Tipo é obrigatório")]
        public {NomeEntidade}Tipo Tipo { get; set; }

        public Guid? {EntidadeRelacionada}Id { get; set; }
    }
}
```
