# Alertas

## Introdução

`abp-alert` é um elemento principal para criar um alerta.

Uso básico:

```xml
<abp-alert alert-type="Primary">
    Um alerta primário simples - confira!
</abp-alert>
```

## Demonstração

Veja a [página de demonstração de alertas](https://bootstrap-taghelpers.abp.io/Components/Alerts) para vê-lo em ação.

## Atributos

### alert-type

Um valor que indica o tipo do alerta. Deve ser um dos seguintes valores:

* `Default` (valor padrão)
* `Primary`
* `Secondary`
* `Success`
* `Danger`
* `Warning`
* `Info`
* `Light`
* `Dark`

Exemplo:

```xml
<abp-alert alert-type="Warning">
    Um alerta de aviso simples - confira!
</abp-alert>
```

### alert-link

Um valor que fornece links com cores correspondentes dentro de qualquer alerta.

Exemplo:

```xml
<abp-alert alert-type="Danger">
    Um alerta de perigo simples com <a abp-alert-link href="#">um link de exemplo</a>. <PERSON><PERSON> se quiser.
</abp-alert>
```

### dismissible

Um valor para tornar o alerta descartável.

Exemplo:

```xml
<abp-alert alert-type="Warning" dismissible="true">
    Santa guacamole! Você deveria verificar alguns desses campos abaixo.
</abp-alert>
```

### Conteúdo Adicional

`abp-alert` também pode conter elementos HTML adicionais como títulos, parágrafos e divisórias.

Exemplo:

```xml
<abp-alert alert-type="Success">
    <h4>Muito bem!</h4>
    <p>Ah sim, você leu com sucesso esta mensagem de alerta importante. Este texto de exemplo vai ser um pouco mais longo para que você possa ver como o espaçamento dentro de um alerta funciona com esse tipo de conteúdo.</p>
    <hr>
    <p class="mb-0">Sempre que precisar, certifique-se de usar utilitários de margem para manter as coisas organizadas.</p>
</abp-alert>
```
