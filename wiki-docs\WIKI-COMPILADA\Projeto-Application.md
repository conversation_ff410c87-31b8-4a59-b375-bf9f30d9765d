[[_TOC_]]

# Projeto Application

O projeto Application contém as implementações dos serviços de aplicação definidos no Application.Contracts.

## Serviços de Aplicação vs Serviços de Domínio

| Característica     | Serviços de Aplicação | Serviços de Domínio   |
| ------------------ | --------------------- | --------------------- |
| Parâmetros/Retorno | DTOs                  | Entidades de domínio  |
| Consumidores       | UI, APIs              | Serviços de Aplicação |
| Autorização        | Sim (via anotações)   | Não                   |
| Responsabilidade   | Casos de uso          | Regras de negócio     |

## Implementação dos Serviços

```csharp
using Microsoft.EntityFrameworkCore;
using TRF3.SISPREC.Autores.Dtos;
using Volo.Abp.Domain.Entities;

namespace TRF3.SISPREC.Autores;

/// <summary>
/// Serviço de aplicação para gerenciar autores utilizando o ABP Framework.
/// Este serviço herda de <see cref="BaseCrudAppService{TEntity,TGetOutputDto,TKey,TGetListInput,TCreateInput,TUpdateInput}"/>
/// para facilitar operações CRUD e aproveita injeção de dependência do ABP.
/// </summary>
public class AutorAppService : BaseCrudAppService<Autor, AutorDto, int, AutorGetListInput, CreateUpdateAutorDto, CreateUpdateAutorDto>,
    IAutorAppService
{

    private readonly IAutorManager _manager;
    private readonly IAutorRepository _repository;

    public AutorAppService(IAutorRepository repository, IAutorManager manager) : base(repository, manager)
    {
        _repository = repository;
        _manager = manager;
    }

    /// <summary>
    /// Exclui um autor pelo identificador e delega a lógica de exclusão ao <see cref="IAutorManager"/>.
    /// </summary>
    protected override async Task DeleteByIdAsync(int id)
    {
        await _manager.ExcluirAsync(await GetEntityByIdAsync(id));
    }

    /// <summary>
    /// Busca um autor por identificador, incluindo o <c>Municipio</c>, lançando exceção se não encontrado.
    /// </summary>
    protected override async Task<Autor> GetEntityByIdAsync(int id)
    {
        var autor = await (await _repository.GetQueryableAsync()).Include(l => l.Municipio).FirstOrDefaultAsync(e => e.AutorId == id);
        if (autor == null)
        {
            throw new EntityNotFoundException(typeof(Autor), id);
        }

        return autor;
    }

    /// <summary>
    /// Aplica a ordenação padrão para Listagem de Autores, ordenando por <c>AutorId</c>.
    /// </summary>
    protected override IQueryable<Autor> ApplyDefaultSorting(IQueryable<Autor> query)
    {
        return query.OrderBy(e => e.AutorId);
    }

    /// <summary>
    /// Cria a query personalizada para filtrar autores pelo <c>AutorId</c>, <c>Nome</c>, <c>Sobrenome</c>, e outros campos.
    /// </summary>
    protected override async Task<IQueryable<Autor>> CreateFilteredQueryAsync(AutorGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .Include(x => x.Municipio)
            .WhereIf(input.AutorId > 0, x => x.AutorId == input.AutorId)
            .WhereIf(!input.Nome.IsNullOrWhiteSpace(), x => x.Nome.Contains(input.Nome))
            .WhereIf(!input.Sobrenome.IsNullOrWhiteSpace(), x => x.Sobrenome.Contains(input.Sobrenome))
            .WhereIf(input.GeneroBiologico != null, x => x.GeneroBiologico == input.GeneroBiologico)
            .WhereIf(!input.Telefone.IsNullOrWhiteSpace(), x => x.Telefone!.Contains(input.Telefone!))
            ;
    }

    /// <summary>
    /// Obtém autores filtrando pelo nome completo, aproveitando funcionalidades do EF Core e do ABP para mapeamento.
    /// Este método é utilizado para preencher o select2 no formulário de Livros, via AJAX.
    /// </summary>
    public async Task<IEnumerable<AutorDto>> GetAutorPorNome(string nomeAutor)
    {
        var autores = await (await _repository.GetQueryableAsync())
                            .Where(e => (e.Nome + " " + e.Sobrenome).Contains(nomeAutor))
                            .OrderBy(e => e.Nome)
                            .ToListAsync();

        return await MapToGetListOutputDtosAsync(autores);
    }
}

```

```csharp
using Microsoft.EntityFrameworkCore;
using TRF3.SISPREC.Livros.Dtos;
using Volo.Abp.Domain.Entities;

namespace TRF3.SISPREC.Livros;
/// <summary>
/// Serviço de aplicação para gerenciar livros utilizando o ABP Framework.
/// Herda de <see cref="BaseCrudAppService{TEntity,TGetOutputDto,TKey,TGetListInput,TCreateInput,TUpdateInput}"/>
/// para facilitar operações CRUD, validações de permissão e injeção de dependência.
/// </summary>
public class LivroAppService : BaseCrudAppService<Livro, LivroDto, int, LivroGetListInput, CreateUpdateLivroDto, CreateUpdateLivroDto>,
    ILivroAppService
{

    private readonly ILivroManager _manager;
    private readonly ILivroRepository _repository;

    public LivroAppService(ILivroRepository repository, ILivroManager manager) : base(repository, manager)
    {
        _repository = repository;
        _manager = manager;
    }

    /// <summary>
    /// Cria um novo livro chamando a lógica de inserção no <see cref="ILivroManager"/>.
    /// Invoca <c>CheckCreatePolicyAsync()</c> para garantir que somente usuários autorizados possam criar.
    /// </summary>
    public override async Task<LivroDto> CreateAsync(CreateUpdateLivroDto input)
    {
        await CheckCreatePolicyAsync();

        var livro = await MapToEntityAsync(input);

        await _manager.InserirAsync(livro, input.AutoresIds, true);
        var livroDto = await MapToGetOutputDtoAsync(livro);

        return livroDto;
    }

    /// <summary>
    /// Atualiza o livro pelo seu identificador, aproveitando a lógica de <see cref="ILivroManager"/>.
    /// Valida permissões por meio de <c>CheckUpdatePolicyAsync()</c>.
    /// </summary>
    public override async Task<LivroDto> UpdateAsync(int id, CreateUpdateLivroDto input)
    {
        await CheckUpdatePolicyAsync();

        var livro = await GetEntityByIdAsync(id);
        await MapToEntityAsync(input, livro);
        await _manager.AlterarAsync(livro, input.AutoresIds, false);

        return await MapToGetOutputDtoAsync(livro);
    }

    /// <summary>
    /// Exclui um livro com base em seu identificador,
    /// delegando a lógica ao <see cref="ILivroManager"/> para manipular o repositório.
    /// </summary>
    protected override Task DeleteByIdAsync(int id)
    {
        return _manager.ExcluirAsync(e => e.LivroId == id);
    }

    /// <summary>
    /// Busca um livro pelo identificador, incluindo a coleção de autores.
    /// Lança <see cref="EntityNotFoundException"/> se não houver resultado.
    /// </summary>
    protected override async Task<Livro> GetEntityByIdAsync(int id)
    {
        var livro = await (await _repository.GetQueryableAsync()).Include(l => l.Autores).FirstOrDefaultAsync(e => e.LivroId == id);
        if (livro == null)
        {
            throw new EntityNotFoundException(typeof(Livro), id);
        }

        return livro;
    }

    /// <summary>
    /// Aplica a ordenação padrão por <c>LivroId</c> quando nenhum outro critério for especificado.
    /// </summary>
    protected override IQueryable<Livro> ApplyDefaultSorting(IQueryable<Livro> query)
    {
        return query.OrderBy(e => e.LivroId);
    }

    /// <summary>
    /// Cria uma consulta filtrada de livros, aplicando filtros de acordo com os campos informados no input.
    /// Utiliza métodos como <c>WhereIf</c> para permitir buscas condicionais no EF Core.
    /// </summary>
    protected override async Task<IQueryable<Livro>> CreateFilteredQueryAsync(LivroGetListInput input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .WhereIf(input.LivroId > 0, x => x.LivroId == input.LivroId)
            .WhereIf(!input.Titulo.IsNullOrWhiteSpace(), x => x.Titulo.Contains(input.Titulo!))
            .WhereIf(input.Categoria != null, x => x.Categoria == input.Categoria)
            .WhereIf(input.DataPublicacaoInicio != null, x => x.DataPublicacao >= input.DataPublicacaoInicio!.Value)
            .WhereIf(input.DataPublicacaoFinal != null, x => x.DataPublicacao <= input.DataPublicacaoFinal!.Value)
            .WhereIf(input.PrecoMin > 0, x => x.Preco >= input.PrecoMin)
            .WhereIf(input.PrecoMax > 0, x => x.Preco <= input.PrecoMax)
            .WhereIf(input.Disponivel != null, x => x.Disponivel == input.Disponivel)
            ;
    }
}

```

## Configuração do AutoMapper

O mapeamento entre entidades e DTOs é configurado no perfil do SISPRECApplicationAutoMapperProfile:

```csharp
public class SISPRECApplicationAutoMapperProfile : Profile
{
    public SISPRECApplicationAutoMapperProfile()
    {
        //... OUTROS MAPEAMENTOS...

        //... Mapeamentos de entidades para DTOs
        CreateMap<Autor, AutorDto>();
        CreateMap<CreateUpdateAutorDto, Autor>(MemberList.Source);
        CreateMap<Livro, LivroDto>();
        CreateMap<CreateUpdateLivroDto, Livro>(MemberList.Source);
    }
}
```

**[Próximo: Projeto Web](/Tutorial-de-Início/Projeto-Web)**
