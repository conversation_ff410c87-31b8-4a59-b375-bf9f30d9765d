[[_TOC_]]
#Controle de mudanças no banco de dados

Atualmente, no TRF3, as alterações de banco de dados de projetos criados e mantidos pela DIPS são aplicadas nas bases de homologação e produção pela DIAD. Logo, não serão utilizadas migrations nesses ambientes. 
Para alteração da base nesse ambientes, é necessário criar um script SQL e abrir um chamado para a área responsável aplicá-lo.

No entanto, nos ambientes de desenvolvimento e staging, serão utilizadas migrations. Para isso, devem ser seguidas algumas regras ao criar as configurations conforme as regras descritas na página [Configurations e Migrations](https://azure-devops.trf3.jus.br/JF3R/TRF3.SISPREC/_wiki/wikis/TRF3.SISPREC.wiki/40/Configurations-e-Migrations)

#Regras para nomenclatura
As estruturas de banco de dados são criadas pela DIAD - Divisão de Administração de Dados e Banco de Dados, após validação dos scripts DDL enviados a eles por chamado.
A fim de agilizar o processo de entrega das funcionalidades, buscaremos evitar correções, portanto, o ideal é criar scripts já no padrão exigido pela DIAD.

Para atingir esse objetivo, é necessário criar os nomes de tabelas e colunas seguindo alguns padrões. Conforme descrito no [guia de nomeclatura](/Guia-de-Desenvolvimento/Banco-de-Dados/Nomes-de-tabelas-e-colunas).