[[_TOC_]]
#Bogus
Ao usar o Bogus para criar Fakers para retorno dos mocks, adotar as dicas a seguir
   - **Strings**: Usar Random.Hash(). Não usar Random.String() ou qual outro método, porque infelizmente podem gerar caracteres especiais, e os testes falharem aleatoriamente.
    - **Propriedade IsDelete**: lembrar de não usar Random.Bool(), usar valor padrão = true.
        - Só usar false se for testar o filtro de softdelete.
#Querables
Para facilitar a criação de listas de retornos para mocks dos repositórios, a biblioteca `MockQueryable.NSubstitute` oferece uma maneira prática de simular o comportamento de `DbSet` e `IQueryable`. Isso é especialmente útil em testes unitários, onde você deseja isolar a lógica de negócios e evitar dependências de banco de dados reais.

## Como usar `MockQueryable.NSubstitute`

A classe `NSubstituteExtensions` fornece métodos que permitem construir mocks de `IQueryable` e `DbSet`. Com isso, você pode criar listas de dados que serão retornadas pelos métodos do repositório durante os testes.

### Exemplo de uso

Aqui está um exemplo de como usar `NSubstituteExtensions` em um teste unitário. Neste caso, utilizamos o método `MoverParaProximaEtapaAsync_Deve_Retornar_Erro_Controles_Invalidos` para demonstrar a criação de um mock:

```csharp
using MockQueryable.NSubstitute;
using NSubstitute;
using Shouldly;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

public class ControleProcessamentoManagerTests : SISPRECDomainTestBase<SISPRECTestBaseModule>
{
    private readonly IControleProcessamentoRepository _controleProcessamentoRepository;
    private readonly ControleProcessamentoManager _controleProcessamentoManager;

    public ControleProcessamentoManagerTests()
    {
        _controleProcessamentoRepository = Substitute.For<IControleProcessamentoRepository>();
        _controleProcessamentoManager = new ControleProcessamentoManager(_controleProcessamentoRepository);
    }

    [Fact]
    public async Task MoverParaProximaEtapaAsync_Deve_Retornar_Erro_Controles_Invalidos()
    {
        var controleProcessamento = new ControleProcessamento
        {
            Etapa = new TipoEtapaProcessamento() { Etapa = ETipoEtapaProcessamento.Validacao },
            ControleProcessos = new List<ControleProcessamentoProcesso>
            {
                new ControleProcessamentoProcesso { Status = EControleProcessamentoProcessoStatus.ErroValidacao }
            }
        };

        var lista = new List<ControleProcessamento> { controleProcessamento }.AsQueryable().BuildMockDbSet();
        _controleProcessamentoRepository.GetQueryableAsync().Returns(lista);
        _controleProcessamentoRepository.ObterPorId(1).Returns(controleProcessamento);

        var message = await Should.ThrowAsync<UserFriendlyException>(() => _controleProcessamentoManager.MoverParaProximaEtapaAsync(1));
        message.Message.ShouldBe("Não é possivel mover para proxima etapa pois não existem processos validos");
    }
}
```

Neste exemplo, o método `BuildMockDbSet` é utilizado para criar um `DbSet` simulado que retorna uma lista de `ControleProcessamento`. Isso permite que você teste a lógica do seu gerenciador sem depender de um banco de dados real, garantindo que seus testes sejam rápidos e confiáveis.

#FirstOrDefaultAsync
Para mockar o retorno, é necessário dizer qual o argumento, que pode ser genérico ou específico.
    - Para mockar retorno com argumento genérico basta usar a abordagem `Arg.Any<>()`
    
```csharp
    _verificacaoTipoRepository
    			.FirstOrDefaultAsync(Arg.Any<Expression<Func<VerificacaoTipo, bool>>>())
    			.Returns(verificacaoTipo);
```
    
Porém, em alguns cenários pode ser necessário mockar retorno do método FirstOrDefaultAsync com um argumento específico, que é um predicado é necessário usar a seguinte abordagem:
    
```csharp
    _tipoIndicadorEconomicoRepository
          .FirstOrDefaultAsync(Arg.Is<Expression<Func<IndicadorEconomicoTipo, bool>>>(expr => expr.Compile().Invoke(new IndicadorEconomicoTipo { Codigo = "xxx" })))
          .Returns((IndicadorEconomicoTipo)null);
```

# IReqPagUnitOfWork
Em serviços que se conectam com a base Ingres do ReqPag, para criar seus testes é necessário realizar o mock dos retornos dos métodos acionados. A interface `IReqPagUnitOfWork` é utilizada para acessar os repositórios do ReqPag.

## Como mockar a interface

Para mockar a interface `IReqPagUnitOfWork`, utilize o método `Substitute.For<IReqPagUnitOfWork>()` do NSubstitute. Isso criará um mock da interface que você poderá configurar para retornar os valores desejados em seus testes.

```csharp
using NSubstitute;
using TRF3.SISPREC.SincronizacaoLegado;

// ...

var reqPagUnitOfWorkMock = Substitute.For<IReqPagUnitOfWork>();
```

## Como mockar os repositórios

A interface `IReqPagUnitOfWork` expõe diversas propriedades, cada uma representando um repositório. Para mockar os retornos desses repositórios, você precisa acessar a propriedade correspondente no mock da interface e usar o método `Returns()` do NSubstitute para definir o valor de retorno.

```csharp
using NSubstitute;
using TRF3.SISPREC.SincronizacaoLegado;
using TRF3.SISPREC.SincronizacaoLegado.Repositories.Interfaces;
using System.Collections.Generic;
using Bogus;

// ...

var reqPagUnitOfWorkMock = Substitute.For<IReqPagUnitOfWork>();
var advogadoJudicialRepositoryMock = Substitute.For<IAdvogadoJudicialRepository>();

// Mockando o retorno do repositório de AdvogadoJudicial
var advogadoJudicialFaker = new Faker<AdvogadoJudicial>("pt_BR")
    .RuleFor(p => p.AdvogadoJudicialId, p => p.Random.Int(min: 10000, max: 20000))
    .RuleFor(p => p.Nome, p => p.Random.Hash(20))
    .RuleFor(p => p.NomeSocial, p => p.Random.Hash(20))
    .RuleFor(p => p.Cpf, p => p.Person.Cpf())
    .RuleFor(p => p.CodigoOab, p => p.Random.Hash(5))
    .RuleFor(p => p.Ativo, p => p.Random.Bool());

var listaAdvogados = advogadoJudicialFaker.Generate(3);

advogadoJudicialRepositoryMock.GetQueryableAsync().Returns(Task.FromResult(listaAdvogados.AsQueryable()));

reqPagUnitOfWorkMock.AdvogadoJudicialRepository.Returns(advogadoJudicialRepositoryMock);
```

## Casos de uso

A seguir, apresentamos alguns casos de uso comuns para mockar a interface `IReqPagUnitOfWork` e seus repositórios.

### Retornar uma lista de entidades

Para simular o retorno de uma lista de entidades, você pode usar o método `Returns()` com uma lista criada com `Bogus` e convertida para `IQueryable` usando `AsQueryable()`.

```csharp
using NSubstitute;
using TRF3.SISPREC.SincronizacaoLegado;
using TRF3.SISPREC.SincronizacaoLegado.Repositories.Interfaces;
using System.Collections.Generic;
using Bogus;

// ...

var reqPagUnitOfWorkMock = Substitute.For<IReqPagUnitOfWork>();
var advogadoJudicialRepositoryMock = Substitute.For<IAdvogadoJudicialRepository>();

// Mockando o retorno do repositório de AdvogadoJudicial
var advogadoJudicialFaker = new Faker<AdvogadoJudicial>("pt_BR")
    .RuleFor(p => p.AdvogadoJudicialId, p => p.Random.Int(min: 10000, max: 20000))
    .RuleFor(p => p.Nome, p => p.Random.Hash(20))
    .RuleFor(p => p.NomeSocial, p => p.Random.Hash(20))
    .RuleFor(p => p.Cpf, p => p.Person.Cpf())
    .RuleFor(p => p.CodigoOab, p => p.Random.Hash(5))
    .RuleFor(p => p.Ativo, p => p.Random.Bool());

var listaAdvogados = advogadoJudicialFaker.Generate(3);

advogadoJudicialRepositoryMock.GetQueryableAsync().Returns(Task.FromResult(listaAdvogados.AsQueryable()));

reqPagUnitOfWorkMock.AdvogadoJudicialRepository.Returns(advogadoJudicialRepositoryMock);
```

### Retornar uma entidade específica

Para simular o retorno de uma entidade específica, você pode usar o método `Returns()` com a entidade desejada.

```csharp
using NSubstitute;
using TRF3.SISPREC.SincronizacaoLegado;
using TRF3.SISPREC.SincronizacaoLegado.Repositories.Interfaces;
using Bogus;
using System.Threading.Tasks;
using System.Linq.Expressions;
using System;

// ...

var reqPagUnitOfWorkMock = Substitute.For<IReqPagUnitOfWork>();
var advogadoJudicialRepositoryMock = Substitute.For<IAdvogadoJudicialRepository>();

// Mockando o retorno do repositório de AdvogadoJudicial
var advogadoJudicialFaker = new Faker<AdvogadoJudicial>("pt_BR")
    .RuleFor(p => p.AdvogadoJudicialId, p => p.Random.Int(min: 10000, max: 20000))
    .RuleFor(p => p.Nome, p => p.Random.Hash(20))
    .RuleFor(p => p.NomeSocial, p => p.Random.Hash(20))
    .RuleFor(p => p.Cpf, p => p.Person.Cpf())
    .RuleFor(p => p.CodigoOab, p => p.Random.Hash(5))
    .RuleFor(p => p.Ativo, p => p.Random.Bool());

var advogado = advogadoJudicialFaker.Generate();

advogadoJudicialRepositoryMock
    .FirstOrDefaultAsync(Arg.Any<Expression<Func<AdvogadoJudicial, bool>>>())
    .Returns(Task.FromResult(advogado));

reqPagUnitOfWorkMock.AdvogadoJudicialRepository.Returns(advogadoJudicialRepositoryMock);
```

### Simular um erro ao salvar as alterações

Para simular um erro ao salvar as alterações, você pode usar o método `SaveChangesAsync()` do mock da interface e configurar para lançar uma exceção.

```csharp
using NSubstitute;
using TRF3.SISPREC.SincronizacaoLegado;
using System;
using System.Threading.Tasks;

// ...

var reqPagUnitOfWorkMock = Substitute.For<IReqPagUnitOfWork>();

reqPagUnitOfWorkMock.SaveChangesAsync().Returns(Task.FromException<int>(new Exception("Erro ao salvar as alterações.")));
```

### Mockar chamadas com argumentos específicos

Em alguns casos, você pode precisar mockar o retorno de um método com base em um argumento específico. Para isso, você pode usar o método `Arg.Is<T>(predicate)` do NSubstitute.

```csharp
using NSubstitute;
using TRF3.SISPREC.SincronizacaoLegado;
using TRF3.SISPREC.SincronizacaoLegado.Repositories.Interfaces;
using Bogus;
using System.Threading.Tasks;
using System.Linq.Expressions;
using System;

// ...

var reqPagUnitOfWorkMock = Substitute.For<IReqPagUnitOfWork>();
var advogadoJudicialRepositoryMock = Substitute.For<IAdvogadoJudicialRepository>();

// Mockando o retorno do repositório de AdvogadoJudicial
var advogadoJudicialFaker = new Faker<AdvogadoJudicial>("pt_BR")
    .RuleFor(p => p.AdvogadoJudicialId, p => p.Random.Int(min: 10000, max: 20000))
    .RuleFor(p => p.Nome, p => p.Random.Hash(20))
    .RuleFor(p => p.NomeSocial, p => p.Random.Hash(20))
    .RuleFor(p => p.Cpf, p => p.Person.Cpf())
    .RuleFor(p => p.CodigoOab, p => p.Random.Hash(5))
    .RuleFor(p => p.Ativo, p => p.Random.Bool());

var advogado = advogadoJudicialFaker.Generate();

advogadoJudicialRepositoryMock
    .FirstOrDefaultAsync(Arg.Is<Expression<Func<AdvogadoJudicial, bool>>>(expr => expr.Compile().Invoke(new AdvogadoJudicial { AdvogadoJudicialId = 123 })))
    .Returns(Task.FromResult(advogado));

reqPagUnitOfWorkMock.AdvogadoJudicialRepository.Returns(advogadoJudicialRepositoryMock);
```

Com esses exemplos, você poderá mockar a interface `IReqPagUnitOfWork` e seus repositórios de forma eficaz em seus testes unitários.

# Chamadas de métodos com argumentos idênticos
Em alguns testes, precisamos simular múltiplas chamadas ao mesmo método (com os mesmos parâmetros) retornando resultados diferentes em cada invocação. 

Segundo a [documentação do NSubstitute](https://nsubstitute.github.io/help/multiple-returns/), podemos fazer isso chamando o método `Returns()` com vários argumentos, sendo a sequência dos argumentos a mesma sequência do retorno.

Exemplo da documentação:
> O exemplo a seguir demonstra isso para uma chamada a uma propriedade, mas funciona da mesma forma para chamadas de métodos.
```csharp
calculator.Mode.Returns("DEC", "HEX", "BIN");
Assert.AreEqual("DEC", calculator.Mode);
Assert.AreEqual("HEX", calculator.Mode);
Assert.AreEqual("BIN", calculator.Mode);
```
Exemplo de teste usado no projeto, no método `ImportarPrecatorioAutomatizadoTests.Executar_Deve_Processar_Com_Sucesso()`:

```csharp
_serviceProvider.GetService<IControleProcessamentoRepository>()
.ObterPrecatoriosOrigem(controleProcessamento.Fase.TipoPrecatorio, controleProcessamento.Id)
/*
 * Returns() pode receber vários argumentos.
 * Neste caso de teste específico, é necessário mockar o retorno de 3 chamadas ao método ObterPrecatoriosOrigem().
 * Assim, na PRIMEIRA e SEGUNDA vez em que o método for chamado, é retornado o objeto listaPreenchida (1º e 2º argumentos).
 * Na TERCEIRA vez em que o método for chamado, é retornado o objeto listaVazia (3º argumento).
 */
.Returns(listaPreenchida,
         listaPreenchida,
         listaVazia);
```

