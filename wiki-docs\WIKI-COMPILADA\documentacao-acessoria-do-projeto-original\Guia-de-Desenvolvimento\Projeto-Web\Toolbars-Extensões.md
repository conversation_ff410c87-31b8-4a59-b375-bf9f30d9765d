# Extensões da Barra de Ferramentas da Página para ASP.NET Core UI

O sistema de barra de ferramentas da página permite que você adicione componentes à barra de ferramentas de qualquer página. A barra de ferramentas da página é a área à direita do cabeçalho de uma página. Um botão ("Importar usuários do excel") foi adicionado à página de gerenciamento de usuários abaixo:

![page-toolbar-button](/ABP-Docs/images/page-toolbar-button.png)

Você pode adicionar qualquer tipo de item de view component à barra de ferramentas da página ou modificar os itens existentes.

## Como Configurar

Neste exemplo, adicionaremos um botão "Importar usuários do excel" e executaremos um código JavaScript para a página de gerenciamento de usuários do [Módulo de Identidade](../../Modules/Identity.md).

### Adicionar um Novo Botão à Página de Gerenciamento de Usuários

Escreva o seguinte código dentro do `ConfigureServices` da sua classe de módulo web:

````csharp
Configure<AbpPageToolbarOptions>(options =>
{
    options.Configure<Volo.Abp.Identity.Web.Pages.Identity.Users.IndexModel>(toolbar =>
    {
        toolbar.AddButton(
            LocalizableString.Create<MyProjectNameResource>("ImportFromExcel"),
            icon: "file-import",
            id: "ImportUsersFromExcel",
            type: AbpButtonType.Secondary
        );
    });
});
````

`AddButton` é um atalho para simplesmente adicionar um componente de botão. Observe que você precisa adicionar o `ImportFromExcel` ao seu dicionário de localização (arquivo json) para localizar o texto.

Quando você executa o aplicativo, você verá o botão adicionado ao lado da lista de botões atual. Existem alguns outros parâmetros do método `AddButton` (por exemplo, use `order` para definir a ordem do componente do botão em relação aos outros componentes).

### Criar um Arquivo JavaScript

Agora, podemos ir para o lado do cliente para lidar com o evento de clique do novo botão. Primeiro, adicione um novo arquivo JavaScript à sua solução. Adicionamos dentro da pasta `/Pages/Identity/Users` do projeto `.Web`:

![user-action-extension-on-solution](/ABP-Docs/images/user-action-extension-on-solution.png)

Aqui, o conteúdo deste arquivo JavaScript:

````js
$(function () {
    $('#ImportUsersFromExcel').click(function (e) {
        e.preventDefault();
        alert('TODO: importar usuários do excel');
    });
});
````

No evento `click`, você pode fazer qualquer coisa que precisar fazer.

### Adicionar o Arquivo à Página de Gerenciamento de Usuários

Então você precisa adicionar este arquivo JavaScript à página de gerenciamento de usuários. Você pode aproveitar o poder do [sistema de Bundling & Minificação](Bundling-Minification.md).

Escreva o seguinte código dentro do `ConfigureServices` da sua classe de módulo:

````csharp
Configure<AbpBundlingOptions>(options =>
{
    options.ScriptBundles.Configure(
        typeof(Volo.Abp.Identity.Web.Pages.Identity.Users.IndexModel).FullName,
        bundleConfiguration =>
        {
            bundleConfiguration.AddFiles(
                "/Pages/Identity/Users/<USER>"
            );
        });
});
````

Esta configuração adiciona `my-user-extensions.js` à página de gerenciamento de usuários do Módulo de Identidade. `typeof(Volo.Abp.Identity.Web.Pages.Identity.Users.IndexModel).FullName` é o nome do bundle na página de gerenciamento de usuários. Esta é uma convenção comum usada para todos os módulos ABP Commercial.

## Casos de Uso Avançados

Embora você normalmente queira adicionar uma ação de botão à barra de ferramentas da página, é possível adicionar qualquer tipo de componente.

### Adicionar View Component a uma Barra de Ferramentas da Página

Primeiro, crie um novo view component em seu projeto:

![page-toolbar-custom-component](/ABP-Docs/images/page-toolbar-custom-component.png)

Para este exemplo, criamos um view component `MyToolbarItem` na pasta `/Pages/Identity/Users/<USER>

Conteúdo de `MyToolbarItemViewComponent.cs`:

````csharp
public class MyToolbarItemViewComponent : AbpViewComponent
{
    public IViewComponentResult Invoke()
    {
        return View("~/Pages/Identity/Users/<USER>/Default.cshtml");
    }
}
````

Conteúdo de `Default.cshtml`:

````xml
<span>
    <button type="button" class="btn btn-dark">CLIQUE EM MIM</button>
</span>
````

* O arquivo `.cshtml` pode conter qualquer tipo de componente(s). É um view component típico.
* `MyToolbarItemViewComponent` pode injetar e usar qualquer serviço, se você precisar.

Então você pode adicionar o `MyToolbarItemViewComponent` à página de gerenciamento de usuários:

````csharp
Configure<AbpPageToolbarOptions>(options =>
{
    options.Configure<Volo.Abp.Identity.Web.Pages.Identity.Users.IndexModel>(
        toolbar =>
        {
            toolbar.AddComponent<MyToolbarItemViewComponent>();
        }
    );
});
````

* Se o seu componente aceitar argumentos (no método `Invoke`/`InvokeAsync`), você pode passá-los para o método `AddComponent` como um objeto anônimo.

#### Permissões

Se o seu botão/componente deve estar disponível com base em uma [permissão/política](../../Authorization.md), você pode passar o nome da permissão/política como o parâmetro `requiredPolicyName` para os métodos `AddButton` e `AddComponent`.

### Adicionar um Page Toolbar Contributor

Se você executar uma lógica personalizada avançada ao adicionar um item a uma barra de ferramentas da página, você pode criar uma classe que implemente a interface `IPageToolbarContributor` ou herde da classe `PageToolbarContributor`:

````csharp
public class MyToolbarContributor : PageToolbarContributor
{
    public override Task ContributeAsync(PageToolbarContributionContext context)
    {
        context.Items.Insert(0, new PageToolbarItem(typeof(MyToolbarItemViewComponent)));

        return Task.CompletedTask;
    }
}
````

* Você pode usar `context.ServiceProvider` para resolver dependências, se precisar.

Então adicione sua classe à lista `Contributors`:

````csharp
Configure<AbpPageToolbarOptions>(options =>
{
    options.Configure<Volo.Abp.Identity.Web.Pages.Identity.Users.IndexModel>(
        toolbar =>
        {
            toolbar.Contributors.Add(new MyToolbarContributor());
        }
    );
});
