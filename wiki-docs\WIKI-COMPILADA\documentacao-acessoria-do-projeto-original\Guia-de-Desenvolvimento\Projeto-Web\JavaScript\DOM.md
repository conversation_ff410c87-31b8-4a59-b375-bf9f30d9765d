# UI para ASP.NET Core MVC / Razor Pages: API JavaScript DOM

`abp.dom` (Document Object Model) fornece eventos aos quais você pode se inscrever para ser notificado quando elementos são adicionados e removidos dinamicamente da página (DOM).

É especialmente útil se você quiser inicializar os novos elementos carregados. Isso geralmente é necessário quando você adiciona elementos dinamicamente ao DOM (por exemplo, obtém alguns elementos HTML via AJAX) após a inicialização da página.

> O ABP usa o [MutationObserver](https://developer.mozilla.org/en-US/docs/Web/API/MutationObserver) para observar as alterações feitas no DOM.

## Eventos de Nó

### onNodeAdded

Este evento é acionado quando um elemento é adicionado ao DOM. Exemplo:

```js
abp.dom.onNodeAdded(function(args){
    console.log(args.$el);
});
```

O objeto `args` possui os seguintes campos:

*   `$el`: A seleção JQuery para obter o novo elemento inserido no DOM.

### onNodeRemoved

Este evento é acionado quando um elemento é removido do DOM. Exemplo:

```js
abp.dom.onNodeRemoved(function(args){
    console.log(args.$el);
});
```

O objeto `args` possui os seguintes campos:

*   `$el`: A seleção JQuery para obter o elemento removido do DOM.

## Inicializadores Pré-Construídos

O ABP Framework está usando os eventos DOM para inicializar alguns tipos de elementos HTML quando eles são adicionados ao DOM depois que a página já foi inicializada.

> Observe que os mesmos inicializadores também funcionam se esses elementos já estiverem incluídos no DOM inicial. Portanto, se eles são carregados inicialmente ou lazy, eles funcionam como esperado.

### Inicializador de Formulário

O inicializador de formulário (definido como `abp.dom.initializers.initializeForms`) inicializa os formulários carregados lazy:

*   Habilita automaticamente a validação `unobtrusive` no formulário.
*   Pode mostrar automaticamente uma mensagem de confirmação quando você envia o formulário. Para habilitar este recurso, basta adicionar o atributo `data-confirm` com uma mensagem (como `data-confirm="Tem certeza?"`) ao elemento `form`.
*   Se o elemento `form` tiver o atributo `data-ajaxForm="true"`, então chama automaticamente o `.abpAjaxForm()` no elemento `form`, para fazer com que o formulário seja postado via AJAX.

Consulte o documento [Formulários e Validação](../Forms-Validation.md) para obter mais informações.

### Inicializador de Script

O inicializador de script (`abp.dom.initializers.initializeScript`) pode executar um código JavaScript para um elemento DOM.

**Exemplo: Carregar um componente lazy e executar algum código quando o elemento for carregado**

Suponha que você tenha um contêiner para carregar o elemento dentro:

```html
<div id="LazyComponent"></div>
```

E este é o componente que será carregado via AJAX do servidor e inserido no contêiner:

```html
<div data-script-class="MyCustomClass">
    <p>Mensagem de exemplo</p>
</div>
```

`data-script-class="MyCustomClass"` indica a classe JavaScript que será usada para realizar alguma lógica neste elemento:

`MyCustomClass` é um objeto global definido como mostrado abaixo:

```js
MyCustomClass = function(){

    function initDom($el){
        $el.css('color', 'red');
    }

    return {
        initDom: initDom
    }
};
```

`initDom` é a função que é chamada pelo ABP Framework. O argumento `$el` é o elemento HTML carregado como uma seleção JQuery.

Finalmente, você pode carregar o componente dentro do contêiner após uma chamada AJAX:

```js
$(function () {
    setTimeout(function(){
        $.get('/get-my-element').then(function(response){
           $('#LazyComponent').html(response);
        });
    }, 2000);
});
```

O sistema de inicialização de script é especialmente útil se você não sabe como e quando o componente será carregado no DOM. Isso pode ser possível se você desenvolveu um componente de UI reutilizável em uma biblioteca e deseja que o desenvolvedor do aplicativo não se preocupe em como inicializar o componente em diferentes casos de uso.

> A inicialização de script não funciona se o componente foi carregado no DOM inicial. Neste caso, você é responsável por inicializá-lo.

### Outros Inicializadores

Os seguintes componentes e bibliotecas do Bootstrap são inicializados automaticamente quando são adicionados ao DOM:

*   Tooltip
*   Popover
*   Timeago
