# ASP.NET Core MVC / Razor Pages UI: Toolbars

O sistema de Toolbars é usado para definir **toolbars** na interface do usuário. <PERSON><PERSON><PERSON><PERSON> (ou sua aplicação) podem adicionar **itens** a uma toolbar, então o [tema](theming.md) renderiza a toolbar no **layout**.

Existe apenas uma **toolbar padrão** chamada "Main" (definida como uma constante: `StandardToolbars.Main`). O [Tema Básico](basic-theme) renderiza a toolbar principal como mostrado abaixo:

![bookstore-toolbar-highlighted](/ABP-Docs/images/bookstore-toolbar-highlighted.png)

Na captura de tela acima, existem dois itens adicionados à toolbar principal: componente de troca de idioma e menu do usuário. Você pode adicionar seus próprios itens aqui.

Além disso, o [Tema LeptonX Lite](../../../ui-themes/lepton-x-lite/asp-net-core.md) possui 2 toolbars diferentes para visualizações de desktop e mobile, que são definidas como constantes: `LeptonXLiteToolbars.Main`, `LeptonXLiteToolbars.MainMobile`.

| LeptonXLiteToolbars.Main | LeptonXLiteToolbars.MainMobile |
| :---: | :---: |
| ![leptonx](/ABP-Docs/images/leptonxlite-toolbar-main-example.png) | ![leptonx](/ABP-Docs/images/leptonxlite-toolbar-mainmobile-example.png) |

## Exemplo: Adicionar um Ícone de Notificação

Neste exemplo, adicionaremos um **ícone de notificação (sino)** à esquerda do item de troca de idioma. Um item na toolbar deve ser um **componente de visualização**. Então, primeiro, crie um novo componente de visualização em seu projeto:

![bookstore-notification-view-component](/ABP-Docs/images/bookstore-notification-view-component.png)

**NotificationViewComponent.cs**

````csharp
public class NotificationViewComponent : AbpViewComponent
{
    public async Task<IViewComponentResult> InvokeAsync()
    {
        return View("/Pages/Shared/Components/Notification/Default.cshtml");
    }
}
````

**Default.cshtml**

````xml
<div id="MainNotificationIcon" style="color: white; margin: 8px;">
    <i class="far fa-bell"></i>
</div>
````

Agora, podemos criar uma classe implementando a interface `IToolbarContributor`:

````csharp
public class MyToolbarContributor : IToolbarContributor
{
    public Task ConfigureToolbarAsync(IToolbarConfigurationContext context)
    {
        if (context.Toolbar.Name == StandardToolbars.Main)
        {
            context.Toolbar.Items
                .Insert(0, new ToolbarItem(typeof(NotificationViewComponent)));
        }

        return Task.CompletedTask;
    }
}
````

Você pode usar a [autorização](../../fundamentals/authorization.md) para decidir se adiciona um `ToolbarItem`.

````csharp
if (await context.IsGrantedAsync("MyPermissionName"))
{
    //...adiciona itens da Toolbar
}
````

Você pode usar o método de extensão `RequirePermissions` como um atalho. Ele também é mais eficiente, o ABP otimiza a verificação de permissão para todos os itens.

````csharp
context.Toolbar.Items.Insert(0, new ToolbarItem(typeof(NotificationViewComponent)).RequirePermissions("MyPermissionName"));
````

Essa classe adiciona o `NotificationViewComponent` como o primeiro item na toolbar `Main`.

Finalmente, você precisa adicionar este contribuidor ao `AbpToolbarOptions`, no `ConfigureServices` do seu [módulo](../../architecture/modularity/basics.md):

````csharp
Configure<AbpToolbarOptions>(options =>
{
    options.Contributors.Add(new MyToolbarContributor());
});
````

É só isso, você verá o ícone de notificação na toolbar quando executar a aplicação:

![bookstore-notification-icon-on-toolbar](/ABP-Docs/images/bookstore-notification-icon-on-toolbar.png)

`NotificationViewComponent` neste exemplo simplesmente retorna uma visualização sem nenhum dado. Na vida real, você provavelmente deseja **consultar o banco de dados** (ou chamar uma API HTTP) para obter as notificações e passar para a visualização. Se precisar, você pode adicionar um arquivo `JavaScript` ou `CSS` ao [bundle](bundling-minification.md) global para o seu item da toolbar.

## IToolbarManager

`IToolbarManager` é usado para renderizar a toolbar. Ele retorna os itens da toolbar por um nome de toolbar. Isso geralmente é usado pelos [temas](theming.md) para renderizar a toolbar no layout.
