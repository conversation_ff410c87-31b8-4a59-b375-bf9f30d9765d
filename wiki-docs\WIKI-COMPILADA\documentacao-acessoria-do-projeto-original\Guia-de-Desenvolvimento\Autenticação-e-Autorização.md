[[_TOC_]]
# Autenticação e Autorização
Atualmente a autenticação e autorização é feita via client OIDC, consumindo um servidor do próprio do TRF3, chamado CAU.
# Permissões (Autorizações) SISPREC - CAU
- Para cada permissão criada no fonte do SISPREC, deve ser criada um recurso no CAU com valor idêntico.
- Os recursos deverão ser atribuidos para um perfil no CAU.
- As permissoes criadas no fonte, devem ser registradas como Policy, no método RegistrarPermissoes da Classe SISPRECPermissoes
- Para acessar o SISPREC, os usuários deverão ter algum perfil atribuido no CAU.
  - O perfil AdminTI deve poder acessar todos os recursos.
  - Demais perfis devem ter suas permissões atribuidas
  - Por enquanto, não é possível, no CAU, herdar permissões uma da outra e também não é possível combinar/herdar perfis.
- Geralmente, as permissões serão criadas para serem aplicadas nos ApplicationServices, e podem existir dois tipos para cada AppService:
    - Visualizar
    - Gravar


**Convenções**
- Permissão "Visualizar" é a permissão básica:
  - Dá acesso ao item de menu.
  - Dá acesso à página index e eventual página de detalhes.

OBS.: Para cada ambiente (desenvolvimento, staging, homologação, etc) há um registro de sistema diferente no CAU, isto é, a estrutura de permissões no CAU deve ser criada para cada sistema equivalente ao ambiente.

**Nomenclatura**
Caso seja necessário criar permissões específicas para cada ação no AppService, criar com as seguintes regras de nomenclatura:
- Criar classe estática com nome do AppService. Ver exemplo na classe `SISPRECPermissoes.cs`
```CSHARP
    public static class UnidadeOrcamentaria
    {
        public const string Visualizar = nameof(UnidadeOrcamentaria) + PermissaoVisualizar;
        public const string Gravar = nameof(UnidadeOrcamentaria) + PermissaoGravar;
    }

```

- Chamar o método para registrar a permissão criada:
```CSHARP
    public static void RegistrarPermissoes(AuthorizationOptions options)
    {
        //... outros registros

        RegistrarPermissao(options, UnidadeOrcamentaria.Visualizar);
        RegistrarPermissao(options, UnidadeOrcamentaria.Gravar);

        //... outros registros
    }
```
