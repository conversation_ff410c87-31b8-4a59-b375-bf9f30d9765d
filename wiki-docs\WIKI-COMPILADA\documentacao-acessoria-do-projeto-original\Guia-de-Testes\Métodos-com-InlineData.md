[[_TOC_]]
# Como usar [InlineData] com NSubstitute

A anotação [InlineData] é uma ferramenta poderosa para parametrizar testes em xUnit, permitindo que você execute o mesmo método de teste com diferentes conjuntos de dados. Isso é especialmente útil para validar a lógica de negócios em diferentes cenários sem a necessidade de duplicar código.

No contexto do NSubstitute, você pode usar [InlineData] para testar métodos que dependem de dados variados, garantindo que seu código se comporte conforme o esperado em diferentes situações.

## Exemplo de Uso

Abaixo está um exemplo de como usar [InlineData] em um teste que verifica a lógica de transição entre etapas de processamento:

```csharp
[Theory]
[InlineData(ETipoEtapaProcessamento.Extracao, ETipoEtapaProcessamento.Enriquecimento)]
[InlineData(ETipoEtapaProcessamento.Enriquecimento, ETipoEtapaProcessamento.Importacao)]
[InlineData(ETipoEtapaProcessamento.Importacao, ETipoEtapaProcessamento.Validacao)]
[InlineData(ETipoEtapaProcessamento.Validacao, ETipoEtapaProcessamento.Envio)]
[InlineData(ETipoEtapaProcessamento.Envio, ETipoEtapaProcessamento.Finalizado)]
public async Task GetTipoProximaEtapa_Deve_Retornar_Etapa_Correta(ETipoEtapaProcessamento etapaAtual, ETipoEtapaProcessamento etapaEsperada)
{
    var result = _controleProcessamentoManager.GetTipoProximaEtapa(etapaAtual, EFaseTipo.PAGO);
    result.ShouldBe(etapaEsperada);
}
```

Neste exemplo, o método `GetTipoProximaEtapa` é testado com diferentes etapas de processamento. Cada chamada ao método de teste é feita com um conjunto diferente de dados, permitindo verificar se a lógica de transição entre etapas está correta.
