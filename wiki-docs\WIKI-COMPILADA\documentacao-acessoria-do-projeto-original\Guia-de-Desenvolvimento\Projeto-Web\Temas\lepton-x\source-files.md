# Arquivos SCSS do Lepton X

A estrutura de arquivos SCSS do LeptonX é dividida em diferentes bundles. O objetivo disso é excluir estilos desnecessários (por exemplo, os estilos da camada do menu superior ao usar o layout do menu lateral) no projeto. Para recarregar apenas arquivos relevantes em alterações de tema.

## Arquivos de Bundle

Pastas que contêm arquivos de bundle nos arquivos de origem não contêm underscores. Aquelas que contêm underscores são arquivos usados ​​pelos arquivos de bundle.

**Arquivos de bundle de tema**: dim.scss dark.scss light.scss colocados no diretório themes.

**Arquivos de bundle do Bootstrap**: dark/booststrap-dark.scss, light/booststrap-light.scss e dim/booststrap-dim.scss colocados no diretório frameworks/bootstrap.

**Arquivos de bundle de layout**: side-menu/layout-bundle.scss e top-menu/layout-bundle.scss colocados no diretório pro

**Bundles específicos de UI**: O bundle de UI Angular é pro/libraries/ng-bundle.scss, o bundle de UI Blazor é pro/libraries/blazor-bundle.scss, o bundle de UI MVC é pro/libraries/js-bundle.scss

**Arquivo de bundle ABP**: Estilos de elementos da UI do ABP é pro/abp/abp-bundle.scss

**Bundle de fontes**: pro/libraries/font-bundle.scss

## Mapa de Temas

Os mapas de temas são definidos em arquivos de cores de tema. A posição dos arquivos está listada como segue:
dark: \_colors/dark/colors.scss
light: \_colors/light/colors.scss
dim: \_colors/dim/colors.scss

As propriedades possíveis estão listadas abaixo

```
border-color,
brand,
brand-text,
card-bg,
card-title-text-color,
container-active-text,
content-bg,
content-text,
danger,
dark
info,
light
logo,
logo-icon,
logo-reverse,
navbar-active-bg-color
navbar-active-text-color
navbar-color
navbar-text-color
primary,
radius
secondary,
shadow,
success,
text-white,
warning
```

## Construtor de Temas

O mixin build-theme lê o theme-map e grava seus valores no seletor :root como variáveis CSS usando funções de builder definidas.

O global-theme-builder converte as cores rgb de valores de propriedade específicos no mapa de temas.

## Compilando para CSS

> Certifique-se de que as dependências estão instaladas. Você pode instalar com o comando `yarn install` ou `npm install`.

Para construir arquivos de origem, execute o comando abaixo

```bash
yarn build
```

Os arquivos CSS serão criados na pasta built.

## Adicionando um novo bundle de tema ao arquivo de origem

Crie um novo arquivo em \_colors/seu-tema/colors.scss e substitua o conteúdo abaixo

```scss
$theme-map: (
  light: #f0f4f7,
  dark: #062a44,
  navbar-color: #fff,
  navbar-text-color: #445f72,
  navbar-active-text-color: #124163,
  navbar-active-bg-color: #f3f6f9,
);
```

Crie um novo arquivo \_colors/seu-tema/index.scss e cole o conteúdo abaixo nele

```scss
@import "colors";
@import "../common";
```

Crie um novo arquivo frameworks/bootstrap/seu-tema/bootstrap-seu-tema.scss e cole o conteúdo abaixo nele

```scss
@import "_colors/seu-tema";
@import "../common";
```

Finalmente, crie um novo arquivo themes/seu-tema.scss e cole o conteúdo abaixo nele

```scss
@import "_colors/seu-tema";
@import "builders/_builder";
```

## Outros Arquivos

- build.js: Constrói arquivos de bundle scss em arquivos css. Também cria arquivos rtl.css.
- package.json: Inclui dependências e comando de build
- postcss.config.js: Usado pelo postcss, necessário para ltr para rtl
