# Alertas de Página

É comum mostrar alertas de erro, aviso ou informação para informar o usuário. Um exemplo de alerta de *Interrupção de Serviço* é mostrado abaixo:

![page-alert-example](/ABP-Docs/images/page-alert-example.png)

## Uso Básico

Se você herdar direta ou indiretamente de `AbpPageModel`, você pode usar a propriedade `Alerts` para adicionar alertas a serem renderizados após a conclusão da solicitação.

**Exemplo: Mostrar um alerta de Aviso**

```csharp
namespace MyProject.Web.Pages
{
    public class IndexModel : MyProjectPageModel //ou herdar de AbpPageModel
    {
        public void OnGet()
        {
            Alerts.Warning(
                text: "Teremos uma interrupção de serviço entre 02:00 AM e 04:00 AM em 23 de outubro de 2023!",
                title: "Interrupção de Serviço"
            );
        }
    }
}
```

Este uso renderiza um alerta que foi mostrado acima. Se você precisar localizar as mensagens, você sempre pode usar o sistema de [localização](/Guia-de-Desenvolvimento/Projeto-Web/Localization.md) padrão.

### Exceções / Estados de Modelo Inválidos

É típico mostrar alertas quando você lida manualmente com exceções (com instruções try/catch) ou deseja lidar com o caso `!ModelState.IsValid` e avisar o usuário. Por exemplo, o Módulo de Conta mostra um aviso se o usuário inserir um nome de usuário ou senha incorretos:

![page-alert-account-layout](/ABP-Docs/images/page-alert-account-layout.png)

> Observe que você geralmente não precisa lidar manualmente com exceções, pois o ABP Framework fornece um sistema automático de [tratamento de exceções](/Guia-de-Desenvolvimento/Projeto-Web/Exception-Handling.md).

### Tipos de Alerta

`Warning` é usado para mostrar um alerta de aviso. Outros métodos comuns são `Info`, `Danger` e `Success`.

Além dos métodos padrão, você pode usar o método `Alerts.Add` passando um `enum` `AlertType` com um desses valores: `Default`, `Primary`, `Secondary`, `Success`, `Danger`, `Warning`, `Info`, `Light`, `Dark`.

### Descartável

Todos os métodos de alerta obtêm um parâmetro `dismissible` opcional. O valor padrão é `true`, o que torna a caixa de alerta descartável. Defina-o como `false` para criar uma caixa de alerta fixa.

## IAlertManager

Se você precisar adicionar mensagens de alerta de outra parte do seu código, você pode injetar o serviço `IAlertManager` e usar sua lista `Alerts`.

**Exemplo: Injetar o `IAlertManager`**

```csharp
using Volo.Abp.AspNetCore.Mvc.UI.Alerts;
using Volo.Abp.DependencyInjection;

namespace MyProject.Web.Pages
{
    public class MyService : ITransientDependency
    {
        private readonly IAlertManager _alertManager;

        public MyService(IAlertManager alertManager)
        {
            _alertManager = alertManager;
        }

        public void Test()
        {
            _alertManager.Alerts.Add(AlertType.Danger, "Mensagem de teste!");
        }
    }
}
```

## Notas

### Requisições AJAX

O sistema de Alerta de Página foi projetado para ser usado em uma solicitação de página completa regular. Não é para solicitações AJAX/parciais. Os alertas são renderizados no layout da página, portanto, uma atualização de página completa é necessária.

Para solicitações AJAX, é mais adequado lançar exceções (por exemplo, `UserFriendlyException`). Veja o documento de [tratamento de exceções](/Guia-de-Desenvolvimento/Projeto-Web/Exception-Handling.md).
