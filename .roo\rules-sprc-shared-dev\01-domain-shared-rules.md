---
description: Regras específicas para desenvolvimento na camada Domain.Shared do TRF3.SISPREC
globs: src/TRF3.SISPREC.Domain.Shared/**/*
alwaysApply: true
---

# Regras para SISPREC Domain.Shared Developer

## **Responsabilidades Principais**
- **Tipos Compartilhados**: Manter enumerações, constantes e value objects
- **Helpers Utilitários**: Implementar classes helper reutilizáveis
- **Configurações**: Gerenciar configurações compartilhadas
- **Shared Kernel**: Seguir padrões DDD para elementos compartilhados

## **Estrutura do Projeto Domain.Shared**
- **Apoio/**: Classes helper e utilitários (CJ<PERSON><PERSON><PERSON><PERSON>, CPF<PERSON>elper, DateTimeHelper, etc.)
- **Assuntos/**: Constantes relacionadas a assuntos jurídicos
- **BackgroundServices/**: Constantes para serviços em background
- **Enums/**: Enumerações de domínio do sistema
- **Localization/**: Recursos de localização
- **OcorrenciaMotivos/**: Constantes de motivos de ocorrência

## **Padrões de Desenvolvimento**
- **Value Objects**: Implementar através de enumerações com comportamento
- **Helpers Estáticos**: Criar classes helper estáticas para operações utilitárias
- **Constantes**: Centralizar constantes em classes específicas por contexto
- **Validações**: Implementar validações reutilizáveis nos helpers

## **Diretrizes de Qualidade**
- **Reutilização**: Garantir que todos os elementos sejam reutilizáveis
- **Documentação**: Documentar adequadamente helpers e constantes
- **Testes**: Criar testes unitários para helpers complexos
- **Performance**: Otimizar helpers para alta performance

## **Impacto nas Camadas**
- **Domain**: Utiliza enumerações e value objects
- **Application**: Consome helpers e constantes
- **Infrastructure**: Usa configurações e utilitários
- **Web**: Aplica helpers de formatação e validação

## **Tecnologias e Frameworks**
- **ABP Framework 8**: Módulo base e configurações
- **C# 12**: Recursos modernos da linguagem
- **.NET 8**: Framework base
- **System.ComponentModel.DataAnnotations**: Validações

## **Boas Práticas**
- Sempre considerar o impacto em todas as camadas dependentes
- Manter compatibilidade com versões anteriores
- Implementar testes para helpers críticos
- Seguir convenções de nomenclatura do projeto
- Documentar mudanças que afetam outras camadas
