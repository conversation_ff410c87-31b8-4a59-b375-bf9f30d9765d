# Guia de Testes para Serviços de Domínio

[[_TOC_]]

## 1. Introdução

Este guia demonstra como implementar testes unitários para serviços de domínio no SISPREC, fornecendo exemplos práticos e padrões recomendados.

### 1.1 Estrutura Básica

Os testes de serviços de domínio são organizados no projeto `TRF3.SISPREC.Domain.Tests` e seguem uma estrutura consistente:

```csharp
public class MeuServicoTests : SISPRECDomainTestBase<SISPRECTestBaseModule>
{
    // Mocks dos repositórios e serviços
    private readonly IRepositorio _repositorio;
    private readonly IServicoDependencia _servicoDependencia;
    
    // Instância do serviço sendo testado (SUT - System Under Test)
    private readonly MeuServico _servico;

    public MeuServicoTests()
    {
        // Inicialização dos mocks e do serviço
        _repositorio = Substitute.For<IRepositorio>();
        _servicoDependencia = Substitute.For<IServicoDependencia>();
        _servico = new MeuServico(_repositorio, _servicoDependencia);
    }

    // Métodos de teste...
}
```

## 2. Ferramentas e Bibliotecas

### 2.1 NSubstitute para Mocks

O NSubstitute é usado para criar mocks de dependências. Exemplos de uso:

```csharp
// Mock básico
var repositorio = Substitute.For<IRepositorio>();

// Configurar retorno
repositorio.GetAsync(1).Returns(new Entidade { Id = 1 });

// Verificar chamada
await repositorio.Received().GetAsync(1);

// Mock de exceção
repositorio.GetAsync(1).Throws(new Exception("erro"));

// Mock de queryable
var lista = new List<Entidade>().AsQueryable().BuildMock();
repositorio.GetQueryableAsync().Returns(lista);
```

### 2.2 Shouldly para Asserções

O Shouldly oferece asserções mais legíveis e mensagens de erro mais claras:

```csharp
// Verificar valor
resultado.ShouldBe(valorEsperado);

// Verificar nulo
objeto.ShouldNotBeNull();

// Verificar exceção
var exception = await Should.ThrowAsync<TipoExcecao>(async () => 
    await _servico.MetodoAsync());

// Verificar coleções
lista.ShouldBeEmpty();
lista.ShouldContain(item);
```

### 2.3 Bogus para Dados de Teste

A biblioteca Bogus é usada para gerar dados de teste consistentes:

```csharp
private Entidade GerarEntidadeValida()
{
    return new Faker<Entidade>()
        .RuleFor(e => e.Id, f => f.Random.Int(1, 100))
        .RuleFor(e => e.Nome, f => f.Random.Hash()) // Preferir Hash() ao invés de String()
        .RuleFor(e => e.Email, f => f.Internet.Email())
        .RuleFor(e => e.DataCadastro, f => f.Date.Past())
        .Generate();
}
```

## 3. Padrões de Testes

### 3.1 Estrutura Arrange-Act-Assert

Todos os testes devem seguir o padrão AAA:

```csharp
[Fact]
public async Task Validar_Operacao_Deve_Passar()
{
    // Arrange - Preparação do cenário
    var entidade = GerarEntidadeValida();
    _repositorio.GetAsync(1).Returns(entidade);

    // Act - Execução da operação
    var resultado = await _servico.ValidarAsync(1);

    // Assert - Verificação do resultado
    resultado.ShouldBeTrue();
    await _repositorio.Received(1).GetAsync(1);
}
```

### 3.2 Nomenclatura de Testes

Os testes devem ter nomes descritivos seguindo o padrão:

```csharp
public async Task Nome_Do_Metodo_Deve_Fazer_Algo_Quando_Algo()
```

Exemplos:
- `Validar_Exclusao_Deve_Passar_Quando_Sem_Dependencias()`
- `Criar_Entidade_Deve_Falhar_Quando_Nome_Invalido()`

## 4. Casos de Uso e Exemplos

### 4.1 Teste de Validação de Dados

```csharp
[Fact]
public async Task Validar_Cpf_Deve_Falhar_Quando_Formato_Invalido()
{
    // Arrange
    var cpfInvalido = "123";
    var parte = new RequisicaoParte 
    { 
        Pessoa = new Pessoa { NumeroCnpjCpf = cpfInvalido } 
    };

    // Act
    var action = () => _verificacaoManager.ValidarCpf(parte);

    // Assert
    await action.ShouldThrowAsync<ArgumentException>();
}
```

### 4.2 Teste de Regras de Negócio

```csharp
[Fact]
public async Task Excluir_Autor_Deve_Falhar_Quando_Possui_Livros()
{
    // Arrange
    var autor = GerarAutorValido();
    _validarExcluirAutorService
        .Validar(Arg.Any<Autor>())
        .ThrowsAsync(new UserFriendlyException("Não é possível excluir Autor com Livro vinculado."));

    // Act & Assert
    var exception = await Should.ThrowAsync<UserFriendlyException>(async () =>
    {
        await _autorManager.ExcluirAsync(autor);
    });

    exception.Message.ShouldContain("Não é possível excluir Autor com Livro vinculado.");
}
```

### 4.3 Teste de Fluxo Completo

```csharp
[Fact]
public async Task Importar_Precatorio_Deve_Processar_Com_Sucesso()
{
    // Arrange
    var controle = CriarControleProcessamento();
    var precatorios = CriarListaPrecatorios(controle, 5);
    
    _controleProcessamentoRepository
        .ListarPendentesImportacao()
        .Returns(new List<ControleProcessamento> { controle }.AsQueryable().BuildMock());

    _serviceProvider
        .GetService<IControleProcessamentoRepository>()
        .ObterPrecatoriosOrigem(controle.Fase.TipoPrecatorio, controle.Id)
        .Returns(precatorios.AsQueryable().BuildMock());

    // Act
    await _importarPrecatorioService.Executar();

    // Assert
    await _controleProcessamentoManager
        .Received(1)
        .IniciarProcessamento(controle.Id);
        
    await _hubContext
        .Received()
        .Clients.All.SendAsync(
            ControleStatusHub.NomeEvento, 
            "100.00%", 
            controle.Id, 
            true);
}
```

### 4.4 Teste de Tratamento de Exceções

```csharp
[Fact]
public async Task Executar_Deve_Registrar_Erro_Quando_Falha()
{
    // Arrange
    _consultaCPFService
        .ConsultarDadosCpfAsync(Arg.Any<string>())
        .Throws(new Exception("Erro na consulta"));

    // Act
    var action = () => _verificacaoManager.ValidarCpf(parte);

    // Assert
    await action.ShouldThrowAsync<Exception>();
    _logger.Received().LogError(
        Arg.Any<Exception>(), 
        "Erro ao consultar CPF");
}
```

### 4.5 Teste de Expressões Lambda com Compile()

Ao testar métodos que recebem expressões lambda como parâmetros (comum em repositórios), podemos usar o método `Compile()` para verificar se a expressão está correta. Isso é particularmente útil quando precisamos testar se um método está sendo chamado com os parâmetros corretos em uma expressão lambda.

```csharp
[Fact]
public async Task Deve_Verificar_Indicador_Economico_Correto()
{
    // Arrange
    var codigoIndicador = "xxx";
    
    _tipoIndicadorEconomicoRepository
        .FirstOrDefaultAsync(Arg.Is<Expression<Func<IndicadorEconomicoTipo, bool>>>(
            expr => expr.Compile().Invoke(new IndicadorEconomicoTipo { Codigo = codigoIndicador })
        ))
        .Returns((IndicadorEconomicoTipo)null);

    // Act & Assert
    var exception = await Should.ThrowAsync<UserFriendlyException>(
        async () => await _manager.ValidarIndicadorEconomico(codigoIndicador)
    );
    
    exception.Message.ShouldContain($"Não foi possível encontrar um tipo de indicador econômico com o código {codigoIndicador}");
}
```

#### 4.5.1 Quando Usar Compile()

Use `Compile()` quando precisar:

1. Verificar o comportamento exato de uma expressão lambda
2. Testar se um método está sendo chamado com os parâmetros corretos em uma expressão
3. Validar a lógica dentro de uma expressão lambda
4. Simular diferentes resultados baseados no conteúdo da expressão

#### 4.5.2 Exemplo Prático

```csharp
[Fact]
public async Task Validar_Parametros_Especificos_Em_Lambda()
{
    // Arrange
    var entidade = new MinhaEntidade { Codigo = "ABC", Valor = 100 };
    
    _repositorio
        .FirstOrDefaultAsync(Arg.Is<Expression<Func<MinhaEntidade, bool>>>(
            expr => expr.Compile().Invoke(new MinhaEntidade 
            { 
                Codigo = "ABC",
                Valor = 100 
            })
        ))
        .Returns(entidade);

    // Act
    var resultado = await _service.BuscarPorCriterios("ABC", 100);

    // Assert
    resultado.ShouldNotBeNull();
    resultado.Codigo.ShouldBe("ABC");
    resultado.Valor.ShouldBe(100);
}
```

#### 4.5.3 Dicas Importantes

1. **Desempenho**: O uso de `Compile()` tem um custo de performance, mas isso é aceitável em testes unitários
2. **Legibilidade**: Mantenha as expressões lambda simples e focadas no que está sendo testado
3. **Manutenibilidade**: Documente o propósito do teste quando usar `Compile()` para validações complexas
4. **Precisão**: Use `Compile()` apenas quando precisar validar o comportamento exato da expressão

### 4.6 Configuração Condicional com When()

O NSubstitute oferece o método `When()` para configurar comportamentos condicionais em mocks. Isso é particularmente útil quando precisamos que um mock responda de maneiras diferentes baseado em condições específicas.

#### 4.6.1 Casos de Uso

1. **Retornos Condicionais**: Configurar diferentes retornos baseados em condições
2. **Simulação de Cenários**: Testar diferentes fluxos do sistema
3. **Validação de Parâmetros**: Verificar se os parâmetros corretos estão sendo passados
4. **Testes de Fluxos Complexos**: Simular comportamentos em cenários com múltiplas condições

#### 4.6.2 Exemplo Básico

```csharp
[Fact]
public async Task Deve_Retornar_Diferente_Baseado_Na_Condicao()
{
    // Arrange
    var repository = Substitute.For<IRepository>();
    
    repository.When(x => x.GetAsync(Arg.Is<int>(id => id > 0)))
        .Do(x => Task.FromResult(new Entidade { Id = 1 }));
        
    repository.When(x => x.GetAsync(Arg.Is<int>(id => id <= 0)))
        .Do(x => throw new ArgumentException("Id inválido"));

    // Act & Assert
    await repository.GetAsync(1); // Retorna entidade
    await Should.ThrowAsync<ArgumentException>(() => repository.GetAsync(0));
}
```

#### 4.6.3 Exemplo com Múltiplas Condições

```csharp
[Fact]
public async Task Deve_Processar_Baseado_Em_Multiplas_Condicoes()
{
    // Arrange
    var service = Substitute.For<IImportacaoService>();
    
    service.When(x => 
        x.ProcessarAsync(
            Arg.Is<ImportacaoRequest>(r => 
                r.Status == EStatus.PENDENTE && 
                r.DataImportacao.Date == DateTime.Today
            )
        )
    ).Do(x => Task.FromResult(new ImportacaoResult { Sucesso = true }));

    service.When(x => 
        x.ProcessarAsync(
            Arg.Is<ImportacaoRequest>(r => 
                r.Status != EStatus.PENDENTE || 
                r.DataImportacao.Date != DateTime.Today
            )
        )
    ).Do(x => throw new BusinessException("Importação inválida"));

    // Act & Assert
    var request1 = new ImportacaoRequest 
    { 
        Status = EStatus.PENDENTE, 
        DataImportacao = DateTime.Today 
    };
    var result = await service.ProcessarAsync(request1); // Sucesso

    var request2 = new ImportacaoRequest 
    { 
        Status = EStatus.PROCESSADO, 
        DataImportacao = DateTime.Today 
    };
    await Should.ThrowAsync<BusinessException>(() => 
        service.ProcessarAsync(request2));
}
```

#### 4.6.4 Exemplo com Callbacks

```csharp
[Fact]
public async Task Deve_Executar_Callback_Quando_Condicao_Satisfeita()
{
    // Arrange
    var processados = new List<string>();
    var service = Substitute.For<IProcessamentoService>();
    
    service.When(x => 
        x.ProcessarAsync(Arg.Any<string>())
    ).Do(x => {
        var protocolo = x.Arg<string>();
        processados.Add(protocolo);
        return Task.CompletedTask;
    });

    // Act
    await service.ProcessarAsync("PROC001");
    await service.ProcessarAsync("PROC002");

    // Assert
    processados.Count.ShouldBe(2);
    processados.ShouldContain("PROC001");
    processados.ShouldContain("PROC002");
}
```

#### 4.6.5 Dicas de Uso do When()

1. **Clareza**: Mantenha as condições claras e específicas
   ```csharp
   // Bom - Condição clara e específica
   service.When(x => x.GetAsync(Arg.Is<int>(id => id > 0)))
   
   // Evite - Condição complexa e difícil de entender
   service.When(x => x.GetAsync(Arg.Is<int>(id => 
       id > 0 && id % 2 == 0 && someComplexLogic(id))))
   ```

2. **Organização**: Agrupe condições relacionadas
   ```csharp
   // Configurar todas as condições relacionadas juntas
   repository.When(x => x.GetStatus() == Status.Ativo)
       .Do(x => ProcessarAtivo());
   repository.When(x => x.GetStatus() == Status.Inativo)
       .Do(x => ProcessarInativo());
   ```

3. **Reutilização**: Extraia condições comuns
   ```csharp
   private static bool IsValidRequest(ImportacaoRequest request) =>
       request.Status == EStatus.PENDENTE && 
       request.DataImportacao.Date == DateTime.Today;

   // Uso
   service.When(x => x.ProcessarAsync(
       Arg.Is<ImportacaoRequest>(r => IsValidRequest(r))
   )).Do(...);
   ```

4. **Verificação**: Use em conjunto com Received() para validar chamadas
   ```csharp
   // Arrange
   service.When(x => x.ProcessarAsync(Arg.Any<string>()))
       .Do(x => Task.CompletedTask);

   // Act
   await service.ProcessarAsync("teste");

   // Assert
   await service.Received()
       .ProcessarAsync(Arg.Is<string>(s => s == "teste"));
   ```

### 4.7 Configuração de Logger com DomainServiceTestExtensions

A classe `DomainServiceTestExtensions` resolve o problema de configuração de loggers mockados para testes de serviços de domínio. Em serviços que herdam de `DomainService`, o logger é obtido através do `LazyServiceProvider`, o que dificulta o mock direto da dependência.

#### 4.7.1 Problema Resolvido

Quando testamos classes que herdam de `DomainService`, precisamos:
1. Criar um mock do logger
2. Configurar o `LazyServiceProvider` mockado para retornar este logger
3. Injetar este provider no serviço sendo testado

A classe `DomainServiceTestExtensions` encapsula essa lógica complexa em um único método de extensão.

#### 4.7.2 Como Usar

```csharp
// 1. Declare o logger mockado nos seus testes
private readonly ILogger<MeuServicoDedominio> _logger;
private readonly MeuServicoDedominio _service;

public MeuServicoDeDominioTests()
{
    // 2. Crie os mocks necessários e a instância do serviço
    _logger = Substitute.For<ILogger<MeuServicoDedominio>>();
    // ... outros mocks ...
    
    _service = new MeuServicoDedominio(/* dependências */);
    
    // 3. Configure o logger mockado no serviço com uma única linha
    _service.ConfigurarLoggerMockado(_logger);
}

[Fact]
public async Task Metodo_Deve_Logar_Erro_Quando_Falha()
{
    // Act
    await _service.MetodoQueLoga();
    
    // Assert
    // Você pode verificar se o logger foi chamado com os parâmetros corretos
    _logger.Received(1).LogError(
        Arg.Any<Exception>(),
        "Mensagem de erro esperada"
    );
}
```

#### 4.7.3 Exemplo Prático

```csharp
public class RequisicaoDocumentoServiceTests : SISPRECDomainTestBase<SISPRECDomainTestModule>
{
    private readonly IRequisicaoDocumentoRepository _repository;
    private readonly ILogger<RequisicaoDocumentoService> _logger;
    private readonly RequisicaoDocumentoService _service;

    public RequisicaoDocumentoServiceTests()
    {
        _repository = Substitute.For<IRequisicaoDocumentoRepository>();
        _logger = Substitute.For<ILogger<RequisicaoDocumentoService>>();
        
        // Outros mocks e inicialização do serviço...
        _service = new RequisicaoDocumentoService(...);
        
        // Configura o logger mockado no serviço
        _service.ConfigurarLoggerMockado(_logger);
    }

    [Fact]
    public async Task GerarEspelhoRequisicaoAsync_NumeroRequisicaoNuloOuVazio_DeveLancarArgumentException()
    {
        // Arrange
        var numeroRequisicao = "";
        
        // Act
        var act = async () => await _service.GerarEspelhoRequisicaoAsync(numeroRequisicao, "obs", "proc");
        
        // Assert
        await Should.ThrowAsync<ArgumentException>(act);
        
        // Verifica se o logger foi chamado com a mensagem de erro correta
        _logger.Received(1).LogError(
            Arg.Any<Exception>(),
            "Erro ao gerar espelho da requisição - Número da requisição não informado."
        );
    }
}
```

#### 4.7.4 Benefícios

1. <u>Simplificação</u>: Elimina código boilerplate para configurar mocks de logger
2. <u>Consistência</u>: Padroniza a forma como os loggers são mockados em testes
3. <u>Manutenibilidade</u>: Centraliza a lógica de mock em um único lugar
4. <u>Testabilidade</u>: Facilita a verificação de chamadas ao logger
5. <u>Legibilidade</u>: Torna os testes mais limpos e focados na lógica de negócios

## 5. Boas Práticas

### 5.1 Mocks e Dependências

- Use `Substitute.For<T>()` para criar mocks
- Configure apenas o comportamento necessário para o teste
- Evite mocks complexos - prefira dividir em testes menores
- Verifique chamadas importantes com `Received()`

### 5.2 Dados de Teste

- Use Bogus para gerar dados consistentes
- Crie métodos auxiliares para dados comuns
- Prefira `Random.Hash()` para strings
- Mantenha os dados de teste relevantes para o cenário

### 5.3 Asserções

- Use Shouldly para asserções mais legíveis
- Verifique apenas o que é relevante para o teste
- Inclua mensagens claras nas asserções
- Teste tanto casos de sucesso quanto de falha

### 5.4 Organização

- Agrupe testes relacionados em regiões (#region)
- Mantenha métodos auxiliares em região separada
- Use comentários para explicar cenários complexos
- Siga a convenção de nomes do projeto

### 5.5 Dicas Importantes

1. **Isolamento**: Cada teste deve ser independente
2. **Foco**: Teste uma única funcionalidade por vez
3. **Clareza**: Nomes e estrutura devem ser auto-explicativos
4. **Manutenibilidade**: Evite duplicação de código
5. **Cobertura**: Teste casos de borda e exceções

## 6. Exemplos Adicionais

Consulte os seguintes arquivos para mais exemplos práticos:

- `AutorManagerTests.cs`: Testes de gerenciador de domínio
- `ValidarExcluirAutorServiceTests.cs`: Testes de serviço de validação
- `ImportarPrecatorioAutomatizadoTests.cs`: Testes de processamento complexo
- `VerificacaoCnpjCpfManagerTests.cs`: Testes de validação de documentos
