# Modos Personalizados SISPREC - Configuração RooCode

## Visão Geral

Este documento descreve os modos personalizados criados especificamente para o projeto TRF3.SISPREC, implementando a metodologia BMAD (Business-Minded Agile Development) adaptada para desenvolvimento de sistemas judiciais.

## Arquivos de Configuração

### `.roomodes` (Formato JSON)

Arquivo principal contendo a definição dos modos personalizados específicos do projeto SISPREC. Utiliza formato JSON conforme solicitado, ao invés do formato YAML padrão do RooCode.

### Diretórios de Regras (`.roo/rules-{slug}/`)

Cada modo possui um diretório específico com regras detalhadas:

-   `.roo/rules-sisprec-orchestrator/` - Regras do orquestrador
-   `.roo/rules-sisprec-arquiteto/` - Regras do arquiteto
-   `.roo/rules-sisprec-backend/` - Regras do desenvolvedor backend
-   `.roo/rules-sisprec-frontend/` - Regras do desenvolvedor frontend
-   `.roo/rules-sisprec-tester/` - Regras do especialista em testes

## Padrões de Codificação e Convenções

**Todos os Modos Personalizados devem seguir estritamente os padrões de codificação e convenções definidos no arquivo `data/sisprec-coding-standards.md`.** Este documento é a fonte central de verdade para as boas práticas de desenvolvimento no projeto TRF3.SISPREC.

## Modos Personalizados Implementados

### 1. 🎯 Orquestrador SISPREC (`sisprec-orchestrator`)

**Propósito**: Coordenação de equipe especializada para desenvolvimento eficiente do sistema SISPREC.

**Características**:

-   Acesso completo a todas as ferramentas (read, edit, command, browser)
-   Conhecimento profundo do domínio judiciário brasileiro
-   Coordenação de agentes especializados
-   Garantia de qualidade e aderência a padrões

**Quando usar**: Para coordenar desenvolvimento de funcionalidades complexas, gerenciar equipe de agentes especializados, e garantir aderência aos padrões estabelecidos.

### 2. 🏗️ Arquiteto SISPREC (`sisprec-arquiteto`)

**Propósito**: Especialista em arquitetura ABP Framework 8, Clean Architecture, DDD e sistema TRF3.SISPREC.

**Características**:

-   Acesso a arquivos C#, JSON e Markdown
-   Expertise em ABP Framework 8 e padrões DDD
-   Design de integrações com serviços externos
-   Validação de aderência a padrões arquiteturais

**Quando usar**: Para análise arquitetural, design de soluções, definição de padrões técnicos, revisão de arquitetura e especificações técnicas detalhadas.

### 3. ⚙️ Dev Backend SISPREC (`sisprec-backend`)

**Propósito**: Especialista em desenvolvimento backend para todas as camadas do sistema SISPREC.

**Características**:

-   Acesso a arquivos C# e JSON
-   Implementação de entidades de domínio seguindo DDD
-   Criação de application services e repositórios
-   Desenvolvimento de integrações com serviços externos

**Quando usar**: Para implementação de camadas Domain, Application, Infrastructure, EntityFrameworkCore e HttpApi do sistema SISPREC.

### 4. 🎨 Dev Frontend SISPREC (`sisprec-frontend`)

**Propósito**: Especialista em desenvolvimento frontend para o sistema TRF3.SISPREC.

**Características**:

-   Acesso a arquivos web (Razor, C#, JS, CSS, JSON)
-   Criação de Razor Pages seguindo padrões SISPREC
-   Implementação de formulários com validações
-   Design responsivo e acessibilidade

**Quando usar**: Para desenvolvimento da camada TRF3.SISPREC.Web, criação de interfaces de usuário, formulários, listagens e componentes frontend.

### 5. 🧪 Testador SISPREC (`sisprec-tester`)

**Propósito**: Especialista em testes automatizados para o sistema TRF3.SISPREC.

**Características**:

-   Acesso a arquivos C# e JSON de teste
-   Implementação de testes unitários e de integração
-   Testes end-to-end com Selenium
-   Geração de relatórios de cobertura

**Quando usar**: Para implementação de testes automatizados, validação de qualidade, geração de relatórios de cobertura e garantia de qualidade do código.

## Contexto Específico do Projeto

### Domínio SISPREC

-   **Sistema**: Gestão de precatórios e RPVs do TRF3
-   **Entidades principais**: RequisicaoProtocolo, Processo, Beneficiario, Proposta, Parcela
-   **Integrações**: CJF, SEI, MinIO, sistemas legados UFEP
-   **Compliance**: Regulamentações CNJ, normas TRF3

### Arquitetura Técnica

-   **Framework**: ABP Framework 8 com .NET 8
-   **Padrões**: Clean Architecture + DDD
-   **Banco**: SQL Server com Entity Framework Core
-   **Frontend**: Razor Pages + Bootstrap 5 + ABP Tag Helpers
-   **Cache**: Redis
-   **Arquivos**: MinIO S3-compatible

### Regras de Negócio Críticas

-   Validação de fases: verificar se fase atual está finalizada
-   Controle de propostas fechadas: não criar verificações para propostas fechadas
-   Usar UserFriendlyException para validações de negócio

### Padrões UI/UX

-   Preferir formulários normais ao invés de abp-dynamic-form
-   Controles de filtro devem caber em uma linha sem quebra
-   Usar classes Bootstrap 5 e atributos ABP tag helper
-   Criar permissões de visualização para controle de menu

## Comandos Específicos Implementados

### Análise e Design

-   `/analyze-entity {name}` - Análise completa de entidade
-   `/review-architecture` - Revisão da arquitetura
-   `/validate-patterns` - Verificação de aderência a padrões

### Desenvolvimento

-   `/create-crud {entity}` - Criar CRUD completo seguindo padrões SISPREC
-   `/implement-entity {name}` - Implementar entidade de domínio
-   `/create-appservice {name}` - Criar application service
-   `/implement-repository {name}` - Implementar repositório
-   `/create-page {name}` - Criar nova Razor Page
-   `/implement-form {entity}` - Implementar formulário
-   `/create-listing {entity}` - Criar listagem com filtros
-   **Nota**: Não há comando para controllers (HttpApi não é usado)

### Testes e Qualidade

-   `/create-unit-tests {class}` - Criar testes unitários
-   `/create-integration-tests {component}` - Criar testes de integração
-   `/create-e2e-tests {workflow}` - Criar testes end-to-end
-   `/generate-coverage-report` - Gerar relatório de cobertura
-   `/check-patterns` - Verificar aderência aos padrões ABP/DDD
-   `/validate-business-rules` - Verificar implementação de regras de negócio
-   **Nota**: Não há comando para testes de API (HttpApi não é usado)

## Fluxos de Trabalho BMAD

### Desenvolvimento de Nova Entidade

1. **Arquiteto**: Análise de requisitos e design da entidade
2. **Dev-Backend**: Implementação Domain → Application → Infrastructure → API
3. **Dev-Frontend**: Criação de interface (páginas, formulários, listagens)
4. **Testador**: Testes abrangentes em todas as camadas
5. **Arquiteto**: Revisão final e documentação

### Correção de Bug

1. **Testador**: Reprodução do bug e criação de teste
2. **Dev-Backend/Frontend**: Resolução do problema
3. **Testador**: Validação da correção
4. **Arquiteto**: Revisão de impacto (se necessário)

### Implementação de Nova Funcionalidade

1. **Arquiteto**: Design da solução e definição de componentes
2. **Dev-Backend**: Implementação da lógica de negócio
3. **Dev-Frontend**: Desenvolvimento da interface de usuário
4. **Testador**: Testes de integração e end-to-end
5. **Arquiteto**: Revisão final e documentação

## Critérios de Qualidade

### Antes da Conclusão de Tarefas

-   [ ] Código segue padrões estabelecidos do SISPREC
-   [ ] Testes implementados e passando
-   [ ] Documentação atualizada
-   [ ] Code review realizado
-   [ ] Integração com sistema existente validada
-   [ ] Performance não degradada
-   [ ] Segurança e auditoria implementadas

## Vantagens dos Modos Personalizados SISPREC

### Especialização

-   Cada modo é otimizado para tarefas específicas do projeto
-   Conhecimento profundo do domínio judiciário brasileiro
-   Padrões técnicos específicos do ABP Framework 8

### Segurança

-   Restrições de acesso a arquivos por tipo
-   Controle granular de ferramentas disponíveis
-   Validações específicas do domínio

### Eficiência

-   Comandos específicos para tarefas comuns do SISPREC
-   Fluxos de trabalho otimizados para desenvolvimento judicial
-   Coordenação automática entre agentes especializados

### Qualidade

-   Validações automáticas de padrões arquiteturais
-   Verificações de regras de negócio específicas
-   Testes abrangentes em todas as camadas

## Uso Recomendado

1. **Inicie sempre com o Orquestrador BMAD** para coordenação geral
2. **Use agentes específicos** para tarefas especializadas
3. **Siga os fluxos de trabalho** definidos para cada tipo de tarefa
4. **Valide qualidade** antes de concluir qualquer implementação
5. **Documente decisões** arquiteturais importantes

---

**Nota**: Estes modos personalizados são específicos para o projeto TRF3.SISPREC e incorporam conhecimento especializado do domínio judiciário brasileiro, padrões ABP Framework 8 e metodologia BMAD adaptada.
