# Input Dinheiro Tag Helper

O `input-dinheiro` é um Tag Helper customizado do SISPREC que simplifica a criação de campos de entrada para valores monetários em Reais (R$).

## Características

- Formatação automática com separador de milhar (.) e decimal (,)
- Mantém sempre 2 casas decimais
- Alinhamento à direita do texto
- Símbolo da moeda (R$) configurável
- Validação client-side integrada
- Compatível com Bootstrap 5
- Suporte a binding com o modelo

## Como Usar

### Uso Básico

```cshtml
<input-dinheiro asp-for="ViewModel.Preco" />
```

Este exemplo criará um campo de entrada formatado para valores monetários com:
- Label automático baseado no nome da propriedade
- Símbolo R$ à esquerda
- Formatação automática ao digitar
- Binding com a propriedade do modelo

### Sem Sí<PERSON><PERSON>

```cshtml
<input-dinheiro asp-for="ViewModel.Preco" com-simbolo-moeda="false" />
```

### Com Validação Obrigatória

```cshtml
<input-dinheiro asp-for="ViewModel.Preco" required />
```

### Campo Desabilitado

```cshtml
<input-dinheiro asp-for="ViewModel.Preco" disabled />
```

## Exemplo Completo

```cshtml
<abp-row>
    <abp-column size="_4">
        <input-dinheiro 
            asp-for="ViewModel.Preco"
            required
            com-simbolo-moeda="true" />
    </abp-column>
</abp-row>
```

## Estrutura HTML Gerada

O Tag Helper gera a seguinte estrutura HTML:

```html
<div class="mb-3">
    <label for="Preco" class="form-label">Preço</label>
    <div class="input-group">
        <span class="input-group-text">R$</span>
        <input type="text" 
               id="Preco_mascara"
               class="form-control input-dinheiro-mascara text-end"
               value="0,00" />
    </div>
    <input type="hidden" 
           id="Preco"
           name="Preco"
           value="0.00" />
</div>
```

## Funcionalidades

1. **Formatação Automática**
   - Adiciona separador de milhar automaticamente
   - Mantém sempre 2 casas decimais
   - Formata o valor enquanto o usuário digita

2. **Validação**
   - Integração com validação client-side do ASP.NET Core
   - Suporte a campo obrigatório
   - Mensagens de erro customizadas

3. **UX Aprimorada**
   - Cursor sempre posicionado no final do campo
   - Prevenção de entrada de caracteres inválidos
   - Formatação instantânea

4. **Binding com Modelo**
   - Input hidden para binding correto com decimal no modelo
   - Conversão automática entre formato BR e formato do modelo

## Propriedades

| Propriedade | Tipo | Padrão | Descrição |
|-------------|------|---------|-----------|
| asp-for | ModelExpression | (obrigatório) | Expressão do modelo para binding |
| com-simbolo-moeda | bool | true | Exibe ou oculta o símbolo R$ |
| required | bool | false | Define se o campo é obrigatório |
| disabled | bool | false | Define se o campo está desabilitado |

## Dependências

O Tag Helper requer:
- jQuery
- Bootstrap 5
- ABP Framework

## Exemplo em Contexto Real

No SISPREC, o Tag Helper é usado no cadastro de livros para o campo de preço:

```cshtml
@* Modal de criação de livro *@
<form id="NovoLivroForm" method="post">
    <abp-modal-body>
        <abp-row>
            <abp-column size="_4">
                <input-dinheiro asp-for="ViewModel.Preco" required />
            </abp-column>
        </abp-row>
    </abp-modal-body>
</form>
```

## Boas Práticas

1. **Validação**
   - Sempre use validação server-side além da client-side
   - Defina valores mínimos/máximos quando apropriado

2. **Layout**
   - Use dentro de grids responsivos (abp-row/abp-column)
   - Mantenha consistência no uso do símbolo da moeda

3. **Acessibilidade**
   - Forneça labels descritivos
   - Use mensagens de validação claras

## Considerações de Performance

- O script é carregado uma vez por instância
- Validação e formatação acontecem no cliente
- Binding otimizado com o modelo