# Proxies de Cliente JavaScript Dinâmicos

É típico consumir suas APIs HTTP a partir do seu código JavaScript. Para fazer isso, você normalmente lida com chamadas AJAX de baixo nível, como $.ajax, ou melhor [abp.ajax](JavaScript-API/Ajax.md). O ABP Framework fornece **uma maneira melhor** de chamar suas APIs HTTP a partir do seu código JavaScript: Proxies de Cliente JavaScript!

## Proxies de Cliente JavaScript Estáticos vs Dinâmicos

O ABP fornece **dois tipos** de sistema de geração de proxies de cliente. Este documento explica os **proxies de cliente dinâmicos**, que geram proxies do lado do cliente em tempo de execução. Você também pode ver a documentação de [Proxies de Cliente JavaScript Estáticos](Static-JavaScript-Proxies.md) para aprender como gerar proxies em tempo de desenvolvimento.

A geração de proxies de cliente em tempo de desenvolvimento (estática) tem uma **leve vantagem de desempenho** já que não precisa obter a definição da API HTTP em tempo de execução. No entanto, você deve **re-gerar** o código do proxy do cliente sempre que alterar a definição do endpoint da API. Por outro lado, os proxies de cliente dinâmicos são gerados em tempo de execução e proporcionam uma **experiência de desenvolvimento mais fácil**.

## Um Exemplo Rápido

Assuma que você tem um serviço de aplicação definido como mostrado abaixo:

````csharp
using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Acme.BookStore.Authors
{
    public interface IAuthorAppService : IApplicationService
    {
        Task<AuthorDto> GetAsync(Guid id);

        Task<PagedResultDto<AuthorDto>> GetListAsync(GetAuthorListDto input);

        Task<AuthorDto> CreateAsync(CreateAuthorDto input);

        Task UpdateAsync(Guid id, UpdateAuthorDto input);

        Task DeleteAsync(Guid id);
    }
}
````

> Você pode seguir o [tutorial de desenvolvimento de aplicação web](../../Tutorials/Part-1.md) para aprender como criar [serviços de aplicação](../../Application-Services.md), expô-los como [APIs HTTP](../../API/Auto-API-Controllers.md) e consumir a partir do código JavaScript como um exemplo completo.

Você pode chamar qualquer um dos métodos como se estivesse chamando uma função JavaScript. A função JavaScript tem o mesmo **nome**, **parâmetros** e o **valor de retorno** que o método C#.

**Exemplo: Obter a lista de autores**

````js
acme.bookStore.authors.author.getList({
  maxResultCount: 10
}).then(function(result){
  console.log(result.items);
});
````

**Exemplo: Excluir um autor**

```js
acme.bookStore.authors.author
    .delete('7245a066-5457-4941-8aa7-3004778775f0') //Obter id de algum lugar!
    .then(function() {
        abp.notify.info('Excluído com sucesso!');
    });
```

## Detalhes do AJAX

As funções de proxy do cliente JavaScript usam o [abp.ajax](JavaScript-API/Ajax.md) por baixo dos panos. Então, você tem os mesmos benefícios como **tratamento de erro automático**. Além disso, você pode controlar totalmente a chamada AJAX fornecendo as opções.

### O Valor de Retorno

Cada função retorna um [objeto Deferred](https://api.jquery.com/category/deferred-object/). Isso significa que você pode encadear com `then` para obter o resultado, `catch` para tratar o erro, `always` para executar uma ação uma vez que a operação seja concluída (com sucesso ou falha).

### Opções de AJAX

Cada função recebe um **último parâmetro adicional** após seus próprios parâmetros. O último parâmetro é chamado de `ajaxParams`. É um objeto que substitui as opções de AJAX.

**Exemplo: Definir opções de AJAX `type` e `dataType`**

````js
acme.bookStore.authors.author
    .delete('7245a066-5457-4941-8aa7-3004778775f0', {
        type: 'POST',
        dataType: 'xml'
    })
    .then(function() {
        abp.notify.info('Excluído com sucesso!');
    });
````

Veja a documentação do [jQuery.ajax](https://api.jquery.com/jQuery.ajax/) para todas as opções disponíveis.

## Endpoint do Script de Proxy de Serviço

A mágica é feita pelo endpoint `/Abp/ServiceProxyScript` definido pelo ABP Framework e adicionado automaticamente ao layout. Você pode visitar este endpoint em sua aplicação para ver as definições das funções de proxy do cliente. Este arquivo de script é gerado automaticamente pelo ABP Framework com base nas definições de método do lado do servidor e nos detalhes do endpoint HTTP relacionado.

## Veja Também

* [Proxies de Cliente JavaScript Estáticos](Static-JavaScript-Proxies.md)
* [Controladores de API Automáticos](../../API/Auto-API-Controllers.md)
* [Tutorial de Desenvolvimento de Aplicação Web](../../Tutorials/Part-1.md)