[[_TOC_]]
Fonte: Clean Code - Capítulo 2 (Nomes Significativos)
- https://giovannamoeller.medium.com/clean-code-li%C3%A7%C3%B5es-essenciais-do-segundo-cap%C3%ADtulo-d06c8e27f275
- https://www.linkedin.com/pulse/resumo-cap%C3%ADtulo-2-clean-code-e<PERSON><PERSON>-car<PERSON><PERSON>/?originalSubdomain=pt
- https://medium.com/@aebone/clean-code-nomes-significativos-b0ce80459b81

# Regras Gerais
- Prefira clareza em vez de brevidade.
  - Os nomes das colunas do banco de dados possui restrições de tamanho, porém, ao configurar o mapeamento, deve-se criar os nomes das propriedades com nome em extenso.
    - A mesma regra vale para propriedades de classes que não serão entidades.
- **Para os nomes dos IDs das entidades**, adotar o padrão "NomeDaEntidade"+"Id".


# Criar nomes significativos
- **Use nomes que revelem seu propósito**: O nome de uma variável, método ou classe deve dizer porque existe, o que faz e como é utilizado. Se um nome requer um comentário (assunto exclusivo do capítulo 4) é porque não revela seu propósito. Segue um exemplo:
![image.png](/.attachments/image-88e1897e-262e-4b4e-b08c-114260de5314.png =300x)
O nome "d" não significa nada, mas elapsedTimeInDays demonstra para que serve e também a unidade envolvida.

- **Use verbos para nomear métodos e substantivos para classes e objetos**: Exemplos de classes e objetos: Cliente, PaginaWiki, Conta e Análise Endereço. Exemplos de métodos: postarPagamento, excluirPagina e salvar. Ainda sobre as funções, é importante adicionar os prefixos get, set ou is de acordo com a necessidade.
![image.png](/.attachments/image-54b557e0-73dd-4564-bb69-e012bb4b3724.png =300x)

- **Use nomes pronunciáveis**: Para facilitar a comunicação entre a equipe de desenvolvimento sempre opte por nomes pronunciáveis. Como mostra o exemplo abaixo, o que é mais fácil de falar: "O compilador está acusando um erro na genymdhms." ou "O compilador está acusando um erro na generationTimestamp."
![image.png](/.attachments/image-dcd87f0d-95d0-4b19-8ef5-340c93f02f3f.png =300x)
<SPAN style="color: rgba(0, 0, 0, 0.6);font-family: -apple-system, system-ui, BlinkMacSystemFont, &amp;quot;font-size: 14px;font-style: normal;font-weight: 400;letter-spacing: normal;text-align: center;text-indent: 0px;text-transform: none;word-spacing: 0px;white-space: normal;background-color: rgb(255, 255, 255);float: none">OBS: genymdhms significa generation date, year, mounth, day, hour, minute e second</SPAN>

- **Use nomes passíveis de busca (e de brinde evite a utilização de números mágicos)**: Observe o exemplo abaixo. Durante uma modificação é mais fácil buscar por "WORK_DAYS_PER_WEEK" do que pesquisar todos os números "5" e determinar quais devem ser alterados. Esse número 4 e 5 "soltos" no primeiro for são conhecidos como números mágicos e devem ser evitados no objetivo de tornar um código limpo. Mais um exemplo de um nome não viável seria "e", já imaginou quantas letras e apareceriam durante uma pequisa por essa variável especificamente?
![image.png](/.attachments/image-9f0b0619-47b1-447a-bf9a-ba367f011d08.png =300x)


- **Evite nomes muito parecidos**: É difícil perceber a pequena diferença entre nomes parecidos, como mostra o exemplo abaixo:
![image.png](/.attachments/image-aaced9c1-b732-4d69-9447-20975467184e.png =300x)
Para evitar que o desenvolvedor chame updTime no lugar de upTime é necessário uma explicitação da diferença, como sugere a nova nomeação do exemplo.

# Evite Abreviações e Siglas
- **Evite usar abreviações e siglas**: Nomes abreviados ou siglas podem ser confusos e difíceis de entender. Prefira sempre nomes completos e descritivos. Por exemplo, ao invés de usar "calc" para uma variável que representa um cálculo, use "calculo".

